-- Add composite indexes for efficient scheduler queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_schedulers_active_next_run_expires
ON schedulers (is_active, next_run_at, expires_at)
WHERE is_active = true;

-- Add index for processing status
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_schedulers_processing_status
ON schedulers USING GIN (scheduler_metadata)
WHERE scheduler_metadata ? 'processing';

-- Add partial index for active schedulers only
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_schedulers_active_frequency
ON schedulers (frequency, next_run_at)
WHERE is_active = true;

-- Optimize execution history queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scheduler_executions_recent
ON scheduler_executions (scheduler_id, execution_time DESC, status);

-- Add index for cleanup operations
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scheduler_executions_cleanup
ON scheduler_executions (execution_time)
WHERE status IN ('completed', 'failed');