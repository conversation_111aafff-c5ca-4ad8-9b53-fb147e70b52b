# Multi-stage Docker build for the Trigger Service

# Build stage
FROM python:3.11-slim as builder

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Create and activate virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --upgrade pip && \
    pip install -r requirements.txt

# Production stage
FROM python:3.11-slim as production

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PATH="/opt/venv/bin:$PATH" \
    PRODUCTION_MODE=true \
    ENABLE_DEBUG_LOGGING=false \
    LOG_SQL_QUERIES=false \
    ENABLE_CONCURRENT_PROCESSING=true \
    ENABLE_TASK_QUEUE=true \
    ENABLE_DISTRIBUTED_LOCKING=true \
    SCHEDULER_BATCH_SIZE=50 \
    SCHEDULER_CONCURRENCY=10 \
    TASK_WORKER_CONCURRENCY=5 \
    MAX_CONCURRENT_SCHEDULERS=100 \
    SEMAPHORE_LIMIT=10

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    libpq5 \
    curl \
    redis-tools \
    && rm -rf /var/lib/apt/lists/*

# Copy virtual environment from builder stage
COPY --from=builder /opt/venv /opt/venv

# Create non-root user
RUN groupadd -r trigger && useradd -r -g trigger trigger

# Create application directory
WORKDIR /app

# Copy application code
COPY src/ ./src/
COPY alembic.ini ./
COPY .env.example ./
COPY migrations/ ./migrations/
COPY scripts/ ./scripts/
COPY deployment/ ./deployment/
COPY requirements.txt ./
COPY REDIS_SETUP_GUIDE.md ./
COPY DEPLOYMENT_GUIDE.md ./
COPY PRODUCTION_SCALABILITY_IMPLEMENTATION_GUIDE.md ./

# Change ownership to non-root user
RUN chown -R trigger:trigger /app

# Switch to non-root user
USER trigger

# Health check - check both API and Redis connectivity
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/api/v1/health || exit 1

# Expose port
EXPOSE 8000

# Default command
CMD ["python", "-m", "src.main"]
