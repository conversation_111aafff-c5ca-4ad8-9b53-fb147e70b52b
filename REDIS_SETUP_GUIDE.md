# Redis Setup Guide

The trigger scheduler service can run in two modes:

## 1. Full Scalable Mode (Recommended for Production)

Requires Redis for task queuing and distributed locking.

### Install Redis

#### macOS (using Homebrew)

```bash
brew install redis
brew services start redis
```

#### Ubuntu/Debian

```bash
sudo apt update
sudo apt install redis-server
sudo systemctl start redis
sudo systemctl enable redis
```

#### CentOS/RHEL

```bash
sudo dnf install redis
sudo systemctl start redis
sudo systemctl enable redis
```

#### Docker

```bash
docker run -d --name redis -p 6379:6379 redis:latest
```

### Verify Redis Installation

```bash
redis-cli ping
# Should return: PONG
```

## 2. Database-Only Mode (Fallback)

If Redis is not available, the system automatically falls back to:

- Database-only distributed locking
- Direct workflow execution (no background task queue)
- Reduced concurrency but still functional

### When Fallback Mode is Used

- Redis server is not running
- Redis connection fails
- Network issues with Redis

### Fallback Mode Limitations

- No background task processing
- Reduced scalability for high-load scenarios
- Workflows execute synchronously during scheduler cycles

## Configuration

### Environment Variables

```bash
# Redis URL (optional - defaults to redis://localhost:6379)
REDIS_URL=redis://localhost:6379

# For Redis with authentication
REDIS_URL=redis://username:password@localhost:6379

# For Redis Cluster
REDIS_URL=redis://localhost:6379,localhost:6380,localhost:6381
```

### Checking Current Mode

The service logs will indicate which mode is active:

**Full Scalable Mode:**

```
[INFO] Redis is available, initializing full scalable mode
[INFO] SchedulerService started with 5 workers processing workflow_execution queue
[INFO] Starting scheduler engine run with task queue
```

**Fallback Mode:**

```
[WARNING] Redis is not available, falling back to database-only mode
[INFO] SchedulerService started in database-only mode (no Redis task queue)
[INFO] Starting scheduler engine run with direct execution (fallback)
```

## Performance Comparison

| Feature               | Full Scalable Mode    | Fallback Mode                   |
| --------------------- | --------------------- | ------------------------------- |
| Concurrent Processing | ✅ High               | ⚠️ Limited                      |
| Background Tasks      | ✅ Yes                | ❌ No                           |
| Horizontal Scaling    | ✅ Yes                | ❌ No                           |
| Redis Dependency      | ✅ Required           | ✅ Not Required                 |
| Database Load         | ✅ Lower              | ⚠️ Higher                       |
| Suitable For          | Production, High Load | Development, Simple Deployments |

## Troubleshooting

### Redis Connection Issues

```bash
# Check if Redis is running
sudo systemctl status redis

# Check Redis logs
sudo journalctl -u redis -f

# Test connection
redis-cli ping

# Check Redis configuration
redis-cli config get "*"
```

### Common Redis Errors

- **Connection refused**: Redis is not running
- **Authentication failed**: Check Redis password configuration
- **Timeout**: Network or firewall issues

### Switching Between Modes

The service automatically detects Redis availability on startup. To switch modes:

1. **Enable Full Mode**: Start Redis service
2. **Enable Fallback Mode**: Stop Redis service

Restart the scheduler service after changing Redis availability.

## Recommendations

### Development

- Use fallback mode for simple development setups
- No additional dependencies required

### Production

- Always use full scalable mode with Redis
- Configure Redis persistence and clustering for high availability
- Monitor Redis memory usage and performance
- Use Redis authentication and network security

### High Availability

- Use Redis Cluster or Redis Sentinel
- Configure multiple Redis instances
- Implement Redis backup and recovery procedures
