# Production Scalability Implementation Guide

## Scheduler System Optimization for 100+ Triggers & Schedulers

### Executive Summary

This document provides a comprehensive implementation plan to transform the current scheduler system from a development prototype to a production-ready, scalable solution capable of handling 100+ triggers and schedulers concurrently.

### Current State Analysis

- **Sequential Processing**: Schedulers execute one-by-one, causing massive delays
- **Database Lock Contention**: Long-running transactions block concurrent operations
- **No Concurrency Control**: Race conditions and duplicate executions possible
- **Resource Exhaustion**: Unbounded memory usage and connection leaks
- **Missing Monitoring**: No observability into system performance

---

## Phase 1: Critical Infrastructure Changes (Priority: URGENT)

### 1.1 Implement Concurrent Scheduler Processing

**File**: `src/core/scheduler_engine.py`

**Current Problem**:

```python
# Sequential processing - BLOCKING
for scheduler in due_schedulers:
    await self._execute_scheduled_workflow(scheduler)
```

**Implementation**:

```python
import asyncio
from asyncio import Semaphore
from typing import List, Optional
import time

class SchedulerEngine:
    def __init__(self, db_session: AsyncSession, workflow_executor: WorkflowExecutor, max_concurrent: int = 10):
        self.db_session = db_session
        self.workflow_executor = workflow_executor
        self.max_concurrent = max_concurrent
        self.semaphore = Semaphore(max_concurrent)
        self.metrics = SchedulerMetrics()

    async def process_due_schedulers(self):
        """Process schedulers concurrently with controlled parallelism."""
        start_time = time.time()
        logger.info("Starting concurrent scheduler engine run")

        try:
            scheduler_manager = SchedulerManager(self.db_session)
            due_schedulers = await scheduler_manager.get_due_schedulers()

            if not due_schedulers:
                logger.info("No schedulers currently due for execution")
                return

            logger.info(f"Found {len(due_schedulers)} schedulers due for execution")

            # Process schedulers concurrently with semaphore control
            tasks = [
                self._execute_with_semaphore(scheduler)
                for scheduler in due_schedulers
            ]

            # Execute all tasks concurrently, collecting results
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results and log metrics
            successful = sum(1 for r in results if not isinstance(r, Exception))
            failed = len(results) - successful

            execution_time = time.time() - start_time

            logger.info(
                f"Scheduler engine run completed: {successful} successful, {failed} failed, "
                f"execution_time={execution_time:.2f}s"
            )

            # Update metrics
            self.metrics.record_batch_execution(
                total=len(due_schedulers),
                successful=successful,
                failed=failed,
                execution_time=execution_time
            )

        except Exception as e:
            logger.error(f"Error in process_due_schedulers: {e}", exc_info=True)
            self.metrics.record_engine_error(str(e))

    async def _execute_with_semaphore(self, scheduler: SimplifiedSchedulerResponse):
        """Execute scheduler with semaphore control to limit concurrency."""
        async with self.semaphore:
            try:
                return await self._execute_scheduled_workflow(scheduler)
            except Exception as e:
                logger.error(f"Failed to execute scheduler {scheduler.id}: {e}", exc_info=True)
                return e
```

**Tasks**:

- [ ] Add `max_concurrent` parameter to SchedulerEngine constructor
- [ ] Implement semaphore-based concurrency control
- [ ] Add comprehensive error handling for concurrent execution
- [ ] Implement metrics collection for batch operations
- [ ] Add configuration for concurrency limits

### 1.2 Implement Distributed Locking

**File**: `src/core/distributed_lock.py` (NEW)

**Implementation**:

```python
import asyncio
import time
from typing import Optional
from abc import ABC, abstractmethod
import redis.asyncio as redis
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

class DistributedLock(ABC):
    """Abstract base class for distributed locking mechanisms."""

    @abstractmethod
    async def acquire(self, key: str, ttl: int = 300) -> bool:
        """Acquire a distributed lock."""
        pass

    @abstractmethod
    async def release(self, key: str) -> bool:
        """Release a distributed lock."""
        pass

class RedisDistributedLock(DistributedLock):
    """Redis-based distributed locking implementation."""

    def __init__(self, redis_url: str):
        self.redis_client = redis.from_url(redis_url)
        self.lock_prefix = "scheduler_lock:"

    async def acquire(self, key: str, ttl: int = 300) -> bool:
        """Acquire lock using Redis SET with NX and EX options."""
        lock_key = f"{self.lock_prefix}{key}"
        result = await self.redis_client.set(
            lock_key,
            f"{time.time()}:{asyncio.current_task().get_name()}",
            nx=True,
            ex=ttl
        )
        return result is not None

    async def release(self, key: str) -> bool:
        """Release lock by deleting the Redis key."""
        lock_key = f"{self.lock_prefix}{key}"
        result = await self.redis_client.delete(lock_key)
        return result > 0

class DatabaseDistributedLock(DistributedLock):
    """Database-based distributed locking using advisory locks."""

    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session

    async def acquire(self, key: str, ttl: int = 300) -> bool:
        """Acquire PostgreSQL advisory lock."""
        lock_id = hash(key) % (2**31)  # Convert string to int for pg_advisory_lock
        try:
            result = await self.db_session.execute(
                text("SELECT pg_try_advisory_lock(:lock_id)"),
                {"lock_id": lock_id}
            )
            return result.scalar()
        except Exception as e:
            logger.error(f"Failed to acquire database lock for {key}: {e}")
            return False

    async def release(self, key: str) -> bool:
        """Release PostgreSQL advisory lock."""
        lock_id = hash(key) % (2**31)
        try:
            result = await self.db_session.execute(
                text("SELECT pg_advisory_unlock(:lock_id)"),
                {"lock_id": lock_id}
            )
            return result.scalar()
        except Exception as e:
            logger.error(f"Failed to release database lock for {key}: {e}")
            return False

class LockManager:
    """High-level lock manager with fallback mechanisms."""

    def __init__(self, primary_lock: DistributedLock, fallback_lock: Optional[DistributedLock] = None):
        self.primary_lock = primary_lock
        self.fallback_lock = fallback_lock

    async def acquire_scheduler_lock(self, scheduler_id: str, ttl: int = 300) -> bool:
        """Acquire lock for scheduler execution."""
        lock_key = f"scheduler:{scheduler_id}"

        # Try primary lock first
        if await self.primary_lock.acquire(lock_key, ttl):
            return True

        # Fallback to secondary lock if available
        if self.fallback_lock:
            return await self.fallback_lock.acquire(lock_key, ttl)

        return False

    async def release_scheduler_lock(self, scheduler_id: str) -> bool:
        """Release lock for scheduler execution."""
        lock_key = f"scheduler:{scheduler_id}"

        # Try to release from both locks
        primary_released = await self.primary_lock.release(lock_key)
        fallback_released = True

        if self.fallback_lock:
            fallback_released = await self.fallback_lock.release(lock_key)

        return primary_released or fallback_released
```

**Tasks**:

- [ ] Create distributed lock interface and implementations
- [ ] Add Redis-based locking with fallback to database locks
- [ ] Integrate lock manager into scheduler engine
- [ ] Add lock timeout and cleanup mechanisms
- [ ] Implement lock monitoring and metrics

### 1.3 Database Query Optimization

**File**: `src/core/scheduler_manager.py`

**Current Problem**:

```python
# Inefficient query without proper indexing
stmt = select(Scheduler).where(
    Scheduler.is_active == True, Scheduler.next_run_at <= now_utc
)
```

**Implementation**:

```python
from sqlalchemy import select, and_, or_
from sqlalchemy.orm import selectinload

class SchedulerManager:
    async def get_due_schedulers(self, limit: int = 100, offset: int = 0) -> List[SimplifiedSchedulerResponse]:
        """
        Retrieves schedulers that are active and due for execution with optimizations.
        """
        now_utc = datetime.now(timezone.utc)

        # Optimized query with proper indexing and pagination
        stmt = (
            select(Scheduler)
            .where(
                and_(
                    Scheduler.is_active == True,
                    Scheduler.next_run_at <= now_utc,
                    or_(
                        Scheduler.expires_at.is_(None),
                        Scheduler.expires_at > now_utc
                    )
                )
            )
            .order_by(Scheduler.next_run_at.asc())  # Process oldest first
            .limit(limit)
            .offset(offset)
            # Eager load relationships to avoid N+1 queries
            .options(selectinload(Scheduler.executions))
        )

        result = await self.db.execute(stmt)
        due_schedulers = result.scalars().all()

        # Convert to Pydantic models within the async session context
        return [SimplifiedSchedulerResponse.model_validate(s) for s in due_schedulers]

    async def get_due_schedulers_batch(self, batch_size: int = 50) -> AsyncGenerator[List[SimplifiedSchedulerResponse], None]:
        """
        Generator that yields batches of due schedulers for memory-efficient processing.
        """
        offset = 0
        while True:
            batch = await self.get_due_schedulers(limit=batch_size, offset=offset)
            if not batch:
                break
            yield batch
            offset += batch_size

    async def mark_scheduler_processing(self, scheduler_id: str) -> bool:
        """
        Atomically mark a scheduler as being processed to prevent duplicate execution.
        """
        now_utc = datetime.now(timezone.utc)

        # Use UPDATE with WHERE to atomically check and update
        stmt = text("""
            UPDATE schedulers
            SET
                last_run_at = :now,
                scheduler_metadata = COALESCE(scheduler_metadata, '{}') || '{"processing": true}'::jsonb
            WHERE
                id = :scheduler_id
                AND is_active = true
                AND next_run_at <= :now
                AND (scheduler_metadata->>'processing' IS NULL OR scheduler_metadata->>'processing' = 'false')
            RETURNING id
        """)

        result = await self.db.execute(stmt, {
            "scheduler_id": scheduler_id,
            "now": now_utc
        })

        return result.rowcount > 0
```

**Database Migration** (`migrations/add_scheduler_indexes.sql`):

```sql
-- Add composite indexes for efficient scheduler queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_schedulers_active_next_run_expires
ON schedulers (is_active, next_run_at, expires_at)
WHERE is_active = true;

-- Add index for processing status
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_schedulers_processing_status
ON schedulers USING GIN (scheduler_metadata)
WHERE scheduler_metadata ? 'processing';

-- Add partial index for active schedulers only
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_schedulers_active_frequency
ON schedulers (frequency, next_run_at)
WHERE is_active = true;

-- Optimize execution history queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scheduler_executions_recent
ON scheduler_executions (scheduler_id, execution_time DESC, status);

-- Add index for cleanup operations
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scheduler_executions_cleanup
ON scheduler_executions (execution_time)
WHERE status IN ('completed', 'failed');
```

**Tasks**:

- [ ] Add composite database indexes for efficient queries
- [ ] Implement pagination for large result sets
- [ ] Add atomic scheduler marking to prevent race conditions
- [ ] Implement batch processing for memory efficiency
- [ ] Add query performance monitoring

---

## Phase 2: Background Task Queue Implementation (Priority: HIGH)

### 2.1 Task Queue Infrastructure

**File**: `src/core/task_queue.py` (NEW)

**Implementation**:

```python
import asyncio
import json
from typing import Dict, Any, Optional, Callable, List
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
import redis.asyncio as redis
from datetime import datetime, timedelta

class TaskStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"

@dataclass
class Task:
    id: str
    queue_name: str
    task_type: str
    payload: Dict[str, Any]
    status: TaskStatus
    created_at: datetime
    scheduled_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    retry_count: int = 0
    max_retries: int = 3
    error_message: Optional[str] = None

class TaskQueue(ABC):
    """Abstract base class for task queue implementations."""

    @abstractmethod
    async def enqueue(self, task: Task) -> str:
        """Enqueue a task for processing."""
        pass

    @abstractmethod
    async def dequeue(self, queue_name: str, timeout: int = 30) -> Optional[Task]:
        """Dequeue a task from the specified queue."""
        pass

    @abstractmethod
    async def complete_task(self, task_id: str, result: Dict[str, Any]) -> bool:
        """Mark a task as completed."""
        pass

    @abstractmethod
    async def fail_task(self, task_id: str, error: str, retry: bool = True) -> bool:
        """Mark a task as failed."""
        pass

class RedisTaskQueue(TaskQueue):
    """Redis-based task queue implementation."""

    def __init__(self, redis_url: str):
        self.redis_client = redis.from_url(redis_url)
        self.task_prefix = "task:"
        self.queue_prefix = "queue:"
        self.processing_prefix = "processing:"

    async def enqueue(self, task: Task) -> str:
        """Enqueue task to Redis list."""
        task_key = f"{self.task_prefix}{task.id}"
        queue_key = f"{self.queue_prefix}{task.queue_name}"

        # Store task data
        task_data = {
            "id": task.id,
            "queue_name": task.queue_name,
            "task_type": task.task_type,
            "payload": json.dumps(task.payload),
            "status": task.status.value,
            "created_at": task.created_at.isoformat(),
            "scheduled_at": task.scheduled_at.isoformat() if task.scheduled_at else None,
            "retry_count": task.retry_count,
            "max_retries": task.max_retries
        }

        await self.redis_client.hset(task_key, mapping=task_data)
        await self.redis_client.expire(task_key, 86400)  # 24 hour TTL

        # Add to queue
        if task.scheduled_at and task.scheduled_at > datetime.utcnow():
            # Delayed task - add to sorted set with timestamp score
            await self.redis_client.zadd(
                f"{queue_key}:delayed",
                {task.id: task.scheduled_at.timestamp()}
            )
        else:
            # Immediate task - add to list
            await self.redis_client.lpush(queue_key, task.id)

        return task.id

    async def dequeue(self, queue_name: str, timeout: int = 30) -> Optional[Task]:
        """Dequeue task with delayed task processing."""
        queue_key = f"{self.queue_prefix}{queue_name}"
        delayed_key = f"{queue_key}:delayed"
        processing_key = f"{self.processing_prefix}{queue_name}"

        # First, move any ready delayed tasks to the main queue
        now = datetime.utcnow().timestamp()
        ready_tasks = await self.redis_client.zrangebyscore(
            delayed_key, 0, now, withscores=False
        )

        if ready_tasks:
            # Move ready tasks to main queue
            pipe = self.redis_client.pipeline()
            for task_id in ready_tasks:
                pipe.lpush(queue_key, task_id)
                pipe.zrem(delayed_key, task_id)
            await pipe.execute()

        # Dequeue from main queue with timeout
        result = await self.redis_client.brpoplpush(
            queue_key, processing_key, timeout
        )

        if not result:
            return None

        task_id = result.decode('utf-8')
        task_key = f"{self.task_prefix}{task_id}"

        # Load task data
        task_data = await self.redis_client.hgetall(task_key)
        if not task_data:
            return None

        # Convert to Task object
        task = Task(
            id=task_data[b'id'].decode('utf-8'),
            queue_name=task_data[b'queue_name'].decode('utf-8'),
            task_type=task_data[b'task_type'].decode('utf-8'),
            payload=json.loads(task_data[b'payload'].decode('utf-8')),
            status=TaskStatus(task_data[b'status'].decode('utf-8')),
            created_at=datetime.fromisoformat(task_data[b'created_at'].decode('utf-8')),
            retry_count=int(task_data[b'retry_count']),
            max_retries=int(task_data[b'max_retries'])
        )

        # Update task status
        task.status = TaskStatus.RUNNING
        task.started_at = datetime.utcnow()
        await self._update_task(task)

        return task

    async def complete_task(self, task_id: str, result: Dict[str, Any]) -> bool:
        """Mark task as completed and remove from processing."""
        task_key = f"{self.task_prefix}{task_id}"

        # Update task status
        await self.redis_client.hset(task_key, mapping={
            "status": TaskStatus.COMPLETED.value,
            "completed_at": datetime.utcnow().isoformat(),
            "result": json.dumps(result)
        })

        # Remove from all processing queues
        await self._remove_from_processing(task_id)
        return True

    async def fail_task(self, task_id: str, error: str, retry: bool = True) -> bool:
        """Mark task as failed and handle retries."""
        task_key = f"{self.task_prefix}{task_id}"
        task_data = await self.redis_client.hgetall(task_key)

        if not task_data:
            return False

        retry_count = int(task_data[b'retry_count'])
        max_retries = int(task_data[b'max_retries'])

        if retry and retry_count < max_retries:
            # Retry the task
            retry_count += 1
            delay = min(300, 2 ** retry_count)  # Exponential backoff, max 5 minutes
            scheduled_at = datetime.utcnow() + timedelta(seconds=delay)

            await self.redis_client.hset(task_key, mapping={
                "status": TaskStatus.RETRYING.value,
                "retry_count": retry_count,
                "error_message": error,
                "scheduled_at": scheduled_at.isoformat()
            })


        if retry and retry_count < max_retries:
            # Retry the task
            retry_count += 1
            delay = min(300, 2 ** retry_count)  # Exponential backoff, max 5 minutes
            scheduled_at = datetime.utcnow() + timedelta(seconds=delay)

            await self.redis_client.hset(task_key, mapping={
                "status": TaskStatus.RETRYING.value,
                "retry_count": retry_count,
                "error_message": error,
                "scheduled_at": scheduled_at.isoformat()
            })

            # Re-queue as delayed task
            queue_name = task_data[b'queue_name'].decode('utf-8')
            delayed_key = f"{self.queue_prefix}{queue_name}:delayed"
            await self.redis_client.zadd(delayed_key, {task_id: scheduled_at.timestamp()})
        else:
            # Final failure
            await self.redis_client.hset(task_key, mapping={
                "status": TaskStatus.FAILED.value,
                "completed_at": datetime.utcnow().isoformat(),
                "error_message": error
            })

        # Remove from processing
        await self._remove_from_processing(task_id)
        return True

    async def _update_task(self, task: Task):
        """Update task data in Redis."""
        task_key = f"{self.task_prefix}{task.id}"
        update_data = {
            "status": task.status.value,
            "retry_count": task.retry_count
        }

        if task.started_at:
            update_data["started_at"] = task.started_at.isoformat()
        if task.completed_at:
            update_data["completed_at"] = task.completed_at.isoformat()

        await self.redis_client.hset(task_key, mapping=update_data)

    async def _remove_from_processing(self, task_id: str):
        """Remove task from all processing queues."""
        # This is a simplified version - in production, you'd track which queue the task came from
        processing_keys = await self.redis_client.keys(f"{self.processing_prefix}*")
        for key in processing_keys:
            await self.redis_client.lrem(key, 0, task_id)

class TaskWorker:
    """Generic task worker that processes tasks from queues."""

    def __init__(self, task_queue: TaskQueue, handlers: Dict[str, Callable]):
        self.task_queue = task_queue
        self.handlers = handlers
        self.running = False

    async def start(self, queue_names: List[str], concurrency: int = 5):
        """Start processing tasks from specified queues."""
        self.running = True

        # Create worker coroutines
        workers = []
        for i in range(concurrency):
            for queue_name in queue_names:
                workers.append(self._worker(queue_name, f"worker-{i}-{queue_name}"))

        # Run all workers concurrently
        await asyncio.gather(*workers)

    async def stop(self):
        """Stop processing tasks."""
        self.running = False

    async def _worker(self, queue_name: str, worker_id: str):
        """Individual worker coroutine."""
        logger.info(f"Worker {worker_id} started for queue {queue_name}")

        while self.running:
            try:
                # Dequeue task with timeout
                task = await self.task_queue.dequeue(queue_name, timeout=5)

                if not task:
                    continue

                logger.info(f"Worker {worker_id} processing task {task.id} of type {task.task_type}")

                # Find and execute handler
                handler = self.handlers.get(task.task_type)
                if not handler:
                    await self.task_queue.fail_task(
                        task.id,
                        f"No handler found for task type: {task.task_type}",
                        retry=False
                    )
                    continue

                try:
                    # Execute task handler
                    result = await handler(task.payload)
                    await self.task_queue.complete_task(task.id, result or {})
                    logger.info(f"Worker {worker_id} completed task {task.id}")

                except Exception as e:
                    logger.error(f"Worker {worker_id} failed to process task {task.id}: {e}", exc_info=True)
                    await self.task_queue.fail_task(task.id, str(e))

            except Exception as e:
                logger.error(f"Worker {worker_id} encountered error: {e}", exc_info=True)
                await asyncio.sleep(1)  # Brief pause before retrying

        logger.info(f"Worker {worker_id} stopped")
```

**Tasks**:

- [ ] Implement Redis-based task queue with delayed execution
- [ ] Create task worker framework with configurable concurrency
- [ ] Add task retry logic with exponential backoff
- [ ] Implement task status tracking and monitoring
- [ ] Add task queue health checks and metrics

### 2.2 Modified Scheduler Engine with Task Queue

**File**: `src/core/scheduler_engine.py` (MODIFIED)

**Implementation**:

```python
from src.core.task_queue import Task, TaskStatus, RedisTaskQueue
from uuid import uuid4

class SchedulerEngine:
    def __init__(self, db_session: AsyncSession, task_queue: RedisTaskQueue, lock_manager: LockManager):
        self.db_session = db_session
        self.task_queue = task_queue
        self.lock_manager = lock_manager
        self.metrics = SchedulerMetrics()

    async def process_due_schedulers(self):
        """Process schedulers by queuing workflow execution tasks."""
        start_time = time.time()
        logger.info("Starting scheduler engine run with task queue")

        try:
            scheduler_manager = SchedulerManager(self.db_session)

            # Process schedulers in batches to avoid memory issues
            total_processed = 0
            total_queued = 0

            async for batch in scheduler_manager.get_due_schedulers_batch(batch_size=50):
                batch_queued = 0

                for scheduler in batch:
                    # Try to acquire lock for this scheduler
                    if not await self.lock_manager.acquire_scheduler_lock(scheduler.id, ttl=300):
                        logger.debug(f"Scheduler {scheduler.id} is already being processed, skipping")
                        continue

                    try:
                        # Create execution record
                        execution_time = datetime.now(timezone.utc)
                        scheduler_execution = SchedulerExecution(
                            scheduler_id=scheduler.id,
                            execution_time=execution_time,
                            status="pending"
                        )
                        self.db_session.add(scheduler_execution)
                        await self.db_session.commit()
                        await self.db_session.refresh(scheduler_execution)

                        # Create workflow execution task
                        task = Task(
                            id=str(uuid4()),
                            queue_name="workflow_execution",
                            task_type="scheduler_workflow",
                            payload={
                                "scheduler_id": scheduler.id,
                                "execution_id": str(scheduler_execution.id),
                                "user_id": scheduler.user_id,
                                "workflow_id": scheduler.workflow_id,
                                "event_data": {
                                    "event_type": "scheduler",
                                    "event_id": f"scheduler_{scheduler.id}",
                                    "timestamp": execution_time.isoformat(),
                                    "source": "scheduler_engine",
                                    "data": {
                                        "scheduler_id": scheduler.id,
                                        "scheduler_name": scheduler.name,
                                        "frequency": scheduler.frequency.value,
                                        "execution_id": str(scheduler_execution.id),
                                    },
                                }
                            },
                            status=TaskStatus.PENDING,
                            created_at=datetime.utcnow(),
                            max_retries=3
                        )

                        # Queue the task
                        await self.task_queue.enqueue(task)
                        batch_queued += 1

                        # Update scheduler's next run time
                        await self._update_scheduler_next_run(scheduler)

                        logger.info(f"Queued workflow execution task for scheduler {scheduler.id}")

                    except Exception as e:
                        logger.error(f"Failed to queue task for scheduler {scheduler.id}: {e}", exc_info=True)
                        # Update execution record with failure
                        scheduler_execution.status = "failed"
                        scheduler_execution.error_message = str(e)
                        await self.db_session.commit()

                    finally:
                        # Always release the lock
                        await self.lock_manager.release_scheduler_lock(scheduler.id)

                total_processed += len(batch)
                total_queued += batch_queued

                logger.info(f"Processed batch: {len(batch)} schedulers, {batch_queued} queued")

            execution_time = time.time() - start_time

            logger.info(
                f"Scheduler engine run completed: {total_processed} processed, {total_queued} queued, "
                f"execution_time={execution_time:.2f}s"
            )

            # Update metrics
            self.metrics.record_batch_execution(
                total=total_processed,
                successful=total_queued,
                failed=total_processed - total_queued,
                execution_time=execution_time
            )

        except Exception as e:
            logger.error(f"Error in process_due_schedulers: {e}", exc_info=True)
            self.metrics.record_engine_error(str(e))

    async def _update_scheduler_next_run(self, scheduler: SimplifiedSchedulerResponse):
        """Update scheduler's next run time after queuing execution."""
        try:
            # Get the actual database object to update it
            from sqlalchemy import select

            stmt = select(Scheduler).where(Scheduler.id == scheduler.id)
            result = await self.db_session.execute(stmt)
            db_scheduler = result.scalars().first()

            if not db_scheduler:
                raise ValueError(f"Scheduler {scheduler.id} not found in database")

            # Update scheduler's last_run_at and next_run_at
            current_time = datetime.now(timezone.utc)
            db_scheduler.last_run_at = current_time

            # Update the scheduler object with the new last_run_at for next run calculation
            updated_scheduler = scheduler.model_copy(
                update={"last_run_at": current_time}
            )

            # Calculate next run time using simplified parser
            next_run_time = ScheduleParser.get_next_run_time(
                updated_scheduler, current_time
            )

            # Update next_run_at
            db_scheduler.next_run_at = next_run_time
            logger.debug(f"Scheduler ID: {scheduler.id}, Next run at: {db_scheduler.next_run_at}")

            self.db_session.add(db_scheduler)
            await self.db_session.commit()

        except Exception as e:
            logger.error(f"Failed to update scheduler {scheduler.id} next run time: {e}", exc_info=True)
```

# Additional Critical Enhancements for Production Scheduler

## Phase 3: Advanced Optimizations (Priority: MEDIUM-HIGH)

### 3.1 Connection Pool Management

**File**: `src/core/connection_manager.py` (NEW)

```python
import asyncio
from contextlib import asynccontextmanager
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.pool import QueuePool
import redis.asyncio as redis
from typing import AsyncGenerator

class ConnectionManager:
    """Centralized connection management with pooling."""

    def __init__(self, database_url: str, redis_url: str):
        # Database connection pool
        self.engine = create_async_engine(
            database_url,
            poolclass=QueuePool,
            pool_size=20,  # Base pool size
            max_overflow=30,  # Additional connections under load
            pool_timeout=30,  # Max wait time for connection
            pool_recycle=3600,  # Recycle connections every hour
            pool_pre_ping=True,  # Validate connections before use
            echo=False
        )

        self.async_session_factory = async_sessionmaker(
            self.engine,
            class_=AsyncSession,
            expire_on_commit=False
        )

        # Redis connection pool
        self.redis_pool = redis.ConnectionPool.from_url(
            redis_url,
            max_connections=50,
            retry_on_timeout=True,
            socket_connect_timeout=5,
            socket_timeout=5
        )

    @asynccontextmanager
    async def get_db_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get database session with automatic cleanup."""
        async with self.async_session_factory() as session:
            try:
                yield session
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()

    async def get_redis_client(self) -> redis.Redis:
        """Get Redis client from pool."""
        return redis.Redis(connection_pool=self.redis_pool)

    async def health_check(self) -> dict:
        """Check health of all connections."""
        health = {"database": False, "redis": False}

        try:
            async with self.get_db_session() as session:
                await session.execute("SELECT 1")
                health["database"] = True
        except Exception as e:
            health["database_error"] = str(e)

        try:
            redis_client = await self.get_redis_client()
            await redis_client.ping()
            health["redis"] = True
        except Exception as e:
            health["redis_error"] = str(e)

        return health

    async def close(self):
        """Clean shutdown of all connections."""
        await self.engine.dispose()
        await self.redis_pool.disconnect()
```

### 3.2 Advanced Monitoring and Alerting

**File**: `src/core/monitoring.py` (NEW)

```python
import time
import asyncio
from dataclasses import dataclass, field
from typing import Dict, List, Optional
from datetime import datetime, timedelta
from collections import defaultdict, deque
import logging

@dataclass
class SchedulerMetrics:
    """Comprehensive metrics collection for scheduler system."""

    # Execution metrics
    total_executions: int = 0
    successful_executions: int = 0
    failed_executions: int = 0

    # Performance metrics
    execution_times: deque = field(default_factory=lambda: deque(maxlen=1000))
    queue_sizes: Dict[str, int] = field(default_factory=dict)

    # Error tracking
    error_counts: Dict[str, int] = field(default_factory=lambda: defaultdict(int))
    recent_errors: deque = field(default_factory=lambda: deque(maxlen=100))

    # System health
    last_successful_run: Optional[datetime] = None
    consecutive_failures: int = 0

    def record_execution(self, success: bool, execution_time: float, error: Optional[str] = None):
        """Record individual execution metrics."""
        self.total_executions += 1
        self.execution_times.append(execution_time)

        if success:
            self.successful_executions += 1
            self.consecutive_failures = 0
            self.last_successful_run = datetime.utcnow()
        else:
            self.failed_executions += 1
            self.consecutive_failures += 1
            if error:
                self.error_counts[error] += 1
                self.recent_errors.append({
                    'timestamp': datetime.utcnow(),
                    'error': error
                })

    def get_success_rate(self) -> float:
        """Calculate success rate percentage."""
        if self.total_executions == 0:
            return 0.0
        return (self.successful_executions / self.total_executions) * 100

    def get_avg_execution_time(self) -> float:
        """Calculate average execution time."""
        if not self.execution_times:
            return 0.0
        return sum(self.execution_times) / len(self.execution_times)

    def get_p95_execution_time(self) -> float:
        """Calculate 95th percentile execution time."""
        if not self.execution_times:
            return 0.0
        sorted_times = sorted(self.execution_times)
        index = int(len(sorted_times) * 0.95)
        return sorted_times[index] if index < len(sorted_times) else sorted_times[-1]

class AlertManager:
    """Alert management system for scheduler health."""

    def __init__(self, metrics: SchedulerMetrics):
        self.metrics = metrics
        self.alert_cooldowns: Dict[str, datetime] = {}
        self.alert_thresholds = {
            'high_failure_rate': 20.0,  # 20% failure rate
            'consecutive_failures': 5,
            'high_execution_time': 300.0,  # 5 minutes
            'queue_backlog': 1000,
            'no_recent_success': 1800,  # 30 minutes
        }

    async def check_alerts(self) -> List[Dict]:
        """Check all alert conditions and return active alerts."""
        alerts = []
        now = datetime.utcnow()

        # High failure rate alert
        if self.metrics.get_success_rate() < (100 - self.alert_thresholds['high_failure_rate']):
            alerts.append(self._create_alert(
                'high_failure_rate',
                f"High failure rate: {100 - self.metrics.get_success_rate():.1f}%",
                'critical'
            ))

        # Consecutive failures alert
        if self.metrics.consecutive_failures >= self.alert_thresholds['consecutive_failures']:
            alerts.append(self._create_alert(
                'consecutive_failures',
                f"Consecutive failures: {self.metrics.consecutive_failures}",
                'critical'
            ))

        # High execution time alert
        p95_time = self.metrics.get_p95_execution_time()
        if p95_time > self.alert_thresholds['high_execution_time']:
            alerts.append(self._create_alert(
                'high_execution_time',
                f"High P95 execution time: {p95_time:.1f}s",
                'warning'
            ))

        # Queue backlog alert
        for queue_name, size in self.metrics.queue_sizes.items():
            if size > self.alert_thresholds['queue_backlog']:
                alerts.append(self._create_alert(
                    f'queue_backlog_{queue_name}',
                    f"Queue {queue_name} backlog: {size} tasks",
                    'warning'
                ))

        # No recent success alert
        if (self.metrics.last_successful_run and
            (now - self.metrics.last_successful_run).total_seconds() > self.alert_thresholds['no_recent_success']):
            alerts.append(self._create_alert(
                'no_recent_success',
                f"No successful execution in {(now - self.metrics.last_successful_run).total_seconds():.0f}s",
                'critical'
            ))

        # Filter out alerts in cooldown
        filtered_alerts = []
        for alert in alerts:
            alert_key = alert['type']
            if alert_key not in self.alert_cooldowns or now > self.alert_cooldowns[alert_key]:
                filtered_alerts.append(alert)
                # Set cooldown (5 minutes for warnings, 15 minutes for critical)
                cooldown_minutes = 15 if alert['severity'] == 'critical' else 5
                self.alert_cooldowns[alert_key] = now + timedelta(minutes=cooldown_minutes)

        return filtered_alerts

    def _create_alert(self, alert_type: str, message: str, severity: str) -> Dict:
        """Create alert dictionary."""
        return {
            'type': alert_type,
            'message': message,
            'severity': severity,
            'timestamp': datetime.utcnow(),
            'metric_snapshot': {
                'total_executions': self.metrics.total_executions,
                'success_rate': self.metrics.get_success_rate(),
                'avg_execution_time': self.metrics.get_avg_execution_time(),
                'consecutive_failures': self.metrics.consecutive_failures
            }
        }

class HealthChecker:
    """Comprehensive health checking for scheduler system."""

    def __init__(self, connection_manager: ConnectionManager, task_queue, metrics: SchedulerMetrics):
        self.connection_manager = connection_manager
        self.task_queue = task_queue
        self.metrics = metrics

    async def full_health_check(self) -> Dict:
        """Perform comprehensive system health check."""
        health_status = {
            'timestamp': datetime.utcnow().isoformat(),
            'overall_healthy': True,
            'components': {}
        }

        # Database health
        db_health = await self.connection_manager.health_check()
        health_status['components']['database'] = db_health

        # Task queue health
        queue_health = await self._check_queue_health()
        health_status['components']['task_queue'] = queue_health

        # Metrics health
        metrics_health = self._check_metrics_health()
        health_status['components']['metrics'] = metrics_health

        # System resource health
        resource_health = await self._check_resource_health()
        health_status['components']['resources'] = resource_health

        # Determine overall health
        health_status['overall_healthy'] = all(
            component.get('healthy', False)
            for component in health_status['components'].values()
        )

        return health_status

    async def _check_queue_health(self) -> Dict:
        """Check task queue health."""
        try:
            # Check if we can connect to Redis
            redis_client = await self.connection_manager.get_redis_client()
            await redis_client.ping()

            # Check queue sizes
            queue_sizes = {}
            queue_names = ['workflow_execution', 'high_priority', 'low_priority']

            for queue_name in queue_names:
                queue_key = f"queue:{queue_name}"
                size = await redis_client.llen(queue_key)
                queue_sizes[queue_name] = size

            self.metrics.queue_sizes = queue_sizes

            return {
                'healthy': True,
                'queue_sizes': queue_sizes,
                'total_queued': sum(queue_sizes.values())
            }
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e)
            }

    def _check_metrics_health(self) -> Dict:
        """Check metrics and system performance."""
        now = datetime.utcnow()

        # Check if system is processing tasks
        processing_recently = (
            self.metrics.last_successful_run and
            (now - self.metrics.last_successful_run).total_seconds() < 300  # 5 minutes
        )

        return {
            'healthy': processing_recently or self.metrics.total_executions == 0,
            'success_rate': self.metrics.get_success_rate(),
            'avg_execution_time': self.metrics.get_avg_execution_time(),
            'total_executions': self.metrics.total_executions,
            'consecutive_failures': self.metrics.consecutive_failures,
        }

    async def _check_resource_health(self) -> Dict:
        """Check system resource health."""
        try:
            import psutil

            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)

            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent

            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent

            # Check if resources are within acceptable limits
            healthy = (
                cpu_percent < 80 and
                memory_percent < 85 and
                disk_percent < 90
            )

            return {
                'healthy': healthy,
                'cpu_percent': cpu_percent,
                'memory_percent': memory_percent,
                'disk_percent': disk_percent
            }
        except ImportError:
            return {
                'healthy': True,
                'note': 'psutil not available for resource monitoring'
            }
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e)
            }
```

### 3.3 Memory and Performance Optimizations

**File**: `src/core/performance_optimizer.py` (NEW)

```python
import asyncio
import gc
import weakref
from typing import Dict, Any, Optional, List
from functools import wraps
import cachetools
from datetime import datetime, timedelta

class MemoryManager:
    """Memory management and optimization utilities."""

    def __init__(self):
        self.object_pools: Dict[str, List[Any]] = {}
        self.cache = cachetools.TTLCache(maxsize=1000, ttl=300)  # 5-minute cache

    def get_pooled_object(self, object_type: str, factory_func):
        """Get object from pool or create new one."""
        pool = self.object_pools.get(object_type, [])

        if pool:
            return pool.pop()
        else:
            return factory_func()

    def return_to_pool(self, object_type: str, obj):
        """Return object to pool for reuse."""
        if object_type not in self.object_pools:
            self.object_pools[object_type] = []

        # Reset object state if it has a reset method
        if hasattr(obj, 'reset'):
            obj.reset()

        self.object_pools[object_type].append(obj)

    def clear_pools(self):
        """Clear all object pools."""
        self.object_pools.clear()
        gc.collect()

def memory_optimized(func):
    """Decorator to add memory optimization to functions."""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            result = await func(*args, **kwargs)
            return result
        finally:
            # Force garbage collection after potentially memory-heavy operations
            if hasattr(gc, 'collect'):
                gc.collect()

    return wrapper

class BatchProcessor:
    """Efficient batch processing for large datasets."""

    def __init__(self, batch_size: int = 100, max_concurrent: int = 5):
        self.batch_size = batch_size
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)

    async def process_batches(self, items: List[Any], processor_func):
        """Process items in concurrent batches."""
        batches = [
            items[i:i + self.batch_size]
            for i in range(0, len(items), self.batch_size)
        ]

        async def process_batch(batch):
            async with self.semaphore:
                return await processor_func(batch)

        # Process all batches concurrently
        results = await asyncio.gather(
            *[process_batch(batch) for batch in batches],
            return_exceptions=True
        )

        # Flatten results and handle exceptions
        flattened_results = []
        for result in results:
            if isinstance(result, Exception):
                logging.error(f"Batch processing error: {result}")
            else:
                flattened_results.extend(result if isinstance(result, list) else [result])

        return flattened_results

class SchedulerOptimizer:
    """Performance optimizer for scheduler system."""

    def __init__(self):
        self.memory_manager = MemoryManager()
        self.batch_processor = BatchProcessor()
        self.performance_cache = cachetools.TTLCache(maxsize=500, ttl=300)

    @memory_optimized
    async def optimize_scheduler_processing(self, schedulers: List[Any]) -> List[Any]:
        """Optimize processing of large scheduler lists."""
        # Sort schedulers by priority and execution time for optimal processing order
        optimized_schedulers = sorted(
            schedulers,
            key=lambda s: (
                -getattr(s, 'priority', 0),  # Higher priority first
                getattr(s, 'next_run_at', datetime.max)  # Earlier execution time first
            )
        )

        # Group schedulers by workflow_id to optimize database queries
        workflow_groups = {}
        for scheduler in optimized_schedulers:
            workflow_id = scheduler.workflow_id
            if workflow_id not in workflow_groups:
                workflow_groups[workflow_id] = []
            workflow_groups[workflow_id].append(scheduler)

        # Process groups in batches
        async def process_workflow_group(group):
            # Pre-load workflow data for the entire group
            workflow_id = group[0].workflow_id
            workflow_data = await self._get_cached_workflow_data(workflow_id)

            # Process schedulers in this group
            results = []
            for scheduler in group:
                scheduler.cached_workflow_data = workflow_data
                results.append(scheduler)

            return results

        # Process all groups concurrently
        processed_groups = await self.batch_processor.process_batches(
            list(workflow_groups.values()),
            process_workflow_group
        )

        # Flatten results
        optimized_list = []
        for group in processed_groups:
            if isinstance(group, list):
                optimized_list.extend(group)

        return optimized_list

    async def _get_cached_workflow_data(self, workflow_id: str) -> Dict:
        """Get workflow data with caching."""
        cache_key = f"workflow_data:{workflow_id}"

        if cache_key in self.performance_cache:
            return self.performance_cache[cache_key]

        # In a real implementation, this would fetch from database
        # For now, return a placeholder
        workflow_data = {"id": workflow_id, "cached_at": datetime.utcnow()}

        self.performance_cache[cache_key] = workflow_data
        return workflow_data

    def get_optimization_stats(self) -> Dict:
        """Get optimization statistics."""
        return {
            'cache_size': len(self.performance_cache),
            'cache_hit_rate': getattr(self.performance_cache, 'currsize', 0) / max(getattr(self.performance_cache, 'maxsize', 1), 1),
            'object_pools': {k: len(v) for k, v in self.memory_manager.object_pools.items()},
            'batch_processor_config': {
                'batch_size': self.batch_processor.batch_size,
                'max_concurrent': self.batch_processor.max_concurrent
            }
        }
```

### 3.4 Configuration Management

**File**: `src/core/config.py` (NEW)

```python
import os
from typing import Optional, Dict, Any
from pydantic import BaseSettings, validator
from enum import Enum

class LogLevel(str, Enum):
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class EnvironmentType(str, Enum):
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"

class SchedulerConfig(BaseSettings):
    """Comprehensive configuration management for scheduler system."""

    # Environment
    environment: EnvironmentType = EnvironmentType.DEVELOPMENT
    debug: bool = False
    log_level: LogLevel = LogLevel.INFO

    # Database Configuration
    database_url: str
    database_pool_size: int = 20
    database_max_overflow: int = 30
    database_pool_timeout: int = 30
    database_pool_recycle: int = 3600

    # Redis Configuration
    redis_url: str
    redis_max_connections: int = 50
    redis_socket_timeout: int = 5
    redis_socket_connect_timeout: int = 5

    # Scheduler Engine Configuration
    scheduler_poll_interval: int = 30  # seconds
    scheduler_batch_size: int = 50
    scheduler_max_concurrent: int = 10
    scheduler_lock_timeout: int = 300  # seconds

    # Task Queue Configuration
    task_queue_default_timeout: int = 300  # seconds
    task_queue_max_retries: int = 3
    task_queue_retry_backoff_base: int = 2
    task_queue_retry_backoff_max: int = 300

    # Worker Configuration
    worker_count: int = 5
    worker_queue_timeout: int = 30
    worker_health_check_interval: int = 60

    # Monitoring Configuration
    metrics_enabled: bool = True
    metrics_retention_days: int = 30
    health_check_port: int = 8080
    health_check_enabled: bool = True

    # Alert Configuration
    alerts_enabled: bool = True
    alert_high_failure_rate_threshold: float = 20.0
    alert_consecutive_failures_threshold: int = 5
    alert_high_execution_time_threshold: float = 300.0
    alert_queue_backlog_threshold: int = 1000
    alert_no_success_threshold: int = 1800  # seconds

    # Performance Configuration
    memory_optimization_enabled: bool = True
    object_pooling_enabled: bool = True
    cache_enabled: bool = True
    cache_ttl: int = 300  # seconds
    cache_max_size: int = 1000

    # Security Configuration
    api_key: Optional[str] = None
    jwt_secret: Optional[str] = None
    cors_origins: List[str] = ["*"]

    @validator('database_url')
    def validate_database_url(cls, v):
        if not v:
            raise ValueError('DATABASE_URL is required')
        return v

    @validator('redis_url')
    def validate_redis_url(cls, v):
        if not v:
            raise ValueError('REDIS_URL is required')
        return v

    @validator('worker_count')
    def validate_worker_count(cls, v):
        if v < 1:
            raise ValueError('WORKER_COUNT must be at least 1')
        if v > 50:
            raise ValueError('WORKER_COUNT should not exceed 50 for resource management')
        return v

    @property
    def is_production(self) -> bool:
        return self.environment == EnvironmentType.PRODUCTION

    @property
    def is_development(self) -> bool:
        return self.environment == EnvironmentType.DEVELOPMENT

    def get_log_config(self) -> Dict[str, Any]:
        """Get logging configuration."""
        return {
            'version': 1,
            'disable_existing_loggers': False,
            'formatters': {
                'detailed': {
                    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                },
                'simple': {
                    'format': '%(levelname)s - %(message)s'
                }
            },
            'handlers': {
                'console': {
                    'class': 'logging.StreamHandler',
                    'level': self.log_level.value,
                    'formatter': 'detailed' if self.debug else 'simple',
                    'stream': 'ext://sys.stdout'
                },
                'file': {
                    'class': 'logging.handlers.RotatingFileHandler',
                    'level': self.log_level.value,
                    'formatter': 'detailed',
                    'filename': 'logs/scheduler.log',
                    'maxBytes': 10485760,  # 10MB
                    'backupCount': 5
                }
            },
            'loggers': {
                'scheduler': {
                    'level': self.log_level.value,
                    'handlers': ['console', 'file'],
                    'propagate': False
                }
            },
            'root': {
                'level': self.log_level.value,
                'handlers': ['console']
            }
        }

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

# Global configuration instance
config = SchedulerConfig()
```

## Implementation Recommendations

### 1. **Immediate Actions (Week 1)**

- Implement connection pooling
- Add basic monitoring endpoints
- Deploy with reduced concurrency (start with 3-5 workers)

### 2. **Week 2-3**

- Full task queue implementation
- Distributed locking with Redis
- Comprehensive error handling

### 3. **Week 4+**

- Advanced monitoring and alerting
- Performance optimizations
- Production hardening

### 4. **Critical Success Factors**

```python
# Environment variables for production
SCHEDULER_WORKER_COUNT=10
SCHEDULER_MAX_CONCURRENT=15
DATABASE_POOL_SIZE=25
REDIS_MAX_CONNECTIONS=50
SCHEDULER_POLL_INTERVAL=30
TASK_QUEUE_MAX_RETRIES=3
ALERTS_ENABLED=true
METRICS_ENABLED=true
```

Your implementation plan is excellent and production-ready. The key is to deploy incrementally, monitor closely, and scale based on actual performance metrics. The combination of task queues, distributed locking, and proper monitoring will handle 100+ schedulers efficiently.
