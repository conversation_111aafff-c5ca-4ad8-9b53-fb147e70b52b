version: "3.8"

services:
  # PostgreSQL Database for Development
  postgres:
    image: postgres:15-alpine
    container_name: trigger-service-postgres-dev
    environment:
      POSTGRES_DB: trigger_db_dev
      POSTGRES_USER: trigger_user
      POSTGRES_PASSWORD: trigger_password
    ports:
      - "5433:5432" # Different port to avoid conflicts
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U trigger_user -d trigger_db_dev"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - trigger-dev-network

  # Redis Cache for Development
  redis:
    image: redis:7-alpine
    container_name: trigger-service-redis-dev
    ports:
      - "6380:6379" # Different port to avoid conflicts
    volumes:
      - redis_dev_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - trigger-dev-network

  # Development Trigger Service with hot reload
  trigger-service-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: builder # Use builder stage for development tools
    container_name: trigger-service-app-dev
    environment:
      - DEBUG=true
      - HOST=0.0.0.0
      - PORT=8000
      - LOG_LEVEL=DEBUG
      - LOG_FORMAT=text
      - DATABASE_URL=********************************************************/trigger_db_dev
      - REDIS_URL=redis://redis:6379/0
      - AUTH_SERVICE_URL=https://auth-service.example.com
      - AUTH_SERVICE_API_KEY=dev-auth-service-api-key
      - WORKFLOW_SERVICE_URL=https://ruh-test-api.rapidinnovation.dev
      - WORKFLOW_SERVICE_API_KEY=dev-workflow-service-api-key
      - GOOGLE_CALENDAR_WEBHOOK_URL=https://99b5-103-173-221-201.ngrok-free.app/api/v1/webhooks/google-calendar
      - SECRET_KEY=dev-secret-key-not-for-production
      - API_KEY=dev-api-key-not-for-production
    ports:
      - "8000:8000"
    volumes:
      - .:/app # Mount source code for hot reload
      - /app/.venv # Exclude virtual environment
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command:
      [
        "python",
        "-m",
        "uvicorn",
        "src.main:app",
        "--host",
        "0.0.0.0",
        "--port",
        "8000",
        "--reload",
      ]
    networks:
      - trigger-dev-network

  # pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: trigger-service-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: "False"
    ports:
      - "5050:80"
    depends_on:
      - postgres
    networks:
      - trigger-dev-network

  # Redis Commander for Redis management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: trigger-service-redis-commander
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - trigger-dev-network

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  trigger-dev-network:
    driver: bridge
