# Production Deployment Guide

This guide provides step-by-step instructions for deploying the scalable trigger scheduler service in a production environment.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [System Requirements](#system-requirements)
3. [Installation](#installation)
4. [Database Setup](#database-setup)
5. [Redis Setup](#redis-setup)
6. [Service Configuration](#service-configuration)
7. [Deployment](#deployment)
8. [Monitoring and Maintenance](#monitoring-and-maintenance)
9. [Troubleshooting](#troubleshooting)

## Prerequisites

- Linux server (Ubuntu 20.04+ or CentOS 8+ recommended)
- Python 3.9 or higher
- PostgreSQL 13+ database
- Redis 6.0+ server
- Systemd for service management
- Nginx (optional, for reverse proxy)

## System Requirements

### Minimum Requirements

- **CPU**: 2 cores
- **RAM**: 4GB
- **Storage**: 20GB SSD
- **Network**: 100 Mbps

### Recommended for Production

- **CPU**: 4+ cores
- **RAM**: 8GB+
- **Storage**: 50GB+ SSD
- **Network**: 1 Gbps
- **Load Balancer**: For high availability

### Scaling Guidelines

- **100 triggers**: 2-4 cores, 4-8GB RAM
- **1000 triggers**: 4-8 cores, 8-16GB RAM
- **10000+ triggers**: 8+ cores, 16GB+ RAM, multiple instances

## Installation

### 1. Create System User

```bash
# Create dedicated user for the service
sudo useradd -r -s /bin/false -d /opt/trigger-service scheduler
sudo mkdir -p /opt/trigger-service
sudo chown scheduler:scheduler /opt/trigger-service
```

### 2. Install Python Dependencies

```bash
# Switch to service directory
cd /opt/trigger-service

# Create virtual environment
sudo -u scheduler python3 -m venv venv
sudo -u scheduler ./venv/bin/pip install --upgrade pip

# Install dependencies
sudo -u scheduler ./venv/bin/pip install -r requirements.txt
```

### 3. Deploy Application Code

```bash
# Copy application files
sudo cp -r src/ /opt/trigger-service/
sudo cp -r scripts/ /opt/trigger-service/
sudo cp -r migrations/ /opt/trigger-service/
sudo cp requirements.txt /opt/trigger-service/
sudo chown -R scheduler:scheduler /opt/trigger-service/
```

## Database Setup

### 1. Install PostgreSQL

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# CentOS/RHEL
sudo dnf install postgresql postgresql-server postgresql-contrib
sudo postgresql-setup --initdb
```

### 2. Configure Database

```bash
# Switch to postgres user
sudo -u postgres psql

-- Create database and user
CREATE DATABASE trigger_db;
CREATE USER scheduler WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE trigger_db TO scheduler;

-- Enable required extensions
\c trigger_db
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
\q
```

### 3. Run Migrations

```bash
# Apply database schema
cd /opt/trigger-service
sudo -u scheduler ./venv/bin/python -m alembic upgrade head

# Apply performance indexes
sudo -u scheduler ./venv/bin/psql -d trigger_db -f migrations/add_scheduler_indexes.sql
```

## Redis Setup

### 1. Install Redis

```bash
# Ubuntu/Debian
sudo apt install redis-server

# CentOS/RHEL
sudo dnf install redis
```

### 2. Configure Redis

```bash
# Edit Redis configuration
sudo nano /etc/redis/redis.conf

# Recommended settings for production:
# maxmemory 2gb
# maxmemory-policy allkeys-lru
# save 900 1
# save 300 10
# save 60 10000
```

### 3. Start Redis Service

```bash
sudo systemctl enable redis
sudo systemctl start redis
sudo systemctl status redis
```

## Service Configuration

### 1. Environment Configuration

```bash
# Create environment file
sudo nano /opt/trigger-service/.env
```

```env
# Database Configuration
DATABASE_URL=postgresql://scheduler:your_secure_password@localhost/trigger_db

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Service Configuration
LOG_LEVEL=INFO
MAX_CONCURRENT_SCHEDULERS=20
SCHEDULER_BATCH_SIZE=100
WORKER_CONCURRENCY=10
CYCLE_INTERVAL=30

# Monitoring
METRICS_PORT=8080
HEALTH_PORT=8081

# Google API Configuration (if using Google Calendar)
GOOGLE_CLIENT_ID=your_client_id
GOOGLE_CLIENT_SECRET=your_client_secret
```

### 2. Create Log Directory

```bash
sudo mkdir -p /var/log/scheduler-service
sudo chown scheduler:scheduler /var/log/scheduler-service
```

### 3. Install Systemd Service

```bash
# Copy service file
sudo cp deployment/scheduler-service.service /etc/systemd/system/

# Reload systemd
sudo systemctl daemon-reload

# Enable service
sudo systemctl enable scheduler-service
```

## Deployment

### 1. Start the Service

```bash
# Start the scheduler service
sudo systemctl start scheduler-service

# Check status
sudo systemctl status scheduler-service

# View logs
sudo journalctl -u scheduler-service -f
```

### 2. Verify Deployment

```bash
# Check health endpoint
curl http://localhost:8081/health

# Check metrics endpoint
curl http://localhost:8080/metrics

# Check service logs
sudo journalctl -u scheduler-service --since "1 hour ago"
```

### 3. Test Functionality

```bash
# Create a test trigger (adjust based on your API)
curl -X POST http://localhost:8000/api/triggers \
  -H "Content-Type: application/json" \
  -d '{
    "name": "test_trigger",
    "schedule": "*/5 * * * *",
    "workflow_id": "test_workflow"
  }'

# Monitor scheduler processing
sudo journalctl -u scheduler-service -f | grep "Processing scheduler"
```

## Monitoring and Maintenance

### 1. Health Checks

The service provides built-in health check endpoints:

- **Health**: `http://localhost:8081/health`
- **Ready**: `http://localhost:8081/ready`
- **Metrics**: `http://localhost:8080/metrics`

### 2. Log Monitoring

```bash
# Monitor service logs
sudo journalctl -u scheduler-service -f

# Check for errors
sudo journalctl -u scheduler-service -p err

# Monitor specific components
sudo journalctl -u scheduler-service | grep "SchedulerEngine"
sudo journalctl -u scheduler-service | grep "TaskQueue"
```

### 3. Performance Monitoring

Key metrics to monitor:

- **Scheduler Processing Time**: Average time to process scheduler batches
- **Task Queue Length**: Number of pending tasks
- **Worker Utilization**: Active workers vs. configured concurrency
- **Database Connection Pool**: Active/idle connections
- **Redis Memory Usage**: Memory consumption and hit rates

### 4. Backup and Recovery

```bash
# Database backup
sudo -u postgres pg_dump trigger_db > /backup/trigger_db_$(date +%Y%m%d_%H%M%S).sql

# Redis backup (if persistence is enabled)
sudo cp /var/lib/redis/dump.rdb /backup/redis_$(date +%Y%m%d_%H%M%S).rdb
```

### 5. Log Rotation

```bash
# Configure logrotate for service logs
sudo nano /etc/logrotate.d/scheduler-service
```

```
/var/log/scheduler-service/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 scheduler scheduler
    postrotate
        systemctl reload scheduler-service
    endscript
}
```

## Scaling and High Availability

### 1. Horizontal Scaling

To scale beyond a single instance:

1. **Deploy multiple instances** with different `INSTANCE_ID` environment variables
2. **Use Redis for coordination** (already implemented via distributed locking)
3. **Load balance** health check endpoints
4. **Monitor** each instance separately

### 2. Database Scaling

For high-load scenarios:

1. **Connection pooling**: Increase `DB_POOL_SIZE` in configuration
2. **Read replicas**: Configure read-only database replicas
3. **Partitioning**: Partition scheduler tables by date or trigger type

### 3. Redis Scaling

For Redis scaling:

1. **Redis Cluster**: Use Redis cluster for high availability
2. **Separate instances**: Use different Redis instances for locks vs. task queue
3. **Memory optimization**: Tune Redis memory policies

## Troubleshooting

### Common Issues

#### Service Won't Start

```bash
# Check service status
sudo systemctl status scheduler-service

# Check logs for errors
sudo journalctl -u scheduler-service -n 50

# Verify configuration
sudo -u scheduler /opt/trigger-service/venv/bin/python -c "from src.utils.config import get_config; print(get_config())"
```

#### Database Connection Issues

```bash
# Test database connection
sudo -u scheduler psql -h localhost -U scheduler -d trigger_db -c "SELECT 1;"

# Check database logs
sudo tail -f /var/log/postgresql/postgresql-*.log
```

#### Redis Connection Issues

```bash
# Test Redis connection
redis-cli ping

# Check Redis logs
sudo journalctl -u redis -n 50
```

#### High Memory Usage

```bash
# Check memory usage
sudo systemctl status scheduler-service
ps aux | grep python

# Monitor Redis memory
redis-cli info memory

# Check for memory leaks
sudo -u scheduler /opt/trigger-service/venv/bin/python -c "
import psutil
import os
process = psutil.Process(os.getpid())
print(f'Memory: {process.memory_info().rss / 1024 / 1024:.2f} MB')
"
```

#### Performance Issues

```bash
# Check metrics endpoint
curl http://localhost:8080/metrics | jq

# Monitor database performance
sudo -u postgres psql -d trigger_db -c "
SELECT query, calls, total_time, mean_time
FROM pg_stat_statements
ORDER BY total_time DESC
LIMIT 10;"

# Check Redis performance
redis-cli --latency-history
```

### Performance Tuning

#### Database Optimization

```sql
-- Monitor slow queries
SELECT query, calls, total_time, mean_time, rows
FROM pg_stat_statements
WHERE mean_time > 100
ORDER BY total_time DESC;

-- Check index usage
SELECT schemaname, tablename, attname, n_distinct, correlation
FROM pg_stats
WHERE tablename IN ('schedulers', 'triggers');
```

#### Redis Optimization

```bash
# Monitor Redis performance
redis-cli --latency
redis-cli --stat

# Check memory usage
redis-cli info memory | grep used_memory
```

#### Application Tuning

Key configuration parameters to adjust:

- `MAX_CONCURRENT_SCHEDULERS`: Increase for more parallelism
- `SCHEDULER_BATCH_SIZE`: Increase for better database efficiency
- `WORKER_CONCURRENCY`: Adjust based on CPU cores
- `CYCLE_INTERVAL`: Decrease for more responsive scheduling

## Security Considerations

### 1. Network Security

- Use firewall rules to restrict access to Redis and PostgreSQL
- Consider VPN or private networks for inter-service communication
- Use TLS/SSL for external connections

### 2. Authentication

- Use strong passwords for database and Redis
- Rotate credentials regularly
- Consider using environment-specific secrets management

### 3. File Permissions

```bash
# Secure configuration files
sudo chmod 600 /opt/trigger-service/.env
sudo chown scheduler:scheduler /opt/trigger-service/.env

# Secure log files
sudo chmod 644 /var/log/scheduler-service/*
sudo chown scheduler:scheduler /var/log/scheduler-service/*
```

## Support and Maintenance

### Regular Maintenance Tasks

1. **Weekly**: Review logs for errors and performance issues
2. **Monthly**: Update dependencies and security patches
3. **Quarterly**: Review and optimize database performance
4. **Annually**: Review and update scaling configuration

### Monitoring Checklist

- [ ] Service is running and healthy
- [ ] Database connections are stable
- [ ] Redis is responding normally
- [ ] Disk space is sufficient
- [ ] Memory usage is within limits
- [ ] No error patterns in logs
- [ ] Metrics endpoints are accessible
- [ ] Backup processes are working

For additional support, refer to the project documentation or contact the development team.
