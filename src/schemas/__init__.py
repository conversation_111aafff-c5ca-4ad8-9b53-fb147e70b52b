"""
Pydantic schemas package for the Trigger Service.

This package contains all Pydantic models for request/response validation
and data serialization.
"""

from .trigger import (
    TriggerCreate,
    TriggerUpdate,
    TriggerResponse,
    EventFieldMapping,
    GoogleCalendarTriggerConfig,
    TriggerTypeInfo,
    TriggerTypesResponse,
)
from .webhook import WebhookCreate, WebhookResponse
from .workflow import WorkflowCreate, WorkflowUpdate, WorkflowResponse

# Simplified scheduler schemas (production ready)
from .scheduler import (
    ScheduleFrequency,
    SimplifiedSchedulerBase,
    SimplifiedSchedulerCreate,
    SimplifiedSchedulerUpdate,
    SimplifiedSchedulerResponse,
)

__all__ = [
    # Trigger schemas
    "TriggerCreate",
    "TriggerUpdate",
    "TriggerResponse",
    "EventFieldMapping",
    "GoogleCalendarTriggerConfig",
    "TriggerTypeInfo",
    "TriggerTypesResponse",
    # Webhook schemas
    "WebhookCreate",
    "WebhookResponse",
    # Workflow schemas
    "WorkflowCreate",
    "WorkflowUpdate",
    "WorkflowResponse",
    # Simplified scheduler schemas (production ready)
    "ScheduleFrequency",
    "SimplifiedSchedulerBase",
    "SimplifiedSchedulerCreate",
    "SimplifiedSchedulerUpdate",
    "SimplifiedSchedulerResponse",
]
