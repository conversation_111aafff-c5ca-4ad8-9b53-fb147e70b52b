"""
Pydantic schemas for webhook-related operations.

This module contains all Pydantic models for webhook events and responses
from external services.
"""

from datetime import datetime
from typing import Dict, Any, Optional, List

from pydantic import BaseModel, Field, ConfigDict


class WebhookEvent(BaseModel):
    """Base schema for webhook events."""

    event_id: str = Field(..., description="Unique identifier for the event")
    event_type: str = Field(
        ..., description="Type of event (created, updated, deleted, etc.)"
    )
    timestamp: datetime = Field(..., description="When the event occurred")
    source: str = Field(..., description="Source service that generated the event")
    data: Dict[str, Any] = Field(..., description="Event-specific data")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class GoogleCalendarWebhookEvent(BaseModel):
    """Schema for Google Calendar webhook events."""

    resource_id: str = Field(..., description="Google Calendar resource ID")
    resource_uri: str = Field(..., description="Google Calendar resource URI")
    resource_state: str = Field(
        ..., description="State of the resource (sync, exists, not_exists)"
    )
    channel_id: str = Field(..., description="Webhook channel ID")
    channel_token: Optional[str] = Field(None, description="Channel verification token")
    channel_expiration: Optional[str] = Field(
        None, description="Channel expiration time"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "resource_id": "calendar-resource-123",
                "resource_uri": "https://www.googleapis.com/calendar/v3/calendars/primary/events",
                "resource_state": "exists",
                "channel_id": "webhook-channel-456",
                "channel_token": "verification-token-789",
                "channel_expiration": "1640995200000",
            }
        }
    )


class Webhook(BaseModel):
    """Schema for webhook information."""

    id: str = Field(..., description="Unique identifier for the webhook")
    url: str = Field(..., description="Webhook URL")
    event_types: List[str] = Field(..., description="List of event types")

    model_config = ConfigDict(from_attributes=True)


class WebhookCreate(BaseModel):
    """Schema for creating a new webhook."""

    url: str = Field(..., description="Webhook URL")
    event_types: List[str] = Field(..., description="List of event types")


class WebhookResponse(BaseModel):
    """Schema for webhook response."""

    success: bool = Field(
        ..., description="Whether the webhook was processed successfully"
    )
    message: str = Field(..., description="Response message")
    event_id: Optional[str] = Field(None, description="ID of the processed event")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "success": True,
                "message": "Webhook processed successfully",
                "event_id": "event-123",
            }
        }
    )
