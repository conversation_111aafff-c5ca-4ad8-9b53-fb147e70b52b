import logging
from datetime import datetime, timedelta
from typing import Union, Optional, Dict, Any, List
import pytz
from croniter import croniter

from src.schemas.scheduler import (
    SimplifiedSchedulerResponse,
    ScheduleFrequency,
)

logger = logging.getLogger(__name__)


class InvalidScheduleConfigError(Exception):
    """Exception raised for invalid schedule configurations."""

    pass


class ScheduleParser:
    """
    Frequency-based schedule parser for the new simplified scheduler schema.

    Supports the new SimplifiedSchedulerResponse objects with frequency-based configuration:
    - every_minute: Execute every minute
    - hourly: Execute every hour
    - daily: Execute daily at specified time
    - weekly: Execute weekly on specified days and time
    - monthly: Execute monthly on specified days and time
    - custom: Execute using custom cron expression
    """

    @staticmethod
    def get_next_run_time(
        schedule_config: Union[SimplifiedSchedulerResponse, Dict[str, Any]],
        last_run_time: Optional[datetime] = None,
    ) -> Optional[datetime]:
        """
        Calculates the next scheduled run time based on the simplified frequency configuration.

        Args:
            schedule_config: The SimplifiedSchedulerResponse object or dict with frequency config
            last_run_time: The last time the scheduler was run (optional)

        Returns:
            The next scheduled datetime in UTC, or None if schedule is complete

        Raises:
            InvalidScheduleConfigError: If the configuration is invalid
        """
        try:
            # Handle SimplifiedSchedulerResponse objects
            if isinstance(schedule_config, SimplifiedSchedulerResponse):
                return ScheduleParser._handle_simplified_schedule(
                    schedule_config, last_run_time
                )

            # Handle raw dictionary (for testing)
            elif isinstance(schedule_config, dict):
                return ScheduleParser._handle_dict_schedule(
                    schedule_config, last_run_time
                )

            else:
                raise InvalidScheduleConfigError(
                    f"Unsupported schedule configuration type: {type(schedule_config)}. "
                    f"Expected SimplifiedSchedulerResponse or dict."
                )

        except Exception as e:
            logger.error(f"Error calculating next run time: {e}", exc_info=True)
            if isinstance(e, InvalidScheduleConfigError):
                raise
            raise InvalidScheduleConfigError(f"Failed to calculate next run time: {e}")

    @staticmethod
    def _handle_simplified_schedule(
        schedule_config: SimplifiedSchedulerResponse, last_run_time: Optional[datetime]
    ) -> Optional[datetime]:
        """Handle SimplifiedSchedulerResponse objects."""
        frequency = schedule_config.frequency
        timezone_str = schedule_config.timezone or "UTC"

        logger.debug(
            f"Processing simplified schedule: frequency={frequency}, timezone={timezone_str}"
        )

        if frequency == ScheduleFrequency.EVERY_MINUTE:
            return ScheduleParser._get_next_every_minute(timezone_str, last_run_time)
        elif frequency == ScheduleFrequency.HOURLY:
            return ScheduleParser._get_next_hourly(timezone_str, last_run_time)
        elif frequency == ScheduleFrequency.DAILY:
            return ScheduleParser._get_next_daily(
                schedule_config.time, timezone_str, last_run_time
            )
        elif frequency == ScheduleFrequency.WEEKLY:
            return ScheduleParser._get_next_weekly(
                schedule_config.time,
                schedule_config.days_of_week,
                timezone_str,
                last_run_time,
            )
        elif frequency == ScheduleFrequency.MONTHLY:
            return ScheduleParser._get_next_monthly(
                schedule_config.time,
                schedule_config.days_of_month,
                timezone_str,
                last_run_time,
            )
        elif frequency == ScheduleFrequency.CUSTOM:
            return ScheduleParser._get_next_custom(
                schedule_config.cron_expression, timezone_str, last_run_time
            )
        else:
            raise InvalidScheduleConfigError(f"Unsupported frequency: {frequency}")

    @staticmethod
    def _handle_dict_schedule(
        schedule_config: Dict[str, Any], last_run_time: Optional[datetime]
    ) -> Optional[datetime]:
        """Handle raw dictionary configurations (for testing)."""
        if "frequency" not in schedule_config:
            raise InvalidScheduleConfigError(
                "Invalid schedule configuration: missing 'frequency' field"
            )

        frequency = schedule_config["frequency"]
        timezone_str = schedule_config.get("timezone", "UTC")

        if frequency == "every_minute":
            return ScheduleParser._get_next_every_minute(timezone_str, last_run_time)
        elif frequency == "hourly":
            return ScheduleParser._get_next_hourly(timezone_str, last_run_time)
        elif frequency == "daily":
            return ScheduleParser._get_next_daily(
                schedule_config.get("time"), timezone_str, last_run_time
            )
        elif frequency == "weekly":
            return ScheduleParser._get_next_weekly(
                schedule_config.get("time"),
                schedule_config.get("days_of_week"),
                timezone_str,
                last_run_time,
            )
        elif frequency == "monthly":
            return ScheduleParser._get_next_monthly(
                schedule_config.get("time"),
                schedule_config.get("days_of_month"),
                timezone_str,
                last_run_time,
            )
        elif frequency == "custom":
            return ScheduleParser._get_next_custom(
                schedule_config.get("cron_expression"), timezone_str, last_run_time
            )
        else:
            raise InvalidScheduleConfigError(f"Unsupported frequency: {frequency}")

    # ============================================================================
    # FREQUENCY-BASED PARSING METHODS
    # ============================================================================

    @staticmethod
    def _get_next_every_minute(
        timezone_str: str, last_run_time: Optional[datetime]
    ) -> datetime:
        """Calculate next run time for every minute execution."""
        try:
            tz = pytz.timezone(timezone_str)
            now = datetime.now(pytz.utc)

            if last_run_time:
                # Next run is 1 minute after last run
                next_run = last_run_time + timedelta(minutes=1)
                # If calculated time is in the past, schedule for next minute from now
                if next_run <= now:
                    next_run = now + timedelta(minutes=1)
            else:
                # First run: schedule for next minute
                next_run = now + timedelta(minutes=1)

            # Ensure we return UTC time
            if next_run.tzinfo is None:
                next_run = pytz.utc.localize(next_run)
            elif next_run.tzinfo != pytz.utc:
                next_run = next_run.astimezone(pytz.utc)

            logger.debug("Next every_minute run", next_run=next_run)
            return next_run

        except Exception as e:
            raise InvalidScheduleConfigError(
                f"Error calculating every_minute schedule: {e}"
            )

    @staticmethod
    def _get_next_hourly(
        timezone_str: str, last_run_time: Optional[datetime]
    ) -> datetime:
        """Calculate next run time for hourly execution."""
        try:
            tz = pytz.timezone(timezone_str)
            now = datetime.now(pytz.utc)

            if last_run_time:
                # Next run is 1 hour after last run
                next_run = last_run_time + timedelta(hours=1)
                # If calculated time is in the past, schedule for next hour from now
                if next_run <= now:
                    next_run = now + timedelta(hours=1)
            else:
                # First run: schedule for next hour
                next_run = now + timedelta(hours=1)

            # Ensure we return UTC time
            if next_run.tzinfo is None:
                next_run = pytz.utc.localize(next_run)
            elif next_run.tzinfo != pytz.utc:
                next_run = next_run.astimezone(pytz.utc)

            logger.debug("Next hourly run", next_run=next_run)
            return next_run

        except Exception as e:
            raise InvalidScheduleConfigError(f"Error calculating hourly schedule: {e}")

    @staticmethod
    def _get_next_daily(
        time_str: Optional[str], timezone_str: str, last_run_time: Optional[datetime]
    ) -> datetime:
        """Calculate next run time for daily execution at specified time."""
        if not time_str:
            raise InvalidScheduleConfigError("time is required for daily frequency")

        try:
            # Parse time string (HH:mm format)
            hour, minute = map(int, time_str.split(":"))
            if not (0 <= hour <= 23 and 0 <= minute <= 59):
                raise ValueError("Invalid time values")

            tz = pytz.timezone(timezone_str)
            now = datetime.now(pytz.utc)
            now_local = now.astimezone(tz)

            # Create next run time for today at specified time
            next_run_local = now_local.replace(
                hour=hour, minute=minute, second=0, microsecond=0
            )

            # If the time has already passed today, schedule for tomorrow
            if next_run_local <= now_local:
                next_run_local += timedelta(days=1)

            # Convert to UTC
            next_run = next_run_local.astimezone(pytz.utc)

            logger.debug(
                "Next daily run",
                time_str=time_str,
                timezone_str=timezone_str,
                next_run=next_run,
            )
            return next_run

        except ValueError as e:
            raise InvalidScheduleConfigError(f"Invalid time format '{time_str}': {e}")
        except Exception as e:
            raise InvalidScheduleConfigError(f"Error calculating daily schedule: {e}")

    @staticmethod
    def _get_next_weekly(
        time_str: Optional[str],
        days_of_week: Optional[List[str]],
        timezone_str: str,
        last_run_time: Optional[datetime],
    ) -> datetime:
        """Calculate next run time for weekly execution on specified days and time."""
        if not time_str:
            raise InvalidScheduleConfigError("time is required for weekly frequency")
        if not days_of_week or len(days_of_week) == 0:
            raise InvalidScheduleConfigError(
                "days_of_week is required for weekly frequency"
            )

        try:
            # Parse time string
            hour, minute = map(int, time_str.split(":"))
            if not (0 <= hour <= 23 and 0 <= minute <= 59):
                raise ValueError("Invalid time values")

            # Map day names to weekday numbers (Monday=0, Sunday=6)
            day_mapping = {
                "Monday": 0,
                "Tuesday": 1,
                "Wednesday": 2,
                "Thursday": 3,
                "Friday": 4,
                "Saturday": 5,
                "Sunday": 6,
            }

            target_weekdays = []
            for day in days_of_week:
                if day not in day_mapping:
                    raise ValueError(f"Invalid day name: {day}")
                target_weekdays.append(day_mapping[day])

            target_weekdays.sort()

            tz = pytz.timezone(timezone_str)
            now = datetime.now(pytz.utc)
            now_local = now.astimezone(tz)
            current_weekday = now_local.weekday()

            # Find the next occurrence
            next_run_local = None

            # Check if any target day is today and time hasn't passed
            if current_weekday in target_weekdays:
                today_at_time = now_local.replace(
                    hour=hour, minute=minute, second=0, microsecond=0
                )
                if today_at_time > now_local:
                    next_run_local = today_at_time

            # If not found, find the next target day
            if next_run_local is None:
                days_ahead = None
                for target_day in target_weekdays:
                    if target_day > current_weekday:
                        days_ahead = target_day - current_weekday
                        break

                # If no day found this week, take the first day of next week
                if days_ahead is None:
                    days_ahead = 7 - current_weekday + target_weekdays[0]

                next_run_local = now_local.replace(
                    hour=hour, minute=minute, second=0, microsecond=0
                )
                next_run_local += timedelta(days=days_ahead)

            # Convert to UTC
            next_run = next_run_local.astimezone(pytz.utc)

            logger.debug(
                f"Next weekly run on {days_of_week} at {time_str} {timezone_str}: {next_run}"
            )
            return next_run

        except ValueError as e:
            raise InvalidScheduleConfigError(
                f"Invalid weekly schedule configuration: {e}"
            )
        except Exception as e:
            raise InvalidScheduleConfigError(f"Error calculating weekly schedule: {e}")

    @staticmethod
    def _get_next_monthly(
        time_str: Optional[str],
        days_of_month: Optional[List[int]],
        timezone_str: str,
        last_run_time: Optional[datetime],
    ) -> datetime:
        """Calculate next run time for monthly execution on specified days and time."""
        if not time_str:
            raise InvalidScheduleConfigError("time is required for monthly frequency")
        if not days_of_month or len(days_of_month) == 0:
            raise InvalidScheduleConfigError(
                "days_of_month is required for monthly frequency"
            )

        try:
            # Parse time string
            hour, minute = map(int, time_str.split(":"))
            if not (0 <= hour <= 23 and 0 <= minute <= 59):
                raise ValueError("Invalid time values")

            # Validate days of month
            for day in days_of_month:
                if not (1 <= day <= 31):
                    raise ValueError(f"Invalid day of month: {day}")

            target_days = sorted(days_of_month)

            tz = pytz.timezone(timezone_str)
            now = datetime.now(pytz.utc)
            now_local = now.astimezone(tz)

            # Find the next occurrence
            next_run_local = None

            # Check if any target day is today and time hasn't passed
            if now_local.day in target_days:
                today_at_time = now_local.replace(
                    hour=hour, minute=minute, second=0, microsecond=0
                )
                if today_at_time > now_local:
                    next_run_local = today_at_time

            # If not found, find the next target day this month
            if next_run_local is None:
                for target_day in target_days:
                    if target_day > now_local.day:
                        try:
                            next_run_local = now_local.replace(
                                day=target_day,
                                hour=hour,
                                minute=minute,
                                second=0,
                                microsecond=0,
                            )
                            break
                        except ValueError:
                            # Day doesn't exist in current month (e.g., Feb 30)
                            continue

            # If not found this month, find the first valid day next month
            if next_run_local is None:
                # Move to next month
                if now_local.month == 12:
                    next_month = now_local.replace(
                        year=now_local.year + 1, month=1, day=1
                    )
                else:
                    next_month = now_local.replace(month=now_local.month + 1, day=1)

                for target_day in target_days:
                    try:
                        next_run_local = next_month.replace(
                            day=target_day,
                            hour=hour,
                            minute=minute,
                            second=0,
                            microsecond=0,
                        )
                        break
                    except ValueError:
                        # Day doesn't exist in next month
                        continue

                if next_run_local is None:
                    raise ValueError("No valid days found in next month")

            # Convert to UTC
            next_run = next_run_local.astimezone(pytz.utc)

            logger.debug(
                f"Next monthly run on {days_of_month} at {time_str} {timezone_str}: {next_run}"
            )
            return next_run

        except ValueError as e:
            raise InvalidScheduleConfigError(
                f"Invalid monthly schedule configuration: {e}"
            )
        except Exception as e:
            raise InvalidScheduleConfigError(f"Error calculating monthly schedule: {e}")

    @staticmethod
    def _get_next_custom(
        cron_expression: Optional[str],
        timezone_str: str,
        last_run_time: Optional[datetime],
    ) -> datetime:
        """Calculate next run time using custom cron expression."""
        if not cron_expression:
            raise InvalidScheduleConfigError(
                "cron_expression is required for custom frequency"
            )

        try:
            tz = pytz.timezone(timezone_str)
            now = datetime.now(pytz.utc)

            # Use last_run_time as base if provided, otherwise use current time
            base_time = last_run_time if last_run_time else now
            base_time_local = base_time.astimezone(tz)

            # Create croniter instance
            cron = croniter(cron_expression, base_time_local)
            next_run_local = cron.get_next(datetime)

            # Convert to UTC
            next_run = next_run_local.astimezone(pytz.utc)

            logger.debug(
                f"Next custom run with '{cron_expression}' {timezone_str}: {next_run}"
            )
            return next_run

        except Exception as e:
            raise InvalidScheduleConfigError(
                f"Invalid cron expression '{cron_expression}': {e}"
            )

    # ============================================================================
    # UTILITY METHODS
    # ============================================================================

    @staticmethod
    def convert_to_utc(dt: datetime, timezone_str: str) -> datetime:
        """Converts a datetime object from a given timezone to UTC."""
        try:
            local_tz = pytz.timezone(timezone_str)
            if dt.tzinfo is None:
                local_dt = local_tz.localize(dt)
            else:
                local_dt = dt.astimezone(local_tz)
            return local_dt.astimezone(pytz.utc)
        except Exception as e:
            raise InvalidScheduleConfigError(f"Error converting to UTC: {e}")

    @staticmethod
    def convert_from_utc(dt_utc: datetime, timezone_str: str) -> datetime:
        """Converts a UTC datetime object to a given timezone."""
        try:
            if dt_utc.tzinfo is None:
                dt_utc = pytz.utc.localize(dt_utc)
            target_tz = pytz.timezone(timezone_str)
            return dt_utc.astimezone(target_tz)
        except Exception as e:
            raise InvalidScheduleConfigError(f"Error converting from UTC: {e}")
