"""
Authentication client for integrating with the external auth service.

This module provides functionality to:
- Validate bearer tokens and fetch user details
- Retrieve OAuth credentials for users from the auth service
"""

import asyncio
from typing import Optional, Dict, Any
import httpx
from src.utils.config import get_settings
from src.utils.logger import get_logger

logger = get_logger(__name__)


class AuthServiceError(Exception):
    """Base exception for auth service errors."""

    pass


class AuthServiceConnectionError(AuthServiceError):
    """Connection-related errors with auth service."""

    pass


class AuthServiceAuthenticationError(AuthServiceError):
    """Authentication-related errors."""

    pass


class AuthClient:
    """
    Client for interacting with the external authentication service.

    Handles bearer token validation and OAuth credential retrieval.
    """

    def __init__(self):
        """Initialize the auth client with configuration."""
        self.settings = get_settings()
        self.base_url = self.settings.auth_service_url.rstrip("/")
        self.api_key = self.settings.auth_service_api_key
        self.timeout = self.settings.auth_service_timeout

        # HTTP client for making requests
        self._client: Optional[httpx.AsyncClient] = None

    async def __aenter__(self):
        """Async context manager entry."""
        self._client = httpx.AsyncClient(
            timeout=httpx.Timeout(self.timeout),
            headers={
                "User-Agent": "TriggerService/1.0",
                "Accept": "application/json",
            },
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self._client:
            await self._client.aclose()
            self._client = None

    async def validate_bearer_token(
        self, bearer_token: str
    ) -> Optional[Dict[str, Any]]:
        """
        Validate a bearer token and return user details.

        Args:
            bearer_token: The bearer token to validate

        Returns:
            Dict containing user details if token is valid, None otherwise

        Raises:
            AuthServiceError: If there's an error communicating with the auth service
        """
        try:
            logger.debug("Validating bearer token with auth service")

            if not self._client:
                raise AuthServiceError(
                    "Auth client not initialized. Use async context manager."
                )

            # Make request to auth service /users/me endpoint
            response = await self._client.get(
                f"{self.base_url}/api/v1/users/me",
                headers={
                    "Authorization": f"Bearer {bearer_token}",
                    "accept": "application/json",
                },
            )

            if response.status_code == 200:
                user_data = response.json()
                logger.info(
                    "Successfully validated bearer token",
                    user_id=user_data.get("id"),
                    email=user_data.get("email"),
                )
                return user_data
            elif response.status_code == 401:
                logger.warning("Invalid bearer token provided")
                return None
            else:
                logger.error(
                    "Unexpected response from auth service",
                    status_code=response.status_code,
                    response_text=response.text,
                )
                raise AuthServiceError(
                    f"Auth service returned status {response.status_code}"
                )

        except httpx.TimeoutException:
            logger.error("Timeout while validating bearer token")
            raise AuthServiceConnectionError("Timeout connecting to auth service")
        except httpx.RequestError as e:
            logger.error("Request error while validating bearer token", error=str(e))
            error_msg = f"Failed to connect to auth service: {str(e)}"
            if "All connection attempts failed" in str(e):
                error_msg += f"\n\nDEVELOPMENT HELP:\n"
                error_msg += f"- Check if auth service is running at {self.base_url}\n"
                error_msg += f"- Verify AUTH_SERVICE_URL in .env file\n"
                error_msg += (
                    f"- For development, ensure the auth service is accessible\n"
                )
                error_msg += f"- Current auth service URL: {self.base_url}"
            raise AuthServiceConnectionError(error_msg)
        except Exception as e:
            logger.error("Unexpected error validating bearer token", error=str(e))
            raise AuthServiceError(f"Unexpected error: {str(e)}")

    async def get_oauth_credentials(
        self,
        user_id: str,
        tool_name: str = "google_calendar",
        provider: str = "google",
    ) -> Optional[Dict[str, Any]]:
        """
        Retrieve OAuth credentials for a user from the auth service.

        Args:
            user_id: The user ID to get credentials for
            mcp_id: MCP identifier (default: "mcp123")
            tool_name: Tool name (default: "google_calendar")
            provider: OAuth provider (default: "google")

        Returns:
            Dict containing OAuth credentials if found, None otherwise

        Raises:
            AuthServiceError: If there's an error communicating with the auth service
        """
        try:
            logger.debug(
                "Fetching OAuth credentials from auth service",
                user_id=user_id,
                tool_name=tool_name,
                provider=provider,
            )

            if not self._client:
                raise AuthServiceError(
                    "Auth client not initialized. Use async context manager."
                )
            # Make request to auth service OAuth credentials endpoint
            response = await self._client.get(
                f"{self.base_url}/api/v1/oauth/server/credentials",
                params={
                    "user_id": user_id,
                    "tool_name": tool_name,
                    "provider": provider,
                },
                headers={
                    "X-Server-Auth-Key": self.api_key,
                    "accept": "application/json",
                },
            )

            if response.status_code == 200:
                credentials_data = response.json()
                logger.info(
                    "Successfully retrieved OAuth credentials",
                    user_id=user_id,
                    provider=provider,
                    has_access_token=bool(credentials_data.get("access_token")),
                    has_refresh_token=bool(credentials_data.get("refresh_token")),
                )
                return credentials_data
            elif response.status_code == 404:
                logger.warning(
                    "OAuth credentials not found for user",
                    user_id=user_id,
                    provider=provider,
                )
                return None
            else:
                logger.error(
                    "Unexpected response from auth service OAuth endpoint",
                    status_code=response.status_code,
                    response_text=response.text,
                    user_id=user_id,
                )
                raise AuthServiceError(
                    f"Auth service returned status {response.status_code}"
                )

        except httpx.TimeoutException:
            logger.error("Timeout while fetching OAuth credentials", user_id=user_id)
            raise AuthServiceConnectionError("Timeout connecting to auth service")
        except httpx.RequestError as e:
            logger.error(
                "Request error while fetching OAuth credentials",
                error=str(e),
                user_id=user_id,
            )
            error_msg = f"Failed to connect to auth service: {str(e)}"
            if "All connection attempts failed" in str(e):
                error_msg += f"\n\nDEVELOPMENT HELP:\n"
                error_msg += f"- Check if auth service is running at {self.base_url}\n"
                error_msg += f"- Verify AUTH_SERVICE_URL in .env file\n"
                error_msg += (
                    f"- For development, ensure the auth service is accessible\n"
                )
                error_msg += f"- Current auth service URL: {self.base_url}\n"
                error_msg += f"- User ID: {user_id}"
            raise AuthServiceConnectionError(error_msg)
        except Exception as e:
            logger.error(
                "Unexpected error fetching OAuth credentials",
                error=str(e),
                user_id=user_id,
            )
            raise AuthServiceError(f"Unexpected error: {str(e)}")


# Singleton instance for easy access
_auth_client_instance: Optional[AuthClient] = None


def get_auth_client() -> AuthClient:
    """
    Get a singleton instance of the auth client.

    Returns:
        AuthClient: The auth client instance
    """
    global _auth_client_instance
    if _auth_client_instance is None:
        _auth_client_instance = AuthClient()
    return _auth_client_instance


async def validate_bearer_token(bearer_token: str) -> Optional[Dict[str, Any]]:
    """
    Convenience function to validate a bearer token.

    Args:
        bearer_token: The bearer token to validate

    Returns:
        Dict containing user details if token is valid, None otherwise
    """
    async with get_auth_client() as client:
        return await client.validate_bearer_token(bearer_token)


async def get_oauth_credentials(
    user_id: str,
    mcp_id: str = "mcp123",
    tool_name: str = "google_calendar",
    provider: str = "google",
) -> Optional[Dict[str, Any]]:
    """
    Convenience function to get OAuth credentials for a user.

    Args:
        user_id: The user ID to get credentials for
        mcp_id: MCP identifier (default: "mcp123")
        tool_name: Tool name (default: "google_calendar")
        provider: OAuth provider (default: "google")

    Returns:
        Dict containing OAuth credentials if found, None otherwise
    """
    async with get_auth_client() as client:
        return await client.get_oauth_credentials(user_id, mcp_id, tool_name, provider)
