"""
Database models for the Trigger Service.

This module contains all SQLAlchemy models for the trigger service including
triggers, trigger executions, and related entities.
"""

from datetime import datetime
from typing import Dict, Any, List, Optional
from uuid import UUID, uuid4

from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    DateTime,
    Integer,
    String,
    Text,
    JSON,
    ForeignKey,
    Index,
    UniqueConstraint,
)
from sqlalchemy.dialects.postgresql import UUID as PostgreSQLUUID, ARRAY
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func

from src.database.connection import Base


class CalendarEvent(Base):
    """
    Model for storing Google Calendar events from webhook notifications.

    This model tracks calendar events received through webhook notifications,
    storing complete event data fetched from the Google Calendar API.
    """

    __tablename__ = "calendar_events"

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        doc="Unique identifier for the calendar event record",
    )

    # Webhook metadata
    channel_id: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        doc="Google Calendar webhook channel ID",
    )

    resource_id: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        doc="Google Calendar resource ID",
    )

    # User and event identification
    user_id: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        doc="ID of the user who owns the calendar",
    )

    event_id: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        doc="Google Calendar event ID",
    )

    calendar_id: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        doc="Google Calendar ID where the event belongs",
    )

    # Event data
    event_data: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        nullable=False,
        doc="Complete event data from Google Calendar API",
    )

    # Event metadata
    event_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        doc="Type of event change (created, updated, deleted)",
    )

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        doc="When this record was created",
    )

    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        doc="When this record was last updated",
    )

    # Google Calendar timestamps
    google_created_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the event was created in Google Calendar",
    )

    google_updated_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the event was last updated in Google Calendar",
    )

    # Indexes for efficient queries
    __table_args__ = (
        Index("idx_calendar_events_channel_id", "channel_id"),
        Index("idx_calendar_events_resource_id", "resource_id"),
        Index("idx_calendar_events_user_id", "user_id"),
        Index("idx_calendar_events_event_id", "event_id"),
        Index("idx_calendar_events_calendar_id", "calendar_id"),
        Index("idx_calendar_events_event_type", "event_type"),
        Index("idx_calendar_events_created_at", "created_at"),
        Index("idx_calendar_events_google_created_at", "google_created_at"),
        Index("idx_calendar_events_user_calendar", "user_id", "calendar_id"),
        UniqueConstraint(
            "user_id", "event_id", "calendar_id", name="uq_user_event_calendar"
        ),
    )


class Trigger(Base):
    """
    Model for storing trigger configurations.

    This model stores all information needed to configure and manage
    triggers for different external services.
    """

    __tablename__ = "triggers"

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        doc="Unique identifier for the trigger",
    )

    # User and workflow association
    user_id: Mapped[str] = mapped_column(
        String(255), nullable=False, doc="ID of the user who owns this trigger"
    )

    workflow_id: Mapped[str] = mapped_column(
        String(255), nullable=False, doc="ID of the workflow to execute when triggered"
    )

    # Trigger configuration
    trigger_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        doc="Type of trigger (e.g., 'google_calendar', 'slack')",
    )

    trigger_name: Mapped[str] = mapped_column(
        String(255), nullable=False, doc="Human-readable name for the trigger"
    )

    trigger_config: Mapped[Dict[str, Any]] = mapped_column(
        JSON, nullable=False, doc="Adapter-specific configuration as JSON"
    )

    event_types: Mapped[List[str]] = mapped_column(
        ARRAY(String), nullable=False, doc="List of event types to monitor"
    )

    # Google Calendar webhook channel ID for database-based storage
    channel_id: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        doc="Google Calendar webhook channel ID for database-based storage",
    )

    # Status and metadata
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether the trigger is currently active",
    )

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        doc="When the trigger was created",
    )

    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        doc="When the trigger was last updated",
    )

    last_triggered_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the trigger was last activated",
    )

    # Relationships
    executions: Mapped[List["TriggerExecution"]] = relationship(
        "TriggerExecution",
        back_populates="trigger",
        cascade="all, delete-orphan",
        doc="Execution history for this trigger",
    )

    # Constraints and indexes
    __table_args__ = (
        UniqueConstraint(
            "user_id",
            "workflow_id",
            "trigger_type",
            name="uq_user_workflow_trigger_type",
        ),
        Index("idx_triggers_user_id", "user_id"),
        Index("idx_triggers_workflow_id", "workflow_id"),
        Index("idx_triggers_type_active", "trigger_type", "is_active"),
        Index("idx_triggers_created_at", "created_at"),
        Index("idx_triggers_channel_id", "channel_id"),
    )

    def __repr__(self) -> str:
        """String representation of the trigger."""
        return (
            f"<Trigger(id={self.id}, name='{self.trigger_name}', "
            f"type='{self.trigger_type}', active={self.is_active})>"
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert trigger to dictionary representation."""
        return {
            "id": str(self.id),
            "user_id": self.user_id,
            "workflow_id": self.workflow_id,
            "trigger_type": self.trigger_type,
            "trigger_name": self.trigger_name,
            "trigger_config": self.trigger_config,
            "event_types": self.event_types,
            "channel_id": self.channel_id,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_triggered_at": (
                self.last_triggered_at.isoformat() if self.last_triggered_at else None
            ),
        }


class TriggerExecution(Base):
    """
    Model for storing trigger execution history.

    This model tracks each time a trigger is activated and the
    corresponding workflow execution details.
    """

    __tablename__ = "trigger_executions"

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        doc="Unique identifier for the execution",
    )

    # Foreign key to trigger
    trigger_id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("triggers.id", ondelete="CASCADE"),
        nullable=False,
        doc="ID of the trigger that was executed",
    )

    # Event and execution data
    event_data: Mapped[Dict[str, Any]] = mapped_column(
        JSON, nullable=False, doc="Event data that triggered the execution"
    )

    workflow_execution_id: Mapped[Optional[str]] = mapped_column(
        String(255), nullable=True, doc="Correlation ID from the workflow service"
    )

    # Execution status
    status: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        default="pending",
        doc="Execution status: pending, success, failed, retrying",
    )

    error_message: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True, doc="Error message if execution failed"
    )

    retry_count: Mapped[int] = mapped_column(
        Integer, default=0, nullable=False, doc="Number of retry attempts"
    )

    # Timestamps
    executed_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        doc="When the execution was initiated",
    )

    completed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the execution was completed (success or final failure)",
    )

    # Relationships
    trigger: Mapped["Trigger"] = relationship(
        "Trigger", back_populates="executions", doc="The trigger that was executed"
    )

    # Indexes
    __table_args__ = (
        Index("idx_trigger_executions_trigger_id", "trigger_id"),
        Index("idx_trigger_executions_status", "status"),
        Index("idx_trigger_executions_executed_at", "executed_at"),
        Index("idx_trigger_executions_workflow_id", "workflow_execution_id"),
    )

    def __repr__(self) -> str:
        """String representation of the execution."""
        return (
            f"<TriggerExecution(id={self.id}, trigger_id={self.trigger_id}, "
            f"status='{self.status}', retry_count={self.retry_count})>"
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert execution to dictionary representation."""
        return {
            "id": str(self.id),
            "trigger_id": str(self.trigger_id),
            "event_data": self.event_data,
            "workflow_execution_id": self.workflow_execution_id,
            "status": self.status,
            "error_message": self.error_message,
            "retry_count": self.retry_count,
            "executed_at": self.executed_at.isoformat() if self.executed_at else None,
            "completed_at": (
                self.completed_at.isoformat() if self.completed_at else None
            ),
        }

    @property
    def is_completed(self) -> bool:
        """Check if the execution is completed (success or final failure)."""
        return self.status in ("success", "failed") and self.completed_at is not None

    @property
    def is_retryable(self) -> bool:
        """Check if the execution can be retried."""
        from src.utils.config import get_settings

        settings = get_settings()
        return (
            self.status in ("failed", "retrying")
            and self.retry_count < settings.max_retry_attempts
        )


class Scheduler(Base):
    """
    Model for storing scheduler configurations.

    This model stores all information needed to configure and manage
    scheduled workflows.
    """

    __tablename__ = "schedulers"

    id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        doc="Unique identifier for the scheduler",
    )
    user_id: Mapped[str] = mapped_column(
        String(255), nullable=False, doc="ID of the user who owns this scheduler"
    )
    name: Mapped[str] = mapped_column(
        String(255), nullable=False, doc="Human-readable name for the scheduler"
    )
    description: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True, doc="Optional description for the scheduler"
    )
    schedule_type: Mapped[str] = mapped_column(
        String(50), nullable=False, doc="Type of schedule (cron, interval, one_time)"
    )
    schedule_config: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        nullable=False,
        doc="JSON configuration for the schedule (e.g., cron expression, interval)",
    )
    workflow_config: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        nullable=False,
        doc="JSON configuration for the workflow to be executed",
    )
    timezone: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        default="UTC",
        doc="Timezone for the scheduler (e.g., 'UTC', 'America/New_York')",
    )
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        doc="Whether the scheduler is currently active",
    )
    max_executions: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        doc="Optional maximum number of executions for this scheduler",
    )
    expires_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="Optional expiration date for the scheduler",
    )
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        doc="When the scheduler was created",
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        doc="When the scheduler was last updated",
    )
    last_run_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the scheduler was last executed",
    )
    next_run_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the scheduler is next scheduled to run",
    )
    workflow_id: Mapped[str] = mapped_column(
        String(255), nullable=False, doc="ID of the workflow to execute when scheduled"
    )
    scheduler_metadata: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON, nullable=True, doc="Optional metadata for the scheduler"
    )

    # Simplified scheduler fields
    frequency: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        default="daily",
        doc="Schedule frequency: every_minute, hourly, daily, weekly, monthly, custom",
    )
    time: Mapped[Optional[str]] = mapped_column(
        String(5),
        nullable=True,
        doc="Time in HH:mm format for daily/weekly/monthly schedules",
    )
    days_of_week: Mapped[Optional[List[str]]] = mapped_column(
        JSON,
        nullable=True,
        doc="JSON array of weekday strings for weekly schedules (e.g., ['monday', 'wednesday', 'friday'])",
    )
    days_of_month: Mapped[Optional[List[int]]] = mapped_column(
        JSON,
        nullable=True,
        doc="JSON array of integers 1-31 for monthly schedules (e.g., [1, 15, 30])",
    )
    cron_expression: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        doc="Cron expression for custom schedules (e.g., '0 9 * * 1-5')",
    )

    executions: Mapped[List["SchedulerExecution"]] = relationship(
        "SchedulerExecution",
        back_populates="scheduler",
        cascade="all, delete-orphan",
        doc="Execution history for this scheduler",
    )

    __table_args__ = (
        Index("idx_schedulers_user_id", "user_id"),
        Index("idx_schedulers_schedule_type", "schedule_type"),
        Index("idx_schedulers_is_active", "is_active"),
        Index("idx_schedulers_expires_at", "expires_at"),
        Index("idx_schedulers_frequency", "frequency"),
        Index("idx_schedulers_time", "time"),
        Index("idx_schedulers_frequency_active", "frequency", "is_active"),
    )

    def __repr__(self) -> str:
        return (
            f"<Scheduler(id={self.id}, name='{self.name}', "
            f"type='{self.schedule_type}', active={self.is_active})>"
        )

    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": str(self.id),
            "user_id": self.user_id,
            "name": self.name,
            "description": self.description,
            "schedule_type": self.schedule_type,
            "schedule_config": self.schedule_config,
            "workflow_config": self.workflow_config,
            "timezone": self.timezone,
            "is_active": self.is_active,
            "max_executions": self.max_executions,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "frequency": self.frequency,
            "time": self.time,
            "days_of_week": self.days_of_week,
            "days_of_month": self.days_of_month,
            "cron_expression": self.cron_expression,
        }


class SchedulerExecution(Base):
    """
    Model for storing scheduler execution history.

    This model tracks each time a scheduler is activated and the
    corresponding workflow execution details.
    """

    __tablename__ = "scheduler_executions"

    id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        doc="Unique identifier for the execution",
    )
    scheduler_id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("schedulers.id", ondelete="CASCADE"),
        nullable=False,
        doc="ID of the scheduler that was executed",
    )
    execution_time: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        doc="When the scheduler was executed",
    )
    status: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        default="pending",
        doc="Execution status: pending, running, completed, failed",
    )
    result: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON, nullable=True, doc="JSON result of the execution"
    )
    error_message: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True, doc="Error message if execution failed"
    )
    duration_ms: Mapped[Optional[int]] = mapped_column(
        Integer, nullable=True, doc="Duration of the execution in milliseconds"
    )
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        doc="When this execution record was created",
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        doc="When this execution record was last updated",
    )

    scheduler: Mapped["Scheduler"] = relationship(
        "Scheduler", back_populates="executions", doc="The scheduler that was executed"
    )

    __table_args__ = (
        Index("idx_scheduler_executions_scheduler_id", "scheduler_id"),
        Index("idx_scheduler_executions_status", "status"),
        Index("idx_scheduler_executions_execution_time", "execution_time"),
    )

    def __repr__(self) -> str:
        return (
            f"<SchedulerExecution(id={self.id}, scheduler_id={self.scheduler_id}, "
            f"status='{self.status}')>"
        )

    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": str(self.id),
            "scheduler_id": str(self.scheduler_id),
            "execution_time": (
                self.execution_time.isoformat() if self.execution_time else None
            ),
            "status": self.status,
            "result": self.result,
            "error_message": self.error_message,
            "duration_ms": self.duration_ms,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
