"""Add scheduler tables

Revision ID: 0004
Revises: 0003
Create Date: 2025-06-17 15:07:00.000000

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "0004"
down_revision = "0003"
branch_labels = None
depends_on = None


def upgrade() -> None:
    """
    Create scheduler and scheduler_executions tables.
    """
    op.create_table(
        "schedulers",
        sa.Column(
            "id",
            postgresql.UUID(as_uuid=True),
            primary_key=True,
            server_default=sa.text("gen_random_uuid()"),
            nullable=False,
            doc="Unique identifier for the scheduler",
        ),
        sa.Column(
            "user_id",
            sa.String(length=255),
            nullable=False,
            doc="ID of the user who owns this scheduler",
        ),
        sa.Column(
            "name",
            sa.String(length=255),
            nullable=False,
            doc="Human-readable name for the scheduler",
        ),
        sa.Column(
            "description",
            sa.Text(),
            nullable=True,
            doc="Optional description for the scheduler",
        ),
        sa.Column(
            "schedule_type",
            sa.String(length=50),
            nullable=False,
            doc="Type of schedule (cron, interval, one_time)",
        ),
        sa.Column(
            "schedule_config",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=False,
            doc="JSON configuration for the schedule (e.g., cron expression, interval)",
        ),
        sa.Column(
            "workflow_config",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=False,
            doc="JSON configuration for the workflow to be executed",
        ),
        sa.Column(
            "timezone",
            sa.String(length=255),
            nullable=False,
            server_default="UTC",
            doc="Timezone for the scheduler (e.g., 'UTC', 'America/New_York')",
        ),
        sa.Column(
            "is_active",
            sa.Boolean(),
            nullable=False,
            server_default=sa.text("true"),
            doc="Whether the scheduler is currently active",
        ),
        sa.Column(
            "max_executions",
            sa.Integer(),
            nullable=True,
            doc="Optional maximum number of executions for this scheduler",
        ),
        sa.Column(
            "expires_at",
            sa.DateTime(timezone=True),
            nullable=True,
            doc="Optional expiration date for the scheduler",
        ),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
            doc="When the scheduler was created",
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
            doc="When the scheduler was last updated",
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index("idx_schedulers_user_id", "schedulers", ["user_id"])
    op.create_index("idx_schedulers_schedule_type", "schedulers", ["schedule_type"])
    op.create_index("idx_schedulers_is_active", "schedulers", ["is_active"])
    op.create_index("idx_schedulers_expires_at", "schedulers", ["expires_at"])

    op.create_table(
        "scheduler_executions",
        sa.Column(
            "id",
            postgresql.UUID(as_uuid=True),
            primary_key=True,
            server_default=sa.text("gen_random_uuid()"),
            nullable=False,
            doc="Unique identifier for the scheduler execution",
        ),
        sa.Column(
            "scheduler_id",
            postgresql.UUID(as_uuid=True),
            sa.ForeignKey("schedulers.id", ondelete="CASCADE"),
            nullable=False,
            doc="ID of the scheduler that was executed",
        ),
        sa.Column(
            "execution_time",
            sa.DateTime(timezone=True),
            nullable=False,
            doc="When the scheduler was executed",
        ),
        sa.Column(
            "status",
            sa.String(length=50),
            nullable=False,
            server_default="pending",
            doc="Execution status: pending, running, completed, failed",
        ),
        sa.Column(
            "result",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
            doc="JSON result of the execution",
        ),
        sa.Column(
            "error_message",
            sa.Text(),
            nullable=True,
            doc="Error message if execution failed",
        ),
        sa.Column(
            "duration_ms",
            sa.Integer(),
            nullable=True,
            doc="Duration of the execution in milliseconds",
        ),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
            doc="When this execution record was created",
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
            doc="When this execution record was last updated",
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        "idx_scheduler_executions_scheduler_id",
        "scheduler_executions",
        ["scheduler_id"],
    )
    op.create_index(
        "idx_scheduler_executions_status", "scheduler_executions", ["status"]
    )
    op.create_index(
        "idx_scheduler_executions_execution_time",
        "scheduler_executions",
        ["execution_time"],
    )


def downgrade() -> None:
    """
    Drop scheduler_executions and schedulers tables.
    """
    op.drop_index(
        "idx_scheduler_executions_execution_time", table_name="scheduler_executions"
    )
    op.drop_index("idx_scheduler_executions_status", table_name="scheduler_executions")
    op.drop_index(
        "idx_scheduler_executions_scheduler_id", table_name="scheduler_executions"
    )
    op.drop_table("scheduler_executions")

    op.drop_index("idx_schedulers_expires_at", table_name="schedulers")
    op.drop_index("idx_schedulers_is_active", table_name="schedulers")
    op.drop_index("idx_schedulers_schedule_type", table_name="schedulers")
    op.drop_index("idx_schedulers_user_id", table_name="schedulers")
    op.drop_table("schedulers")
