"""Add simplified scheduler fields

Revision ID: 0005
Revises: 4a48125e7c31
Create Date: 2025-06-17 17:05:00.000000

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "0005"
down_revision = ("e92ada5c4aea", "4a48125e7c31")
branch_labels = None
depends_on = None


def upgrade() -> None:
    """
    Add simplified scheduler fields to the schedulers table.

    This migration adds the following fields for simplified scheduling:
    - frequency: Required field with enum values for schedule frequency
    - time: Optional field for HH:mm format time specification
    - days_of_week: Optional JSON array for weekly schedules
    - days_of_month: Optional JSON array for monthly schedules
    - cron_expression: Optional field for custom cron expressions
    """

    # Add frequency field with check constraint for valid enum values
    op.add_column(
        "schedulers",
        sa.Column(
            "frequency",
            sa.String(length=50),
            nullable=False,
            server_default="daily",
            doc="Schedule frequency: every_minute, hourly, daily, weekly, monthly, custom",
        ),
    )

    # Add check constraint for frequency enum values
    op.create_check_constraint(
        "ck_schedulers_frequency",
        "schedulers",
        "frequency IN ('every_minute', 'hourly', 'daily', 'weekly', 'monthly', 'custom')",
    )

    # Add time field for daily/weekly/monthly schedules (HH:mm format)
    op.add_column(
        "schedulers",
        sa.Column(
            "time",
            sa.String(length=5),
            nullable=True,
            doc="Time in HH:mm format for daily/weekly/monthly schedules",
        ),
    )

    # Add check constraint for time format validation (HH:mm)
    op.create_check_constraint(
        "ck_schedulers_time_format",
        "schedulers",
        "time IS NULL OR time ~ '^([0-1][0-9]|2[0-3]):[0-5][0-9]$'",
    )

    # Add days_of_week field for weekly schedules
    op.add_column(
        "schedulers",
        sa.Column(
            "days_of_week",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
            doc="JSON array of weekday strings for weekly schedules (e.g., ['monday', 'wednesday', 'friday'])",
        ),
    )

    # Add days_of_month field for monthly schedules
    op.add_column(
        "schedulers",
        sa.Column(
            "days_of_month",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
            doc="JSON array of integers 1-31 for monthly schedules (e.g., [1, 15, 30])",
        ),
    )

    # Add cron_expression field for custom schedules
    op.add_column(
        "schedulers",
        sa.Column(
            "cron_expression",
            sa.String(length=255),
            nullable=True,
            doc="Cron expression for custom schedules (e.g., '0 9 * * 1-5')",
        ),
    )

    # Create indexes for performance
    op.create_index("idx_schedulers_frequency", "schedulers", ["frequency"])
    op.create_index("idx_schedulers_time", "schedulers", ["time"])
    op.create_index(
        "idx_schedulers_frequency_active", "schedulers", ["frequency", "is_active"]
    )


def downgrade() -> None:
    """
    Remove simplified scheduler fields from the schedulers table.
    """
    # Drop indexes
    op.drop_index("idx_schedulers_frequency_active", table_name="schedulers")
    op.drop_index("idx_schedulers_time", table_name="schedulers")
    op.drop_index("idx_schedulers_frequency", table_name="schedulers")

    # Drop check constraints
    op.drop_constraint("ck_schedulers_time_format", "schedulers", type_="check")
    op.drop_constraint("ck_schedulers_frequency", "schedulers", type_="check")

    # Drop columns
    op.drop_column("schedulers", "cron_expression")
    op.drop_column("schedulers", "days_of_month")
    op.drop_column("schedulers", "days_of_week")
    op.drop_column("schedulers", "time")
    op.drop_column("schedulers", "frequency")
