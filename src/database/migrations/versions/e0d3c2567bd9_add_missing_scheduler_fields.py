"""Add missing scheduler fields

Revision ID: e0d3c2567bd9
Revises: 0004
Create Date: 2025-06-17 16:06:35.612910

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'e0d3c2567bd9'
down_revision: Union[str, None] = '0004'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade database schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('scheduler_executions', 'id',
               existing_type=sa.UUID(),
               server_default=None,
               existing_nullable=False)
    op.alter_column('scheduler_executions', 'status',
               existing_type=sa.VARCHAR(length=50),
               server_default=None,
               existing_nullable=False)
    op.alter_column('scheduler_executions', 'result',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
    op.add_column('schedulers', sa.Column('last_run_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('schedulers', sa.Column('next_run_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('schedulers', sa.Column('workflow_id', sa.String(length=255), nullable=False))
    op.add_column('schedulers', sa.Column('scheduler_metadata', sa.JSON(), nullable=True))
    op.alter_column('schedulers', 'id',
               existing_type=sa.UUID(),
               server_default=None,
               existing_nullable=False)
    op.alter_column('schedulers', 'schedule_config',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=False)
    op.alter_column('schedulers', 'workflow_config',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=False)
    op.alter_column('schedulers', 'timezone',
               existing_type=sa.VARCHAR(length=255),
               server_default=None,
               existing_nullable=False)
    op.alter_column('schedulers', 'is_active',
               existing_type=sa.BOOLEAN(),
               server_default=None,
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade database schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('schedulers', 'is_active',
               existing_type=sa.BOOLEAN(),
               server_default=sa.text('true'),
               existing_nullable=False)
    op.alter_column('schedulers', 'timezone',
               existing_type=sa.VARCHAR(length=255),
               server_default=sa.text("'UTC'::character varying"),
               existing_nullable=False)
    op.alter_column('schedulers', 'workflow_config',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=False)
    op.alter_column('schedulers', 'schedule_config',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=False)
    op.alter_column('schedulers', 'id',
               existing_type=sa.UUID(),
               server_default=sa.text('gen_random_uuid()'),
               existing_nullable=False)
    op.drop_column('schedulers', 'scheduler_metadata')
    op.drop_column('schedulers', 'workflow_id')
    op.drop_column('schedulers', 'next_run_at')
    op.drop_column('schedulers', 'last_run_at')
    op.alter_column('scheduler_executions', 'result',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.alter_column('scheduler_executions', 'status',
               existing_type=sa.VARCHAR(length=50),
               server_default=sa.text("'pending'::character varying"),
               existing_nullable=False)
    op.alter_column('scheduler_executions', 'id',
               existing_type=sa.UUID(),
               server_default=sa.text('gen_random_uuid()'),
               existing_nullable=False)
    # ### end Alembic commands ###
