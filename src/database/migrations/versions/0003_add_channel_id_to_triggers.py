"""Add channel_id field to triggers table

Revision ID: 0003
Revises: 0002
Create Date: 2025-01-15 19:55:09.000000

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "0003"
down_revision = "0002"
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add channel_id field to triggers table for database-based webhook channel storage."""
    # Add channel_id column to triggers table
    op.add_column(
        "triggers", sa.Column("channel_id", sa.String(length=255), nullable=True)
    )

    # Add index for efficient channel_id lookups
    op.create_index("idx_triggers_channel_id", "triggers", ["channel_id"])


def downgrade() -> None:
    """Remove channel_id field from triggers table."""
    # Drop index first
    op.drop_index("idx_triggers_channel_id", table_name="triggers")

    # Drop column
    op.drop_column("triggers", "channel_id")
