"""Manually add missing scheduler fields

Revision ID: 4578488482b4
Revises: 29fa012cddfd
Create Date: 2025-06-17 16:19:01.972053

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4578488482b4'
down_revision: Union[str, None] = '29fa012cddfd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade database schema."""
    pass


def downgrade() -> None:
    """Downgrade database schema."""
    pass
