"""Add calendar_events table for webhook event storage

Revision ID: 0002
Revises: 0001
Create Date: 2024-01-02 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '0002'
down_revision: Union[str, None] = '0001'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade database schema."""
    # Create calendar_events table
    op.create_table(
        'calendar_events',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False, default=sa.text('gen_random_uuid()')),
        sa.Column('channel_id', sa.String(length=255), nullable=False),
        sa.Column('resource_id', sa.String(length=255), nullable=False),
        sa.Column('user_id', sa.String(length=255), nullable=False),
        sa.Column('event_id', sa.String(length=255), nullable=False),
        sa.Column('calendar_id', sa.String(length=255), nullable=False),
        sa.Column('event_data', sa.JSON(), nullable=False),
        sa.Column('event_type', sa.String(length=50), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('google_created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('google_updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('user_id', 'event_id', 'calendar_id', name='uq_user_event_calendar')
    )
    
    # Create indexes for calendar_events table
    op.create_index('idx_calendar_events_channel_id', 'calendar_events', ['channel_id'])
    op.create_index('idx_calendar_events_resource_id', 'calendar_events', ['resource_id'])
    op.create_index('idx_calendar_events_user_id', 'calendar_events', ['user_id'])
    op.create_index('idx_calendar_events_event_id', 'calendar_events', ['event_id'])
    op.create_index('idx_calendar_events_calendar_id', 'calendar_events', ['calendar_id'])
    op.create_index('idx_calendar_events_event_type', 'calendar_events', ['event_type'])
    op.create_index('idx_calendar_events_created_at', 'calendar_events', ['created_at'])
    op.create_index('idx_calendar_events_google_created_at', 'calendar_events', ['google_created_at'])
    op.create_index('idx_calendar_events_user_calendar', 'calendar_events', ['user_id', 'calendar_id'])
    
    # Create trigger to automatically update updated_at for calendar_events
    op.execute("""
        CREATE TRIGGER update_calendar_events_updated_at
            BEFORE UPDATE ON calendar_events
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    """)


def downgrade() -> None:
    """Downgrade database schema."""
    # Drop trigger
    op.execute("DROP TRIGGER IF EXISTS update_calendar_events_updated_at ON calendar_events;")
    
    # Drop indexes for calendar_events
    op.drop_index('idx_calendar_events_user_calendar', table_name='calendar_events')
    op.drop_index('idx_calendar_events_google_created_at', table_name='calendar_events')
    op.drop_index('idx_calendar_events_created_at', table_name='calendar_events')
    op.drop_index('idx_calendar_events_event_type', table_name='calendar_events')
    op.drop_index('idx_calendar_events_calendar_id', table_name='calendar_events')
    op.drop_index('idx_calendar_events_event_id', table_name='calendar_events')
    op.drop_index('idx_calendar_events_user_id', table_name='calendar_events')
    op.drop_index('idx_calendar_events_resource_id', table_name='calendar_events')
    op.drop_index('idx_calendar_events_channel_id', table_name='calendar_events')
    
    # Drop calendar_events table
    op.drop_table('calendar_events')
