"""Manually add missing scheduler fields

Revision ID: 4a48125e7c31
Revises: 4578488482b4
Create Date: 2025-06-17 16:19:07.062426

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision: str = "4a48125e7c31"
down_revision: Union[str, None] = "4578488482b4"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade database schema."""
    # Check if column already exists before adding it
    connection = op.get_bind()
    inspector = sa.inspect(connection)
    columns = [col["name"] for col in inspector.get_columns("schedulers")]

    if "scheduler_metadata" not in columns:
        op.add_column(
            "schedulers",
            sa.Column(
                "scheduler_metadata",
                postgresql.JSONB(astext_type=sa.Text()),
                nullable=True,
                doc="Optional metadata for the scheduler",
            ),
        )


def downgrade() -> None:
    """Downgrade database schema."""
    op.drop_column("schedulers", "scheduler_metadata")
