from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from src.core.scheduler_manager import SchedulerManager
from src.database.connection import get_async_session
from src.schemas.scheduler import (
    SimplifiedSchedulerCreate,
    SimplifiedSchedulerUpdate,
    SimplifiedSchedulerResponse,
)
from src.api.middleware.auth import get_authenticated_user
from src.schemas.workflow import WorkflowResponse
from src.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1/schedulers", tags=["schedulers"])


@router.post(
    "/schedulers",
    response_model=SimplifiedSchedulerResponse,
    status_code=status.HTTP_201_CREATED,
)
async def create_scheduler(
    scheduler_create: SimplifiedSchedulerCreate,
    db: AsyncSession = Depends(get_async_session),
    user: dict = Depends(get_authenticated_user),
):
    """
    Create a new scheduler using simplified schema.

    **Frequency Types:**
    - `every_minute`: Execute every minute
    - `hourly`: Execute every hour
    - `daily`: Execute daily at specified time (requires `time`)
    - `weekly`: Execute weekly on specified days and time (requires `time` and `days_of_week`)
    - `monthly`: Execute monthly on specified days and time (requires `time` and `days_of_month`)
    - `custom`: Execute using custom cron expression (requires `cron_expression`)

    **Example Request Body (Every Minute):**
    ```json
    {
      "name": "Every Minute Task",
      "workflow_id": "wkf_12345",
      "frequency": "every_minute",
      "timezone": "UTC",
      "is_active": true
    }
    ```

    **Example Request Body (Hourly):**
    ```json
    {
      "name": "Hourly Data Sync",
      "workflow_id": "wkf_23456",
      "frequency": "hourly",
      "timezone": "America/New_York",
      "is_active": true
    }
    ```

    **Example Request Body (Daily Schedule):**
    ```json
    {
      "name": "Daily Report Scheduler",
      "workflow_id": "wkf_34567",
      "frequency": "daily",
      "time": "09:00",
      "timezone": "America/New_York",
      "is_active": true,
      "scheduler_metadata": {
        "department": "finance",
        "report_type": "daily_summary"
      }
    }
    ```

    **Example Request Body (Weekly Schedule):**
    ```json
    {
      "name": "Weekly Report Scheduler",
      "workflow_id": "wkf_45678",
      "frequency": "weekly",
      "time": "10:00",
      "days_of_week": ["Monday", "Friday"],
      "timezone": "UTC",
      "is_active": true
    }
    ```

    **Example Request Body (Monthly Schedule):**
    ```json
    {
      "name": "Monthly Invoice Generator",
      "workflow_id": "wkf_56789",
      "frequency": "monthly",
      "time": "08:00",
      "days_of_month": [1, 15],
      "timezone": "Asia/Kolkata",
      "is_active": true
    }
    ```

    **Example Request Body (Custom Cron Schedule):**
    ```json
    {
      "name": "Custom Cron Scheduler",
      "workflow_id": "wkf_67890",
      "frequency": "custom",
      "cron_expression": "0 9 * * 1-5",
      "timezone": "UTC",
      "is_active": true
    }
    ```

    **Field Requirements by Frequency:**
    - `every_minute`, `hourly`: Only `name`, `workflow_id`, `frequency`, `timezone`, `is_active`
    - `daily`: Requires `time` (HH:mm format)
    - `weekly`: Requires `time` and `days_of_week` (array of weekday names)
    - `monthly`: Requires `time` and `days_of_month` (array of 1-31)
    - `custom`: Requires `cron_expression` (5-part cron format)
    """
    try:
        # Validate user authentication and extract user ID
        if not user or "id" not in user or not user["id"]:
            logger.error("User authentication failed", user_data=user)
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not authenticated or user ID missing.",
            )

        user_id = user["id"]
        logger.info("Creating scheduler", user_id=user_id, name=scheduler_create.name)

        scheduler_manager = SchedulerManager(db)
        scheduler = await scheduler_manager.create(scheduler_create, user_id)
        return scheduler
    except HTTPException:
        raise  # Re-raise HTTPException directly
    except Exception as e:
        logger.error(f"Error creating scheduler: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create scheduler: {e}",
        )


@router.get("/schedulers", response_model=List[SimplifiedSchedulerResponse])
async def list_schedulers(
    db: AsyncSession = Depends(get_async_session),
    user: dict = Depends(get_authenticated_user),
):
    """
    List all schedulers for the authenticated user using simplified schema.

    **Example Response:**
    ```json
    [
      {
        "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
        "user_id": "user_123",
        "name": "Every Minute Task",
        "workflow_id": "wkf_12345",
        "frequency": "every_minute",
        "time": null,
        "days_of_week": null,
        "days_of_month": null,
        "cron_expression": null,
        "timezone": "UTC",
        "is_active": true,
        "scheduler_metadata": null,
        "created_at": "2025-01-17T10:00:00.000Z",
        "updated_at": "2025-01-17T10:00:00.000Z",
        "last_run_at": "2025-01-17T10:05:00.000Z",
        "next_run_at": "2025-01-17T10:06:00.000Z"
      },
      {
        "id": "b2c3d4e5-f6g7-8901-2345-678901bcdefg",
        "user_id": "user_123",
        "name": "Daily Report Scheduler",
        "workflow_id": "wkf_23456",
        "frequency": "daily",
        "time": "09:00",
        "days_of_week": null,
        "days_of_month": null,
        "cron_expression": null,
        "timezone": "America/New_York",
        "is_active": true,
        "scheduler_metadata": {
          "department": "finance",
          "report_type": "daily_summary"
        },
        "created_at": "2025-01-17T08:00:00.000Z",
        "updated_at": "2025-01-17T08:00:00.000Z",
        "last_run_at": "2025-01-17T09:00:00.000Z",
        "next_run_at": "2025-01-18T09:00:00.000Z"
      },
      {
        "id": "c3d4e5f6-g7h8-9012-3456-789012cdefgh",
        "user_id": "user_123",
        "name": "Weekly Report Scheduler",
        "workflow_id": "wkf_34567",
        "frequency": "weekly",
        "time": "10:00",
        "days_of_week": ["Monday", "Friday"],
        "days_of_month": null,
        "cron_expression": null,
        "timezone": "UTC",
        "is_active": true,
        "scheduler_metadata": null,
        "created_at": "2025-01-17T07:00:00.000Z",
        "updated_at": "2025-01-17T07:00:00.000Z",
        "last_run_at": "2025-01-17T10:00:00.000Z",
        "next_run_at": "2025-01-20T10:00:00.000Z"
      },
      {
        "id": "d4e5f6g7-h8i9-0123-4567-890123defghi",
        "user_id": "user_123",
        "name": "Monthly Invoice Generator",
        "workflow_id": "wkf_45678",
        "frequency": "monthly",
        "time": "08:00",
        "days_of_week": null,
        "days_of_month": [1, 15],
        "cron_expression": null,
        "timezone": "Asia/Kolkata",
        "is_active": true,
        "scheduler_metadata": {
          "invoice_type": "monthly_billing"
        },
        "created_at": "2025-01-17T06:00:00.000Z",
        "updated_at": "2025-01-17T06:00:00.000Z",
        "last_run_at": "2025-01-15T08:00:00.000Z",
        "next_run_at": "2025-02-01T08:00:00.000Z"
      },
      {
        "id": "e5f6g7h8-i9j0-1234-5678-901234efghij",
        "user_id": "user_123",
        "name": "Custom Cron Scheduler",
        "workflow_id": "wkf_56789",
        "frequency": "custom",
        "time": null,
        "days_of_week": null,
        "days_of_month": null,
        "cron_expression": "0 9 * * 1-5",
        "timezone": "UTC",
        "is_active": true,
        "scheduler_metadata": null,
        "created_at": "2025-01-17T05:00:00.000Z",
        "updated_at": "2025-01-17T05:00:00.000Z",
        "last_run_at": "2025-01-17T09:00:00.000Z",
        "next_run_at": "2025-01-20T09:00:00.000Z"
      }
    ]
    ```
    """
    logger.info("Listing schedulers", user_id=user["id"])
    scheduler_manager = SchedulerManager(db)
    user_schedulers = await scheduler_manager.list(user["id"])
    return user_schedulers


@router.get("/schedulers/{scheduler_id}", response_model=SimplifiedSchedulerResponse)
async def get_scheduler(
    scheduler_id: str,
    db: AsyncSession = Depends(get_async_session),
    user: dict = Depends(get_authenticated_user),
):
    """
    Get a specific scheduler by ID using simplified schema.

    **Example Response (Weekly Scheduler):**
    ```json
    {
      "id": "c3d4e5f6-g7h8-9012-3456-789012cdefgh",
      "user_id": "user_123",
      "name": "Weekly Report Scheduler",
      "workflow_id": "wkf_34567",
      "frequency": "weekly",
      "time": "10:00",
      "days_of_week": ["Monday", "Friday"],
      "days_of_month": null,
      "cron_expression": null,
      "timezone": "UTC",
      "is_active": true,
      "scheduler_metadata": {
        "report_type": "weekly_summary",
        "recipients": ["<EMAIL>"]
      },
      "created_at": "2025-01-17T07:00:00.000Z",
      "updated_at": "2025-01-17T07:00:00.000Z",
      "last_run_at": "2025-01-17T10:00:00.000Z",
      "next_run_at": "2025-01-20T10:00:00.000Z"
    }
    ```

    **Note:** Only fields relevant to the frequency type will have values.
    For example, a daily scheduler will have `time` but `days_of_week`,
    `days_of_month`, and `cron_expression` will be null.
    """
    logger.info("Getting scheduler", user_id=user["id"], scheduler_id=scheduler_id)
    scheduler_manager = SchedulerManager(db)
    scheduler = await scheduler_manager.get(scheduler_id, user["id"])
    if not scheduler:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Scheduler not found"
        )
    return scheduler


@router.put("/schedulers/{scheduler_id}", response_model=SimplifiedSchedulerResponse)
async def update_scheduler(
    scheduler_id: str,
    scheduler_update: SimplifiedSchedulerUpdate,
    db: AsyncSession = Depends(get_async_session),
    user: dict = Depends(get_authenticated_user),
):
    """
    Update an existing scheduler using simplified schema.

    **Note:** All fields are optional in update requests. Only provided fields will be updated.

    **Example Request Body (Update Time and Status):**
    ```json
    {
      "name": "Updated Daily Report Scheduler",
      "is_active": false,
      "time": "10:00",
      "timezone": "America/New_York"
    }
    ```

    **Example Request Body (Change Frequency from Daily to Weekly):**
    ```json
    {
      "frequency": "weekly",
      "time": "09:00",
      "days_of_week": ["Monday", "Wednesday", "Friday"]
    }
    ```

    **Example Request Body (Update Monthly Days):**
    ```json
    {
      "days_of_month": [1, 10, 20],
      "time": "08:30"
    }
    ```

    **Example Response:**
    ```json
    {
      "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
      "user_id": "user_123",
      "name": "Updated Daily Report Scheduler",
      "workflow_id": "wkf_12345",
      "frequency": "daily",
      "time": "10:00",
      "days_of_week": null,
      "days_of_month": null,
      "cron_expression": null,
      "timezone": "America/New_York",
      "is_active": false,
      "scheduler_metadata": {
        "department": "finance",
        "report_type": "daily_summary"
      },
      "created_at": "2025-01-17T10:00:00.000Z",
      "updated_at": "2025-01-17T11:00:00.000Z",
      "last_run_at": "2025-01-17T09:00:00.000Z",
      "next_run_at": "2025-01-18T10:00:00.000Z"
    }
    ```

    **Important:** When changing frequency type, ensure you provide the required fields:
    - `daily`: requires `time`
    - `weekly`: requires `time` and `days_of_week`
    - `monthly`: requires `time` and `days_of_month`
    - `custom`: requires `cron_expression`
    """
    logger.info("Updating scheduler", user_id=user["id"], scheduler_id=scheduler_id)
    scheduler_manager = SchedulerManager(db)

    try:
        updated_scheduler = await scheduler_manager.update(
            scheduler_id, scheduler_update, user["id"]
        )
        if not updated_scheduler:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Scheduler not found"
            )
        return updated_scheduler
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.delete("/schedulers/{scheduler_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_scheduler(
    scheduler_id: str,
    db: AsyncSession = Depends(get_async_session),
    user: dict = Depends(get_authenticated_user),
):
    """
    Delete a scheduler.

    **Success Response:** HTTP 204 No Content

    **Error Responses:**
    - HTTP 404 Not Found: Scheduler not found or doesn't belong to user
    - HTTP 401 Unauthorized: User not authenticated

    **Note:** This operation is irreversible. The scheduler and all its execution history will be permanently deleted.
    """
    logger.info("Deleting scheduler", user_id=user["id"], scheduler_id=scheduler_id)
    scheduler_manager = SchedulerManager(db)

    success = await scheduler_manager.delete(scheduler_id, user["id"])
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Scheduler not found"
        )
    return {"message": "Scheduler deleted successfully"}
