"""
API middleware package for the Trigger Service.

This package contains all middleware components for request processing
including authentication, logging, error handling, and correlation tracking.
"""

from .error_handler import ErrorHandlerMiddleware
from .correlation import CorrelationMiddleware, get_correlation_id

__all__ = [
    "ErrorHandlerMiddleware",
    "CorrelationMiddleware",
    "get_correlation_id",
]
