"""
Authentication middleware for validating bearer tokens.

This module provides middleware to validate bearer tokens and extract user information
from the external authentication service.
"""

from typing import Optional, Dict, Any
from fastapi import HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from src.utils.auth_client import validate_bearer_token, AuthServiceError
from src.utils.logger import get_logger

logger = get_logger(__name__)

# HTTP Bearer token scheme
bearer_scheme = HTTPBearer(auto_error=False)


class AuthenticationError(Exception):
    """Authentication-related errors."""

    pass


async def get_current_user(request: Request) -> Dict[str, Any]:
    """
    Extract and validate the current user from the bearer token.

    Args:
        request: FastAPI request object

    Returns:
        Dict containing user information

    Raises:
        HTTPException: If authentication fails
    """
    try:
        # Extract Authorization header
        authorization = request.headers.get("Authorization")

        if not authorization:
            logger.warning("No Authorization header provided")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authorization header required",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Check if it's a Bearer token
        if not authorization.startswith("Bearer "):
            logger.warning(
                "Invalid authorization scheme", authorization_header=authorization[:20]
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authorization scheme. Use 'Bearer <token>'",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Extract the token
        token = authorization[7:]  # Remove "Bearer " prefix

        if not token:
            logger.warning("Empty bearer token provided")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Bearer token is required",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Validate token with auth service
        try:
            user_data = await validate_bearer_token(token)

            if not user_data:
                logger.warning("Invalid bearer token")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid or expired token",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            # Ensure we have required user fields
            if not user_data.get("id"):
                logger.error(
                    "User data missing required 'id' field", user_data=user_data
                )
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Invalid user data from authentication service",
                )

            logger.debug(
                "Successfully authenticated user",
                user_id=user_data.get("id"),
                email=user_data.get("email"),
            )

            return user_data

        except AuthServiceError as e:
            logger.error("Auth service error during token validation", error=str(e))
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Authentication service temporarily unavailable",
            )

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error("Unexpected error during authentication", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal authentication error",
        )


async def get_current_user_optional(request: Request) -> Optional[Dict[str, Any]]:
    """
    Extract and validate the current user from the bearer token, but don't require authentication.

    Args:
        request: FastAPI request object

    Returns:
        Dict containing user information if authenticated, None otherwise
    """
    try:
        return await get_current_user(request)
    except HTTPException:
        # Return None if authentication fails instead of raising an exception
        return None
    except Exception as e:
        logger.warning("Error during optional authentication", error=str(e))
        return None


def require_auth(request: Request) -> Dict[str, Any]:
    """
    Dependency function to require authentication for an endpoint.

    Args:
        request: FastAPI request object

    Returns:
        Dict containing user information

    Raises:
        HTTPException: If authentication fails
    """
    # This is a synchronous wrapper that will be used with Depends()
    # The actual async work is done in the endpoint
    return request


async def extract_user_id_from_request(request: Request) -> str:
    """
    Extract user ID from authenticated request.

    Args:
        request: FastAPI request object

    Returns:
        str: User ID

    Raises:
        HTTPException: If authentication fails or user ID not found
    """
    user_data = await get_current_user(request)
    user_id = user_data.get("id")

    if not user_id:
        logger.error(
            "User ID not found in authenticated user data", user_data=user_data
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="User ID not available",
        )

    return user_id


def create_auth_dependency():
    """
    Create an authentication dependency that can be used with FastAPI Depends().

    Returns:
        Callable that validates authentication and returns user data
    """

    async def auth_dependency(request: Request) -> Dict[str, Any]:
        return await get_current_user(request)

    return auth_dependency


# Create the default auth dependency
get_authenticated_user = create_auth_dependency()
