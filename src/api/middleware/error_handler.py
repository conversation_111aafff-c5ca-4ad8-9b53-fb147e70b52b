"""
Global error handler middleware for the Trigger Service.

This module provides centralized error handling and response formatting
for all API endpoints.
"""

import traceback
from typing import Dict, Any, Optional
from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
from pydantic import ValidationError


class ErrorHandlerMiddleware(BaseHTTPMiddleware):
    """
    Global error handler middleware for consistent error responses.

    This middleware catches all unhandled exceptions and formats them
    into consistent JSON responses with proper logging.
    """

    async def dispatch(self, request: Request, call_next) -> Response:
        """
        Process the request and handle any exceptions.

        Args:
            request: Incoming HTTP request
            call_next: Next middleware or endpoint handler

        Returns:
            Response: HTTP response with error handling
        """
        try:
            response = await call_next(request)
            return response

        except HTTPException as e:
            # FastAPI HTTPExceptions are already properly formatted

            return JSONResponse(
                status_code=e.status_code,
                content=self._format_error_response(
                    error_type="HTTPException",
                    message=e.detail,
                    status_code=e.status_code,
                ),
            )

        except ValidationError as e:
            # Pydantic validation errors

            return JSONResponse(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                content=self._format_validation_error_response(e),
            )

        except Exception as e:
            # Unhandled exceptions
            error_id = self._log_unhandled_exception(e, request)

            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content=self._format_error_response(
                    error_type="InternalServerError",
                    message="An internal server error occurred",
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    error_id=error_id,
                ),
            )

    def _format_error_response(
        self,
        error_type: str,
        message: str,
        status_code: int,
        error_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Format a consistent error response.

        Args:
            error_type: Type of error
            message: Error message
            status_code: HTTP status code
            error_id: Unique error identifier for tracking
            details: Additional error details

        Returns:
            Dict[str, Any]: Formatted error response
        """
        response = {
            "error": {
                "type": error_type,
                "message": message,
                "status_code": status_code,
            }
        }

        if error_id:
            response["error"]["error_id"] = error_id

        if details:
            response["error"]["details"] = details

        return response

    def _format_validation_error_response(
        self, validation_error: ValidationError
    ) -> Dict[str, Any]:
        """
        Format validation error response with field details.

        Args:
            validation_error: Pydantic validation error

        Returns:
            Dict[str, Any]: Formatted validation error response
        """
        errors = []
        for error in validation_error.errors():
            errors.append(
                {
                    "field": ".".join(str(loc) for loc in error["loc"]),
                    "message": error["msg"],
                    "type": error["type"],
                }
            )

        return self._format_error_response(
            error_type="ValidationError",
            message="Request validation failed",
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details={"validation_errors": errors},
        )

    def _log_unhandled_exception(self, exception: Exception, request: Request) -> str:
        """
        Log unhandled exception with full context.

        Args:
            exception: The unhandled exception
            request: HTTP request that caused the exception

        Returns:
            str: Unique error ID for tracking
        """
        import uuid

        error_id = str(uuid.uuid4())

        return error_id
