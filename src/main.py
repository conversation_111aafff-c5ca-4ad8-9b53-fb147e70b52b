"""
Main application entry point for the Trigger Service.

This module initializes and configures the FastAPI application with all
necessary middleware, routes, and startup/shutdown events.
"""

import uvicorn
from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, APIKeyHeader
from contextlib import asynccontextmanager

from src.utils.config import get_settings

import asyncio
from src.database.connection import init_database, close_database, get_async_session
from src.api.routes import triggers, webhooks, health, google_calendar, schedulers
from src.api.middleware.error_handler import ErrorHandlerMiddleware
from src.api.middleware.correlation import CorrelationMiddleware
from src.core.scheduler_engine import SchedulerEngine
from src.core.workflow_executor import WorkflowExecutor
from src.utils.logger import get_logger

logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager for startup and shutdown events.

    Args:
        app: FastAPI application instance
    """
    # Startup
    try:
        await init_database()

        # Load existing webhook subscriptions
        await load_existing_webhook_subscriptions()

        # Start scheduler engine in a background task
        app.state.scheduler_task = asyncio.create_task(run_scheduler_engine())
        logger.info("Scheduler engine background task started.")

        yield
    finally:
        # Shutdown
        if hasattr(app.state, "scheduler_task"):
            app.state.scheduler_task.cancel()
            logger.info("Scheduler engine background task cancelled.")
        await close_database()


async def run_scheduler_engine():
    """
    Background task to run the scheduler engine periodically.
    """
    from src.core.scheduler_service import get_scheduler_service

    try:
        # Get the scheduler service instance
        scheduler_service = await get_scheduler_service()

        # Start the service
        await scheduler_service.start()
        logger.info("Scheduler service started successfully")

        # Run the scheduler loop
        while True:
            try:
                await scheduler_service.run_scheduler_cycle()
                await asyncio.sleep(
                    30
                )  # Run every 30 seconds to catch minute-level schedules
            except Exception as e:
                logger.error(f"Error in scheduler cycle: {e}")
                await asyncio.sleep(5)  # Wait before retrying

    except Exception as e:
        logger.error(f"Error in scheduler engine background task: {e}")
        # Fallback to simple implementation for backward compatibility
        await run_simple_scheduler_engine()


async def run_simple_scheduler_engine():
    """
    Fallback simple scheduler implementation for backward compatibility.
    """
    while True:
        try:
            async for db_session in get_async_session():
                # Use the old method name for backward compatibility
                from src.core.scheduler_manager import SchedulerManager

                scheduler_manager = SchedulerManager(db_session)

                # Get due schedulers and process them
                due_schedulers = await scheduler_manager.get_due_schedulers(limit=50)

                for scheduler in due_schedulers:
                    try:
                        # Mark as processing
                        await scheduler_manager.mark_scheduler_processing(scheduler.id)

                        # Execute workflow (simplified)
                        workflow_executor = WorkflowExecutor(db_session)
                        result = await workflow_executor.execute_workflow(
                            scheduler.workflow_id,
                            scheduler.user_id,
                            {"scheduler_id": scheduler.id},
                        )

                        # Update next run time
                        await scheduler_manager.update_scheduler_next_run(
                            scheduler.id, scheduler.schedule
                        )

                        logger.debug(f"Processed scheduler {scheduler.id}")

                    except Exception as e:
                        logger.error(f"Error processing scheduler {scheduler.id}: {e}")
                        # Reset processing status on error
                        await scheduler_manager.reset_scheduler_processing(scheduler.id)

                break  # Only process once per iteration
        except Exception as e:
            logger.error(f"Error in simple scheduler engine: {e}")
        await asyncio.sleep(30)


async def load_existing_webhook_subscriptions():
    """Load existing webhook subscriptions and register them with adapters."""
    try:
        import json
        import os
        from datetime import datetime, timezone
        from uuid import uuid4
        from src.adapters.base import TriggerEventType

        # Check if webhook subscription file exists
        subscription_file = "webhook_subscription.json"
        if not os.path.exists(subscription_file):
            return

        # Load subscription info
        with open(subscription_file, "r") as f:
            subscription_info = json.load(f)

        # Check if subscription is still valid
        expires_at = datetime.fromisoformat(
            subscription_info["expires_at"].replace("Z", "+00:00")
        )
        now = datetime.now(timezone.utc)

        if expires_at <= now:
            return

        # Get the Google Calendar adapter from the trigger manager
        from src.api.routes.webhooks import get_trigger_manager

        trigger_manager = get_trigger_manager()

        # Find the Google Calendar adapter
        google_calendar_adapter = None
        for adapter_name, adapter in trigger_manager.adapters.items():
            if adapter_name == "google_calendar":
                google_calendar_adapter = adapter
                break

        if not google_calendar_adapter:
            return

        # Register the channel with the adapter
        channel_id = subscription_info["channel_id"]
        resource_id = subscription_info["resource_id"]
        calendar_id = subscription_info["calendar_id"]
        expiration_ms = int(expires_at.timestamp() * 1000)

        google_calendar_adapter._active_channels[channel_id] = {
            "resource_id": resource_id,
            "expiration": expiration_ms,
            "trigger_id": uuid4(),  # Generate a dummy trigger ID
            "user_id": "test-user",  # Default user ID
            "calendar_id": calendar_id,
            "event_types": [
                TriggerEventType.CREATED,
                TriggerEventType.UPDATED,
                TriggerEventType.DELETED,
            ],
            "created_at": datetime.now(timezone.utc).timestamp(),
        }

    except Exception:
        pass


def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.

    Returns:
        FastAPI: Configured application instance
    """
    settings = get_settings()

    # Create FastAPI app with lifespan and security schemes
    app = FastAPI(
        title="Workflow Automation Platform - Trigger Service",
        description="""
        A comprehensive trigger service for workflow automation platform.

        This service manages triggers that monitor external events and initiate workflows.
        It supports multiple adapters including Google Calendar, webhooks, and more.

        ## Features
        - **Multi-user Google Calendar integration** with webhook and polling support
        - **Real-time webhook processing** with signature verification
        - **Polling-based event monitoring** as fallback
        - **Dead letter queue** for failed events with retry capabilities
        - **Multi-user credential management** with caching
        - **Webhook channel lifecycle management** with automatic cleanup
        - **Comprehensive metrics and monitoring**

        ## Authentication
        This API supports two authentication methods:
        1. **Bearer Token**: Use `Authorization: Bearer <token>` header
        2. **API Key**: Use `X-API-Key: <key>` header

        Both methods are supported on all protected endpoints. Use the "Authorize" button
        in Swagger UI to test with either method.

        ## Google Calendar Integration
        The Google Calendar adapter supports:
        - **Multi-user webhook subscriptions** with automatic user routing
        - **Polling mode fallback** when webhooks are not available
        - **Per-user credential management** with token refresh
        - **Channel expiration handling** and cleanup
        - **Signature verification** for webhook security
        """,
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        lifespan=lifespan,
        debug=settings.debug,
        # Add security schemes for Swagger UI
        openapi_tags=[
            {
                "name": "triggers",
                "description": "Trigger management operations - create, update, delete, and monitor triggers",
            },
            {
                "name": "webhooks",
                "description": "Webhook processing endpoints for external services",
            },
            {
                "name": "Google Calendar",
                "description": "Google Calendar adapter management - webhook channels, status, and cleanup",
            },
            {
                "name": "health",
                "description": "Health checks and service status monitoring",
            },
            {
                "name": "schedulers",
                "description": "Scheduler management operations - create, update, delete, and list scheduled workflows",
            },
            {
                "name": "public",
                "description": "Public endpoints that do not require authentication",
            },
        ],
    )

    # Define security schemes for Swagger UI
    from fastapi.openapi.utils import get_openapi

    def custom_openapi():
        if app.openapi_schema:
            return app.openapi_schema

        openapi_schema = get_openapi(
            title=app.title,
            version=app.version,
            description=app.description,
            routes=app.routes,
        )

        # Add security schemes
        openapi_schema["components"]["securitySchemes"] = {
            "BearerAuth": {
                "type": "http",
                "scheme": "bearer",
                "bearerFormat": "API Key",
                "description": "Enter your API key as a Bearer token (e.g., 'abc' for development)",
            },
            "ApiKeyAuth": {
                "type": "apiKey",
                "in": "header",
                "name": "X-API-Key",
                "description": "Enter your API key in the X-API-Key header (e.g., 'abc' for development)",
            },
        }

        # Add security requirements to protected endpoints
        for path_item in openapi_schema["paths"].values():
            for operation in path_item.values():
                if isinstance(operation, dict) and "tags" in operation:
                    # Add security to all endpoints except health, webhooks, and public
                    # Apply security to specific tags
                    if (
                        operation.get("tags")
                        and (
                            "triggers" in operation["tags"]
                            or "google_calendar" in operation["tags"]
                            or "schedulers" in operation["tags"]
                        )
                        and "public" not in operation["tags"]
                    ):
                        operation["security"] = [{"BearerAuth": []}, {"ApiKeyAuth": []}]

        app.openapi_schema = openapi_schema
        return app.openapi_schema

    app.openapi = custom_openapi

    # Add middleware in correct order (last added = first executed)
    # CORS middleware should be last (first to execute)
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Allow all origins for testing
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["*"],
    )

    # Add exception handlers
    @app.exception_handler(404)
    async def not_found_handler(request: Request, exc):
        """Handle 404 errors with consistent format."""
        return JSONResponse(
            status_code=404,
            content={
                "error": {
                    "type": "NotFound",
                    "message": "The requested resource was not found",
                    "status_code": 404,
                }
            },
        )

    @app.exception_handler(500)
    async def internal_error_handler(request: Request, exc):
        """Handle 500 errors with consistent format."""

        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "type": "InternalServerError",
                    "message": "An internal server error occurred",
                    "status_code": 500,
                }
            },
        )

    # Register routers
    app.include_router(health.router)
    app.include_router(triggers.router)
    app.include_router(webhooks.router)
    app.include_router(google_calendar.router)
    app.include_router(schedulers.router)

    # Add root endpoint
    @app.get("/")
    async def root():
        """Root endpoint with service information."""
        return {
            "service": "trigger-service",
            "version": "1.0.0",
            "status": "operational",
            "docs": "/docs",
            "health": "/api/v1/health",
        }

    # Add the public trigger types endpoint with a different path to bypass any auth middleware
    @app.get("/public/trigger-types", tags=["public"])
    async def get_trigger_types_public():
        """
        Get information about all available trigger types.

        This endpoint is public and does not require authentication.
        """
        try:
            from src.schemas.trigger import TriggerTypeInfo, TriggerTypesResponse

            # Sample Google Calendar event data as provided by the user
            google_calendar_sample_event = {
                "kind": "calendar#event",
                "etag": '"3181161784712000"',
                "id": "4eahs9ghkhrvkld72hogu9ph3e_20200519T140000Z",
                "status": "confirmed",
                "htmlLink": "https://www.google.com/calendar/event?eid=NGVhaHM5Z2hraHJ2a2xkNzJob2d1OXBoM2VfMjAyMDA1MTlUMTQwMDAwWiBhZG1pbkBt",
                "created": "2020-05-19T17:00:58.000Z",
                "updated": "2020-05-19T17:00:58.356Z",
                "summary": "Sample Meeting",
                "description": "This is a sample meeting description",
                "location": "Conference Room A",
                "creator": {"email": "<EMAIL>", "displayName": "Admin User"},
                "organizer": {
                    "email": "<EMAIL>",
                    "displayName": "Admin User",
                },
                "start": {
                    "dateTime": "2020-05-19T14:00:00-07:00",
                    "timeZone": "America/Los_Angeles",
                },
                "end": {
                    "dateTime": "2020-05-19T15:00:00-07:00",
                    "timeZone": "America/Los_Angeles",
                },
                "iCalUID": "<EMAIL>",
                "sequence": 0,
                "attendees": [
                    {
                        "email": "<EMAIL>",
                        "displayName": "Attendee One",
                        "responseStatus": "needsAction",
                    },
                    {
                        "email": "<EMAIL>",
                        "displayName": "Attendee Two",
                        "responseStatus": "accepted",
                    },
                ],
                "hangoutLink": "https://meet.google.com/abc-defg-hij",
                "conferenceData": {
                    "entryPoints": [
                        {
                            "entryPointType": "video",
                            "uri": "https://meet.google.com/abc-defg-hij",
                            "label": "meet.google.com/abc-defg-hij",
                        }
                    ],
                    "conferenceSolution": {
                        "key": {"type": "hangoutsMeet"},
                        "name": "Google Meet",
                        "iconUri": "https://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v6/web-512dp/logo_meet_2020q4_color_2x_web_512dp.png",
                    },
                    "conferenceId": "abc-defg-hij",
                },
                "reminders": {"useDefault": True},
            }

            # Define available trigger types
            trigger_types = [
                TriggerTypeInfo(
                    name="google_calendar",
                    display_name="Google Calendar",
                    description="Trigger workflows based on Google Calendar events (created, updated, deleted)",
                    supported_events=["created", "updated", "deleted"],
                    configuration_schema={
                        "type": "object",
                        "properties": {
                            "calendar_id": {
                                "type": "string",
                                "description": "Google Calendar ID (use 'primary' for main calendar)",
                                "example": "primary",
                            },
                            "selected_event_fields": {
                                "type": "array",
                                "description": "Optional: Select specific event fields to include in workflow payload",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "source_field": {
                                            "type": "string",
                                            "description": "Source field from Google Calendar event (supports dot notation)",
                                        },
                                        "target_field": {
                                            "type": "string",
                                            "description": "Target field name in workflow payload",
                                        },
                                        "field_type": {
                                            "type": "string",
                                            "enum": [
                                                "string",
                                                "number",
                                                "boolean",
                                                "array",
                                            ],
                                            "description": "Expected field type for conversion",
                                        },
                                    },
                                    "required": [
                                        "source_field",
                                        "target_field",
                                        "field_type",
                                    ],
                                },
                            },
                        },
                        "required": ["calendar_id"],
                    },
                    sample_event_data=google_calendar_sample_event,
                )
            ]

            return TriggerTypesResponse(
                trigger_types=trigger_types, total_types=len(trigger_types)
            )

        except Exception as e:
            from fastapi import HTTPException, status

            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error while retrieving trigger types",
            )

    # Note: The /api/v1/triggers/types endpoint is now handled by the triggers router

    return app


def get_app() -> FastAPI:
    """
    Get the FastAPI application instance.

    This function creates the app on-demand to avoid loading settings
    at module import time.

    Returns:
        FastAPI: Application instance
    """
    return create_app()


# Create app instance for uvicorn when running as module
app = get_app()


def main() -> None:
    """Main entry point for the application."""
    settings = get_settings()
    uvicorn.run(
        "src.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
    )


if __name__ == "__main__":
    main()
