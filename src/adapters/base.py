"""
Base adapter interface for trigger implementations.

This module defines the abstract base class that all trigger adapters must
implement to ensure consistent behavior across different trigger types.
"""

from abc import ABC, abstractmethod
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Set
from uuid import UUID

from pydantic import BaseModel, Field, validator


class TriggerEventType(str, Enum):
    """Enumeration of supported trigger event types."""

    CREATED = "created"
    UPDATED = "updated"
    DELETED = "deleted"
    REMINDER = "reminder"
    TRIGGERED = "triggered"  # For scheduled triggers


class TriggerStatus(str, Enum):
    """Enumeration of trigger status values."""

    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    PENDING = "pending"


class TriggerEvent(BaseModel):
    """
    Standardized trigger event data structure.

    All adapters must transform their specific event formats into this
    standardized structure for consistent processing.
    """

    event_id: str = Field(..., description="Unique identifier for the event")
    event_type: TriggerEventType = Field(..., description="Type of event that occurred")
    source: str = Field(..., description="Source service that generated the event")
    timestamp: datetime = Field(..., description="When the event occurred")
    data: Dict[str, Any] = Field(..., description="Adapter-specific event data")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

    @validator("timestamp", pre=True)
    def parse_timestamp(cls, v):
        """Parse timestamp from various formats."""
        if isinstance(v, str):
            from dateutil.parser import parse

            return parse(v)
        return v

    class Config:
        """Pydantic configuration."""

        use_enum_values = True
        json_encoders = {datetime: lambda v: v.isoformat()}


class AdapterHealthStatus(BaseModel):
    """Health status information for an adapter."""

    is_healthy: bool = Field(..., description="Whether the adapter is healthy")
    last_check: datetime = Field(..., description="When the health check was performed")
    error_message: Optional[str] = Field(None, description="Error message if unhealthy")
    external_service_status: Optional[Dict[str, Any]] = Field(
        None, description="External service status details"
    )


class TriggerConfiguration(BaseModel):
    """Base configuration for triggers."""

    trigger_id: UUID = Field(..., description="Unique identifier for the trigger")
    user_id: str = Field(..., description="ID of the user who owns the trigger")
    workflow_id: str = Field(..., description="ID of the workflow to execute")
    event_types: List[TriggerEventType] = Field(
        ..., description="List of event types to monitor"
    )
    config: Dict[str, Any] = Field(..., description="Adapter-specific configuration")
    is_active: bool = Field(True, description="Whether the trigger is active")


class BaseTriggerAdapter(ABC):
    """
    Abstract base class for all trigger adapters.

    This class defines the interface that all trigger adapters must implement
    to provide consistent trigger functionality across different services.
    """

    def __init__(self, adapter_name: str):
        """
        Initialize the base adapter.

        Args:
            adapter_name: Unique name for this adapter type
        """
        self.adapter_name = adapter_name
        self._active_triggers: Dict[UUID, TriggerConfiguration] = {}
        self._health_status: Optional[AdapterHealthStatus] = None

    @property
    def supported_event_types(self) -> Set[TriggerEventType]:
        """
        Get the set of event types supported by this adapter.

        Returns:
            Set[TriggerEventType]: Supported event types
        """
        return {
            TriggerEventType.CREATED,
            TriggerEventType.UPDATED,
            TriggerEventType.DELETED,
        }

    @abstractmethod
    async def setup_trigger(
        self, trigger_config: TriggerConfiguration, session=None
    ) -> bool:
        """
        Set up a new trigger with the external service.

        Args:
            trigger_config: Complete trigger configuration
            session: Optional database session to use for the same transaction

        Returns:
            bool: True if setup was successful, False otherwise
        """
        pass

    @abstractmethod
    async def remove_trigger(self, trigger_id: UUID) -> bool:
        """
        Remove a trigger from the external service.

        Args:
            trigger_id: Unique identifier for the trigger to remove

        Returns:
            bool: True if removal was successful, False otherwise
        """
        pass

    @abstractmethod
    async def process_event(self, raw_event: Dict[str, Any]) -> Optional[TriggerEvent]:
        """
        Process a raw event from the external service.

        Args:
            raw_event: Raw event data from the external service

        Returns:
            TriggerEvent: Standardized event data, or None if event should be ignored
        """
        pass

    @abstractmethod
    async def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        Validate adapter-specific configuration.

        Args:
            config: Configuration to validate

        Returns:
            bool: True if configuration is valid, False otherwise
        """
        pass

    async def health_check(self) -> AdapterHealthStatus:
        """
        Check if the adapter and its external service are healthy.

        Returns:
            AdapterHealthStatus: Detailed health status information
        """
        try:
            is_healthy = await self._perform_health_check()
            self._health_status = AdapterHealthStatus(
                is_healthy=is_healthy,
                last_check=datetime.now(),
                error_message=None if is_healthy else "Health check failed",
            )
        except Exception as e:
            self._health_status = AdapterHealthStatus(
                is_healthy=False, last_check=datetime.now(), error_message=str(e)
            )

        return self._health_status

    @abstractmethod
    async def _perform_health_check(self) -> bool:
        """
        Perform the actual health check implementation.

        Returns:
            bool: True if healthy, False otherwise
        """
        pass

    async def register_trigger(
        self, trigger_config: TriggerConfiguration, session=None
    ) -> bool:
        """
        Register a trigger with this adapter.

        Args:
            trigger_config: Trigger configuration to register
            session: Optional database session to use for the same transaction

        Returns:
            bool: True if registration was successful
        """
        try:
            # Validate event types are supported
            unsupported_types = (
                set(trigger_config.event_types) - self.supported_event_types
            )
            if unsupported_types:
                return False

            # Validate configuration
            if not await self.validate_config(trigger_config.config):
                return False

            # Setup the trigger
            success = await self.setup_trigger(trigger_config, session)
            if success:
                self._active_triggers[trigger_config.trigger_id] = trigger_config

            return success

        except Exception as e:
            return False

    async def unregister_trigger(self, trigger_id: UUID) -> bool:
        """
        Unregister a trigger from this adapter.

        Args:
            trigger_id: ID of the trigger to unregister

        Returns:
            bool: True if unregistration was successful
        """
        try:
            success = await self.remove_trigger(trigger_id)
            if success and trigger_id in self._active_triggers:
                del self._active_triggers[trigger_id]

            return success

        except Exception as e:
            return False

    def get_active_triggers(self) -> List[TriggerConfiguration]:
        """
        Get list of active triggers for this adapter.

        Returns:
            List[TriggerConfiguration]: List of active trigger configurations
        """
        return list(self._active_triggers.values())

    def get_trigger_count(self) -> int:
        """
        Get the number of active triggers.

        Returns:
            int: Number of active triggers
        """
        return len(self._active_triggers)

    async def pause_trigger(self, trigger_id: UUID) -> bool:
        """
        Pause a specific trigger without removing it.

        Args:
            trigger_id: ID of the trigger to pause

        Returns:
            bool: True if pausing was successful
        """
        if trigger_id in self._active_triggers:
            self._active_triggers[trigger_id].is_active = False
            return True
        return False

    async def resume_trigger(self, trigger_id: UUID) -> bool:
        """
        Resume a paused trigger.

        Args:
            trigger_id: ID of the trigger to resume

        Returns:
            bool: True if resuming was successful
        """
        if trigger_id in self._active_triggers:
            self._active_triggers[trigger_id].is_active = True
            return True
        return False
