"""
Google Calendar Adapter for the Trigger Service.

This module implements the Google Calendar adapter that monitors calendar events
via webhooks and transforms them into standardized trigger events.
"""

import asyncio
import json
import os
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Set
from uuid import UUID

import httpx
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, update

from src.adapters.base import (
    BaseTriggerAdapter,
    TriggerEvent,
    TriggerEventType,
    TriggerConfiguration,
    AdapterHealthStatus,
)
from src.database.connection import get_async_session
from src.database.models import CalendarEvent, Trigger
from src.utils.config import get_settings
from src.utils.logger import get_logger
from src.utils.retry import RetryHandler, RetryableError

logger = get_logger(__name__)


class GoogleCalendarError(Exception):
    """Base exception for Google Calendar adapter errors."""

    pass


class GoogleCalendarAuthError(GoogleCalendarError):
    """Authentication-related errors."""

    pass


class GoogleCalendarAPIError(GoogleCalendarError, RetryableError):
    """API-related errors that should trigger retry."""

    pass


class GoogleCalendarAdapter(BaseTriggerAdapter):
    """
    Google Calendar adapter for monitoring calendar events.

    This adapter integrates with Google Calendar API to:
    - Set up webhook subscriptions for calendar events
    - Process incoming webhook events
    - Transform calendar events into standardized trigger events
    - Manage subscription lifecycle and renewal
    """

    _instance = None

    def __new__(cls):
        """Ensure singleton pattern for GoogleCalendarAdapter."""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls):
        """Get the singleton instance of GoogleCalendarAdapter."""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def __init__(self):
        """Initialize the Google Calendar adapter."""
        # Only initialize once (singleton pattern)
        if hasattr(self, "_initialized"):
            return

        super().__init__("google_calendar")
        self.settings = get_settings()

        self.retry_handler = RetryHandler(
            retryable_exceptions=[GoogleCalendarAPIError, HttpError, ConnectionError]
        )
        # Multi-user webhook and polling management (based on your webhook implementation)
        self._webhook_subscriptions: Dict[UUID, Dict[str, Any]] = {}
        self._polling_tasks: Dict[UUID, asyncio.Task] = {}
        self._polling_states: Dict[UUID, Dict[str, Any]] = {}

        # Multi-user credential management
        self._user_credentials: Dict[str, Credentials] = {}
        self._user_services: Dict[str, Any] = {}

        # Channel management (inspired by your active_channels)
        self._active_channels: Dict[str, Dict[str, Any]] = {}

        # Mark as initialized
        self._initialized = True

        logger.info(
            "Google Calendar adapter initialized",
            active_channels_count=len(self._active_channels),
        )

    @property
    def supported_event_types(self) -> Set[TriggerEventType]:
        """
        Get the set of event types supported by this adapter.

        Returns:
            Set[TriggerEventType]: Supported event types
        """
        return {
            TriggerEventType.CREATED,
            TriggerEventType.UPDATED,
            TriggerEventType.DELETED,
            TriggerEventType.REMINDER,
        }

    async def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        Validate Google Calendar adapter configuration.

        Args:
            config: Configuration to validate

        Returns:
            bool: True if configuration is valid, False otherwise
        """
        try:
            # Required fields
            required_fields = ["calendar_id"]
            for field in required_fields:
                if field not in config:
                    logger.error(f"Missing required field: {field}")
                    return False

            # Validate calendar_id format
            calendar_id = config["calendar_id"]
            if not isinstance(calendar_id, str) or not calendar_id.strip():
                logger.error("calendar_id must be a non-empty string")
                return False

            # Optional fields validation
            if "event_filters" in config:
                event_filters = config["event_filters"]
                if not isinstance(event_filters, dict):
                    logger.error("event_filters must be a dictionary")
                    return False

            # Validate webhook_ttl if provided
            if "webhook_ttl" in config:
                webhook_ttl = config["webhook_ttl"]
                if not isinstance(webhook_ttl, int) or webhook_ttl <= 0:
                    logger.error("webhook_ttl must be a positive integer")
                    return False

            logger.debug(
                f"Configuration validation passed for calendar_id: {calendar_id}"
            )
            return True

        except Exception as e:
            logger.error(f"Error validating configuration: {str(e)}")
            return False

    async def setup_trigger(
        self, trigger_config: TriggerConfiguration, session=None
    ) -> bool:
        """
        Set up a new Google Calendar trigger with webhook subscription or polling fallback.

        Args:
            trigger_config: Complete trigger configuration

        Returns:
            bool: True if setup was successful, False otherwise
        """
        try:
            logger.info(
                "🔧 Setting up Google Calendar trigger",
                trigger_id=trigger_config.trigger_id,
                user_id=trigger_config.user_id,
                calendar_id=trigger_config.config.get("calendar_id"),
                event_types=trigger_config.event_types,
                use_polling=trigger_config.config.get("use_polling", False),
            )

            # Get user credentials
            credentials = await self._get_user_credentials(trigger_config.user_id)
            if not credentials:
                logger.error(
                    "❌ Failed to get Google Calendar credentials",
                    user_id=trigger_config.user_id,
                    trigger_id=trigger_config.trigger_id,
                )
                return False

            # Create Google Calendar service
            service = await self._create_calendar_service(credentials)
            if not service:
                logger.error("Failed to create Google Calendar service")
                return False

            # Check if polling mode is explicitly requested or if webhook setup fails
            use_polling = trigger_config.config.get("use_polling", False)

            if not use_polling:
                # Try webhook setup first
                subscription_id = await self._create_webhook_subscription(
                    service, trigger_config, session
                )
                if subscription_id:
                    # Get the resource_id from the active channel
                    channel_info = self._active_channels.get(subscription_id)
                    resource_id = (
                        channel_info.get("resource_id")
                        if channel_info
                        else subscription_id
                    )

                    # Store subscription information
                    self._webhook_subscriptions[trigger_config.trigger_id] = {
                        "subscription_id": subscription_id,
                        "calendar_id": trigger_config.config["calendar_id"],
                        "user_id": trigger_config.user_id,
                        "created_at": datetime.now(),
                        "expires_at": datetime.now()
                        + timedelta(
                            seconds=trigger_config.config.get("webhook_ttl", 3600)
                        ),
                        "resource_id": resource_id,  # Store actual resource_id for cleanup
                    }

                    logger.info(
                        "✅ Google Calendar webhook trigger setup successful",
                        trigger_id=trigger_config.trigger_id,
                        user_id=trigger_config.user_id,
                        subscription_id=subscription_id,
                        calendar_id=trigger_config.config["calendar_id"],
                    )
                    return True
                else:
                    logger.warning(
                        "⚠️ Webhook setup failed, falling back to polling",
                        trigger_id=trigger_config.trigger_id,
                        user_id=trigger_config.user_id,
                    )
                    use_polling = True

            if use_polling:
                # Set up polling mode
                success = await self._setup_polling_trigger(trigger_config, service)
                if success:
                    logger.info(
                        "✅ Google Calendar polling trigger setup successful",
                        trigger_id=trigger_config.trigger_id,
                        user_id=trigger_config.user_id,
                        poll_interval=trigger_config.config.get(
                            "poll_interval_seconds", 60
                        ),
                        calendar_id=trigger_config.config["calendar_id"],
                    )
                    return True

            logger.error(
                "Failed to set up trigger with both webhook and polling methods"
            )
            return False

        except Exception as e:
            logger.error(
                f"Failed to setup Google Calendar trigger {trigger_config.trigger_id}",
                error=str(e),
            )
            return False

    async def remove_trigger(self, trigger_id: UUID) -> bool:
        """
        Remove a Google Calendar trigger and its webhook subscription or polling task.

        Args:
            trigger_id: Unique identifier for the trigger to remove

        Returns:
            bool: True if removal was successful, False otherwise
        """
        try:
            logger.info("Removing Google Calendar trigger", trigger_id=trigger_id)

            # Stop polling task if exists
            if trigger_id in self._polling_tasks:
                task = self._polling_tasks[trigger_id]
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
                del self._polling_tasks[trigger_id]
                logger.info("Stopped polling task for trigger", trigger_id=trigger_id)

            # Remove polling state if exists
            if trigger_id in self._polling_states:
                del self._polling_states[trigger_id]

            # 🔥 DATABASE CLEANUP: Get trigger info from database and clean up webhook
            try:
                trigger_record = None
                async for session in get_async_session():
                    result = await session.execute(
                        select(Trigger).where(Trigger.id == trigger_id)
                    )
                    trigger_record = result.scalar_one_or_none()
                    break  # Exit after first (and only) iteration

                if trigger_record and trigger_record.channel_id:
                    # Get user credentials
                    credentials = await self._get_user_credentials(
                        trigger_record.user_id
                    )
                    if credentials:
                        # Create Google Calendar service
                        service = await self._create_calendar_service(credentials)
                        if service:
                            # Remove webhook subscription using channel_id from database
                            await self._remove_webhook_subscription(
                                service,
                                trigger_record.channel_id,
                                None,  # resource_id not needed for channel stop
                            )

                    # Clear channel_id from database
                    async for session in get_async_session():
                        await session.execute(
                            update(Trigger)
                            .where(Trigger.id == trigger_id)
                            .values(channel_id=None)
                        )
                        await session.commit()
                        break  # Exit after first (and only) iteration

                    logger.info(
                        "✅ DATABASE CLEANUP: Removed webhook subscription and cleared channel_id",
                        trigger_id=trigger_id,
                        channel_id=trigger_record.channel_id,
                    )

                    # Remove from in-memory cache if exists
                    if trigger_record.channel_id in self._active_channels:
                        del self._active_channels[trigger_record.channel_id]

            except Exception as db_error:
                logger.error(
                    "❌ DATABASE CLEANUP: Failed to clean up webhook from database",
                    error=str(db_error),
                    trigger_id=trigger_id,
                )

            # Get webhook subscription info (fallback for old in-memory storage)
            subscription_info = self._webhook_subscriptions.get(trigger_id)
            if subscription_info:
                # Get user credentials
                credentials = await self._get_user_credentials(
                    subscription_info["user_id"]
                )
                if credentials:
                    # Create Google Calendar service
                    service = await self._create_calendar_service(credentials)
                    if service:
                        # Remove webhook subscription
                        await self._remove_webhook_subscription(
                            service,
                            subscription_info["subscription_id"],
                            subscription_info.get("resource_id"),
                        )

                # Remove from local storage
                del self._webhook_subscriptions[trigger_id]
                logger.info(
                    "Removed webhook subscription for trigger", trigger_id=trigger_id
                )

            logger.info(
                "Successfully removed Google Calendar trigger", trigger_id=trigger_id
            )
            return True

        except Exception as e:
            logger.error(
                f"Failed to remove Google Calendar trigger {trigger_id}",
                error=str(e),
            )
            return False

    async def process_event(self, raw_event: Dict[str, Any]) -> Optional[TriggerEvent]:
        """
        Process a raw Google Calendar webhook event.
        Enhanced to handle the actual event structure from Google Calendar webhooks.

        Args:
            raw_event: Raw event data from Google Calendar webhook

        Returns:
            TriggerEvent: Standardized event data, or None if event should be ignored
        """
        try:

            logger.info(
                "📨 Processing Google Calendar webhook event",
                event_type=raw_event.get("type"),
                has_headers=bool(raw_event.get("headers")),
                has_webhook_headers=bool(raw_event.get("webhook_headers")),
                channel_id=raw_event.get("webhook_headers", {}).get(
                    "x-goog-channel-id"
                ),
                resource_state=raw_event.get("webhook_headers", {}).get(
                    "x-goog-resource-state"
                ),
            )

            # Handle verification events (sync messages)
            if raw_event.get("type") == "verification":
                logger.info("Received webhook verification/sync event")
                return None

            # Extract webhook headers for validation
            headers = raw_event.get("webhook_headers", {})

            if not headers:
                logger.warning("No webhook_headers found in event")
                return None

            # Validate webhook authenticity
            if not self._validate_webhook(headers, raw_event):
                logger.warning("Invalid webhook received, ignoring")
                return None

            # Get channel information to identify the user and trigger
            channel_id = headers.get("x-goog-channel-id")

            if not channel_id:
                logger.warning("Missing channel ID in webhook headers")
                return None

            try:
                trigger_info = None
                async for session in get_async_session():
                    # Query database for trigger with this channel_id
                    result = await session.execute(
                        select(Trigger).where(Trigger.channel_id == channel_id)
                    )
                    trigger_record = result.scalar_one_or_none()

                    if trigger_record:
                        trigger_info = {
                            "trigger_id": trigger_record.id,
                            "user_id": trigger_record.user_id,
                            "calendar_id": trigger_record.trigger_config.get(
                                "calendar_id"
                            ),
                            "event_types": trigger_record.event_types,
                            "is_active": trigger_record.is_active,
                        }
                    break  # Exit after first (and only) iteration

                if not trigger_info:
                    logger.warning(
                        "No trigger found for channel_id",
                        channel_id=channel_id,
                    )
                    return None

                if not trigger_info["is_active"]:
                    logger.warning(
                        "Trigger is inactive for channel_id",
                        trigger_id=trigger_info["trigger_id"],
                        channel_id=channel_id,
                    )
                    return None

                # Extract trigger information from database result
                user_id = trigger_info["user_id"]
                trigger_id = trigger_info["trigger_id"]
                calendar_id = trigger_info["calendar_id"]
                configured_event_types = trigger_info["event_types"]

            except Exception as db_error:
                logger.error(
                    "Failed to fetch trigger info from database",
                    error=str(db_error),
                    channel_id=channel_id,
                )
                return None

            logger.info(
                "Processing webhook for user",
                user_id=user_id,
                trigger_id=trigger_id,
                channel_id=channel_id,
                calendar_id=calendar_id,
                resource_state=headers.get("x-goog-resource-state"),
            )

            # Process the calendar event notification
            trigger_events = await self._process_calendar_notification(
                user_id, calendar_id, trigger_id, headers, raw_event
            )

            # Return the first trigger event (or None if no events)
            return trigger_events[0] if trigger_events else None

        except Exception as e:
            logger.error("Failed to process Google Calendar event", error=str(e))
            return None

    async def _process_calendar_notification(
        self,
        user_id: str,
        calendar_id: str,
        trigger_id: UUID,
        headers: Dict[str, str],
        raw_event: Dict[str, Any],
    ) -> List[TriggerEvent]:
        """
        Process calendar notification and fetch complete event data.
        Enhanced to fetch complete event details and store them in the database.

        Args:
            user_id: User ID
            calendar_id: Calendar ID
            trigger_id: Trigger ID
            headers: Webhook headers
            raw_event: Raw webhook event

        Returns:
            List[TriggerEvent]: List of trigger events
        """
        try:
            # Get channel info to access event_types configuration
            channel_id = headers.get("x-goog-channel-id")
            resource_id = headers.get("x-goog-resource-id")

            # 🔥 DATABASE LOOKUP: Get configured event types from database
            configured_event_types = []
            try:
                async for session in get_async_session():
                    result = await session.execute(
                        select(Trigger).where(Trigger.channel_id == channel_id)
                    )
                    trigger_record = result.scalar_one_or_none()
                    if trigger_record:
                        configured_event_types = trigger_record.event_types
                    break  # Exit after first (and only) iteration
            except Exception as db_error:
                logger.error(
                    "Failed to fetch event types from database",
                    error=str(db_error),
                    channel_id=channel_id,
                )
                configured_event_types = []  # Default to empty list

            logger.debug(
                "Processing Google Calendar webhook notification",
                user_id=user_id,
                trigger_id=trigger_id,
                channel_id=channel_id,
                resource_id=resource_id,
                calendar_id=calendar_id,
                resource_state=headers.get("x-goog-resource-state"),
            )

            # Get user credentials and service
            credentials = await self._get_user_credentials(user_id)
            if not credentials:
                logger.error("No credentials found for user", user_id=user_id)
                return []

            service = await self._create_calendar_service(credentials)
            if not service:
                logger.error("Failed to create service for user", user_id=user_id)
                return []

            # Fetch complete event data from Google Calendar API
            # Since webhook notifications only contain minimal data, we need to fetch the actual events
            events = await self._fetch_recent_calendar_events(
                service, calendar_id, user_id
            )

            if not events:
                logger.debug("No recent events found", user_id=user_id)
                return []

            logger.debug(
                "Found recent events", user_id=user_id, event_count=len(events)
            )

            trigger_events = []
            for event in events:
                try:
                    # Determine event type based on webhook resource state and event timestamps
                    event_type = self._determine_event_type(event, headers)

                    # Filter based on configured event types
                    if not self._should_process_event_type(
                        event_type, configured_event_types
                    ):
                        logger.debug(
                            f"🚫 Skipping event due to type filter",
                            event_type=event_type.value if event_type else "unknown",
                            configured_types=configured_event_types,
                            event_summary=event.get("summary", "No title"),
                        )
                        continue

                    # Store complete event data in database
                    await self._store_calendar_event_in_database(
                        channel_id=channel_id,
                        resource_id=resource_id,
                        user_id=user_id,
                        calendar_id=calendar_id,
                        event=event,
                        event_type=event_type.value if event_type else "updated",
                    )

                    # Create trigger event for each calendar event
                    trigger_event = (
                        await self._create_trigger_event_from_calendar_event(
                            event,
                            trigger_id,
                            event_type.value if event_type else "updated",
                        )
                    )

                    if trigger_event:
                        trigger_events.append(trigger_event)

                        # Log the action
                        summary = event.get("summary", "No title")
                        start = event["start"].get(
                            "dateTime", event["start"].get("date")
                        )
                        event_id = event["id"]
                        created = event.get("created")
                        updated = event.get("updated")

                        logger.info(
                            "Processing calendar event",
                            event_type=event_type.value if event_type else "updated",
                            user_id=user_id,
                            summary=summary,
                            start_time=start,
                            event_id=event_id,
                            trigger_id=trigger_id,
                        )

                except Exception as event_error:
                    logger.error(
                        "Failed to process individual event",
                        error=str(event_error),
                        event_id=event.get("id", "unknown"),
                        user_id=user_id,
                    )
                    continue

            logger.debug(
                "Processed events after filtering",
                user_id=user_id,
                trigger_id=trigger_id,
                total_events=len(events),
                filtered_events=len(trigger_events),
            )

            return trigger_events

        except Exception as e:
            logger.error(
                "Failed to process calendar notification",
                error=str(e),
                user_id=user_id,
                trigger_id=trigger_id,
            )
            return []

    async def _fetch_recent_calendar_events(
        self, service, calendar_id: str, user_id: str
    ) -> List[Dict[str, Any]]:
        """
        Fetch recent calendar events from Google Calendar API.

        Args:
            service: Google Calendar service instance
            calendar_id: Calendar ID to fetch events from
            user_id: User ID for logging

        Returns:
            List[Dict[str, Any]]: List of calendar events
        """
        try:
            # Get the most recently updated event (webhook-triggered, no need for time tracking)
            now = datetime.now(timezone.utc)

            # Short time window to catch very recent changes (webhooks are real-time)
            updated_min = (
                (now - timedelta(minutes=1)).isoformat().replace("+00:00", "Z")
            )

            logger.debug(
                "Fetching recent events", user_id=user_id, updated_min=updated_min
            )

            # Build query parameters for webhook usage
            query_params = {
                "calendarId": calendar_id,
                "maxResults": 1,  # Only get the most recent event
                "singleEvents": True,
                "orderBy": "updated",  # Order by last update time (most recent first)
                "updatedMin": updated_min,  # Only very recent events (webhook context)
                "showDeleted": True,  # Include deleted events to catch deletions
            }

            loop = asyncio.get_event_loop()
            events_result = await loop.run_in_executor(
                None,
                lambda: service.events().list(**query_params).execute(),
            )

            events = events_result.get("items", [])

            if not events:
                logger.debug("No recent events found", user_id=user_id)
                return []

            # For the single event, fetch complete details to ensure we have all data
            complete_events = []
            for event in events:
                try:
                    # Fetch complete event details
                    loop = asyncio.get_event_loop()
                    complete_event = await loop.run_in_executor(
                        None,
                        lambda: service.events()
                        .get(calendarId=calendar_id, eventId=event["id"])
                        .execute(),
                    )
                    complete_events.append(complete_event)
                    logger.debug(
                        "Fetched complete event details",
                        summary=complete_event.get("summary", "No title"),
                        updated=complete_event.get("updated"),
                        status=complete_event.get("status"),
                    )
                except Exception as event_error:
                    logger.warning(
                        f"Failed to fetch complete details for event {event.get('id')}",
                        error=str(event_error),
                        user_id=user_id,
                    )
                    # Use the partial event data if complete fetch fails
                    complete_events.append(event)

            return complete_events

        except Exception as e:
            logger.error(
                "Failed to fetch recent calendar events",
                error=str(e),
                user_id=user_id,
            )
            return []

    async def _store_calendar_event_in_database(
        self,
        channel_id: str,
        resource_id: str,
        user_id: str,
        calendar_id: str,
        event: Dict[str, Any],
        event_type: str,
    ) -> bool:
        """
        Store complete calendar event data in the database.

        Args:
            channel_id: Google Calendar webhook channel ID
            resource_id: Google Calendar resource ID
            user_id: User ID
            calendar_id: Calendar ID
            event: Complete event data from Google Calendar API
            event_type: Type of event (created, updated, deleted)

        Returns:
            bool: True if storage was successful
        """
        try:
            event_id = event.get("id")
            if not event_id:
                logger.warning("Event missing ID, cannot store in database")
                return False

            # Parse Google Calendar timestamps
            google_created_at = None
            google_updated_at = None

            if event.get("created"):
                try:
                    google_created_at = datetime.fromisoformat(
                        event["created"].replace("Z", "+00:00")
                    )
                except Exception as date_error:
                    logger.warning(f"Failed to parse created timestamp: {date_error}")

            if event.get("updated"):
                try:
                    google_updated_at = datetime.fromisoformat(
                        event["updated"].replace("Z", "+00:00")
                    )
                except Exception as date_error:
                    logger.warning(f"Failed to parse updated timestamp: {date_error}")

            # Create calendar event record
            calendar_event = CalendarEvent(
                channel_id=channel_id,
                resource_id=resource_id,
                user_id=user_id,
                event_id=event_id,
                calendar_id=calendar_id,
                event_data=event,
                event_type=event_type,
                google_created_at=google_created_at,
                google_updated_at=google_updated_at,
            )

            # Store in database
            async for session in get_async_session():
                # Check if event already exists (for upsert behavior)
                existing_event = await session.execute(
                    select(CalendarEvent).where(
                        and_(
                            CalendarEvent.user_id == user_id,
                            CalendarEvent.event_id == event_id,
                            CalendarEvent.calendar_id == calendar_id,
                        )
                    )
                )
                existing = existing_event.scalar_one_or_none()

                if existing:
                    # Update existing event
                    existing.event_data = event
                    existing.event_type = event_type
                    existing.google_updated_at = google_updated_at
                    existing.channel_id = channel_id
                    existing.resource_id = resource_id
                    logger.debug(
                        f"Updated existing calendar event {event_id} in database"
                    )
                else:
                    # Create new event
                    session.add(calendar_event)
                    logger.debug(
                        "Stored new calendar event in database", event_id=event_id
                    )

                await session.commit()
                break  # Exit after first (and only) iteration

            logger.debug(
                "Stored calendar event in database",
                event_id=event_id,
                user_id=user_id,
                calendar_id=calendar_id,
                event_type=event_type,
                event_summary=event.get("summary", "No title"),
            )
            return True

        except Exception as e:
            logger.error(
                "Failed to store calendar event in database",
                error=str(e),
                event_id=event.get("id", "unknown"),
                user_id=user_id,
            )
            return False

    async def _perform_health_check(self) -> bool:
        """
        Perform health check for Google Calendar adapter.

        Returns:
            bool: True if healthy, False otherwise
        """
        try:
            # Check if we can reach Google Calendar API
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    "https://www.googleapis.com/calendar/v3/users/me/calendarList",
                    timeout=10.0,
                )
                # We expect 401 since we're not authenticated, but service should be reachable
                return response.status_code in [200, 401, 403]

        except Exception as e:
            logger.error(f"Google Calendar health check failed", error=str(e))
            return False

    def get_adapter_info(self) -> Dict[str, Any]:
        """
        Get information about the Google Calendar adapter.

        Returns:
            Dict[str, Any]: Adapter information including display name, description,
                          supported events, configuration schema, and sample event data
        """
        return {
            "trigger_type": "google_calendar",
            "name": "Google Calendar",
            "description": "Trigger workflows based on Google Calendar events (created, updated, deleted)",
            "supported_event_types": ["created", "updated", "deleted"],
            "configuration_schema": self.get_configuration_schema(),
            "sample_event_data": self.get_sample_event_data(),
            "available_fields": self.get_available_fields(),
        }

    def get_configuration_schema(self) -> Dict[str, Any]:
        """
        Get the JSON schema for Google Calendar adapter configuration.

        Returns:
            Dict[str, Any]: JSON schema for configuration validation
        """
        return {
            "type": "object",
            "properties": {
                "calendar_id": {
                    "type": "string",
                    "description": "Google Calendar ID (use 'primary' for main calendar)",
                    "example": "primary",
                },
                "selected_event_fields": {
                    "type": "array",
                    "description": "Optional: Select specific event fields to include in workflow payload",
                    "items": {
                        "type": "object",
                        "properties": {
                            "calendar_field": {
                                "type": "string",
                                "description": "Source field from Google Calendar event (supports dot notation)",
                            },
                            "workflow_field": {
                                "type": "string",
                                "description": "Target field name in workflow payload",
                            },
                            "field_type": {
                                "type": "string",
                                "enum": ["string", "number", "boolean", "array"],
                                "description": "Expected field type for conversion",
                            },
                        },
                        "required": ["calendar_field", "workflow_field", "field_type"],
                    },
                },
            },
            "required": ["calendar_id"],
        }

    def get_sample_event_data(self) -> Dict[str, Any]:
        """
        Get sample Google Calendar event data.

        Returns:
            Dict[str, Any]: Sample Google Calendar event structure
        """
        return {
            "kind": "calendar#event",
            "etag": '"3181161784712000"',
            "id": "4eahs9ghkhrvkld72hogu9ph3e_20200519T140000Z",
            "status": "confirmed",
            "htmlLink": "https://www.google.com/calendar/event?eid=NGVhaHM5Z2hraHJ2a2xkNzJob2d1OXBoM2VfMjAyMDA1MTlUMTQwMDAwWiBhZG1pbkBt",
            "created": "2020-05-19T17:00:58.000Z",
            "updated": "2020-05-19T17:00:58.356Z",
            "summary": "Sample Meeting",
            "description": "This is a sample meeting description",
            "location": "Conference Room A",
            "creator": {"email": "<EMAIL>", "displayName": "Admin User"},
            "organizer": {"email": "<EMAIL>", "displayName": "Admin User"},
            "start": {
                "dateTime": "2020-05-19T14:00:00-07:00",
                "timeZone": "America/Los_Angeles",
            },
            "end": {
                "dateTime": "2020-05-19T15:00:00-07:00",
                "timeZone": "America/Los_Angeles",
            },
            "iCalUID": "<EMAIL>",
            "sequence": 0,
            "attendees": [
                {
                    "email": "<EMAIL>",
                    "displayName": "Attendee One",
                    "responseStatus": "needsAction",
                },
                {
                    "email": "<EMAIL>",
                    "displayName": "Attendee Two",
                    "responseStatus": "accepted",
                },
            ],
            "hangoutLink": "https://meet.google.com/abc-defg-hij",
            "conferenceData": {
                "entryPoints": [
                    {
                        "entryPointType": "video",
                        "uri": "https://meet.google.com/abc-defg-hij",
                        "label": "meet.google.com/abc-defg-hij",
                    }
                ],
                "conferenceSolution": {
                    "key": {"type": "hangoutsMeet"},
                    "name": "Google Meet",
                    "iconUri": "https://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v6/web-512dp/logo_meet_2020q4_color_2x_web_512dp.png",
                },
                "conferenceId": "abc-defg-hij",
            },
            "reminders": {"useDefault": True},
        }

    def get_available_fields(self) -> List[Dict[str, str]]:
        """
        Get available fields for Google Calendar events.

        Returns:
            List[Dict[str, str]]: List of available fields for mapping
        """
        return [
            {"field": "id", "description": "Event ID", "type": "string"},
            {
                "field": "summary",
                "description": "Event title/summary",
                "type": "string",
            },
            {
                "field": "description",
                "description": "Event description",
                "type": "string",
            },
            {"field": "location", "description": "Event location", "type": "string"},
            {
                "field": "start.dateTime",
                "description": "Event start date/time",
                "type": "string",
            },
            {
                "field": "end.dateTime",
                "description": "Event end date/time",
                "type": "string",
            },
            {
                "field": "creator.email",
                "description": "Event creator email",
                "type": "string",
            },
            {
                "field": "creator.displayName",
                "description": "Event creator name",
                "type": "string",
            },
            {
                "field": "organizer.email",
                "description": "Event organizer email",
                "type": "string",
            },
            {
                "field": "organizer.displayName",
                "description": "Event organizer name",
                "type": "string",
            },
            {
                "field": "attendees",
                "description": "Event attendees list",
                "type": "array",
            },
            {"field": "status", "description": "Event status", "type": "string"},
            {"field": "htmlLink", "description": "Event HTML link", "type": "string"},
            {
                "field": "hangoutLink",
                "description": "Google Meet link",
                "type": "string",
            },
            {
                "field": "created",
                "description": "Event creation timestamp",
                "type": "string",
            },
            {
                "field": "updated",
                "description": "Event last update timestamp",
                "type": "string",
            },
        ]

    async def _get_user_credentials(self, user_id: str) -> Optional[Credentials]:
        """
        Get Google Calendar credentials for a user with caching support.
        Enhanced based on your webhook implementation for multi-user support.

        Args:
            user_id: ID of the user

        Returns:
            Credentials: Google OAuth2 credentials, or None if not found
        """
        try:
            # Check if we have cached credentials for this user
            if user_id in self._user_credentials:
                credentials = self._user_credentials[user_id]

                # Check if credentials are still valid
                if credentials.valid:
                    return credentials

                # Try to refresh if expired
                if credentials.expired and credentials.refresh_token:
                    logger.info(
                        "Refreshing cached credentials for user", user_id=user_id
                    )
                    try:
                        loop = asyncio.get_event_loop()
                        await loop.run_in_executor(None, credentials.refresh, Request())
                        logger.info(
                            f"Successfully refreshed credentials for user {user_id}"
                        )
                        return credentials
                    except Exception as refresh_error:
                        logger.error(
                            f"Failed to refresh cached credentials: {refresh_error}"
                        )
                        # Remove invalid credentials from cache
                        del self._user_credentials[user_id]

            # Load fresh credentials from storage
            credentials = await self._load_user_credentials_from_storage(user_id)
            if credentials:
                # Cache the credentials for future use
                self._user_credentials[user_id] = credentials
                logger.info("Cached credentials for user", user_id=user_id)
                return credentials

            return None

        except Exception as e:
            logger.error(f"Failed to get credentials for user {user_id}", error=str(e))
            return None

    async def _load_user_credentials_from_storage(
        self, user_id: str
    ) -> Optional[Credentials]:
        """
        Load user credentials from auth service.
        Updated to use the external auth service for OAuth credentials.

        Args:
            user_id: ID of the user

        Returns:
            Credentials: Google OAuth2 credentials, or None if not found
        """
        try:
            from src.utils.auth_client import AuthClient, AuthServiceConnectionError

            logger.info(
                f"Loading Google Calendar credentials for user {user_id} from auth service"
            )

            # Try to fetch credentials from auth service
            try:
                # Create auth client instance and use async context manager
                auth_client = AuthClient()
                async with auth_client:
                    # Fetch OAuth credentials from auth service
                    oauth_credentials = await auth_client.get_oauth_credentials(user_id)

                    if not oauth_credentials:
                        logger.warning(
                            f"No OAuth credentials found for user {user_id} in auth service"
                        )
                        return None
                    logger.debug(
                        "OAuth credentials received",
                        oauth_credentials=oauth_credentials,
                    )

                    # Extract Google Calendar credentials from OAuth response
                    # The auth service returns credentials directly at the top level
                    if not oauth_credentials.get("success"):
                        logger.warning(
                            f"OAuth credentials request was not successful for user {user_id}"
                        )
                        return None

                    # Validate required fields - credentials are at top level
                    required_fields = ["access_token"]
                    for field in required_fields:
                        if field not in oauth_credentials:
                            logger.error(
                                f"Missing required OAuth field '{field}' for user {user_id}"
                            )
                            return None

                    # Extract scopes from the response (convert space-separated string to list)
                    scope_string = oauth_credentials.get("scope", "")
                    scopes = (
                        scope_string.split()
                        if scope_string
                        else [
                            "https://www.googleapis.com/auth/calendar",
                            "https://www.googleapis.com/auth/calendar.events",
                        ]
                    )

                    # Create Google OAuth2 credentials object
                    credentials = Credentials(
                        token=oauth_credentials.get("access_token"),
                        refresh_token=oauth_credentials.get("refresh_token"),
                        token_uri="https://oauth2.googleapis.com/token",
                        client_id=(
                            self.settings.google_client_id
                            if hasattr(self.settings, "google_client_id")
                            else None
                        ),
                        client_secret=(
                            self.settings.google_client_secret
                            if hasattr(self.settings, "google_client_secret")
                            else None
                        ),
                        scopes=scopes,
                    )

                    # Check if token needs refresh
                    if credentials.expired and credentials.refresh_token:
                        logger.info(
                            "Refreshing expired token for user", user_id=user_id
                        )
                        try:
                            # Use asyncio to refresh token without blocking
                            loop = asyncio.get_event_loop()
                            await loop.run_in_executor(
                                None, credentials.refresh, Request()
                            )

                            # TODO: Update refreshed token back to auth service
                            # This would require an update endpoint in the auth service
                            logger.info("Token refreshed for user", user_id=user_id)
                            logger.warning(
                                f"Refreshed token not saved back to auth service - implement update endpoint"
                            )

                        except Exception as refresh_error:
                            logger.error(
                                f"Failed to refresh token for user {user_id}: {refresh_error}"
                            )
                            return None
                    elif credentials.expired:
                        logger.error(
                            f"Token expired and no refresh token available for user {user_id}"
                        )
                        return None

                    logger.info(
                        f"Successfully loaded credentials for user {user_id} from auth service"
                    )
                    return credentials

            except AuthServiceConnectionError as conn_error:
                logger.warning(
                    f"Auth service connection failed for user {user_id}: {conn_error}."
                )
                return None
            except Exception as auth_error:
                logger.error(f"Auth service error for user {user_id}: {auth_error}.")
                return None

        except Exception as e:
            logger.error(
                f"Failed to load credentials for user {user_id} from auth service",
                error=str(e),
            )
            return None

    async def _create_calendar_service(self, credentials: Credentials):
        """
        Create Google Calendar API service instance.

        Args:
            credentials: Google OAuth2 credentials

        Returns:
            Google Calendar service instance, or None if failed
        """
        try:
            service = build("calendar", "v3", credentials=credentials)
            return service

        except Exception as e:
            logger.error(f"Failed to create Google Calendar service", error=str(e))
            return None

    async def _create_webhook_subscription(
        self, service, trigger_config: TriggerConfiguration, session=None
    ) -> Optional[str]:
        """
        Create a webhook subscription for calendar events.
        Enhanced based on your webhook implementation for better multi-user support.

        Args:
            service: Google Calendar service instance
            trigger_config: Trigger configuration

        Returns:
            str: Subscription ID if successful, None otherwise
        """
        try:
            calendar_id = trigger_config.config["calendar_id"]
            webhook_ttl = trigger_config.config.get(
                "webhook_ttl", 604800
            )  # 7 days like your implementation

            # Check if webhook URL is configured
            logger.debug("Checking webhook URL configuration")
            logger.debug("Settings object", settings=str(self.settings))
            logger.debug(
                "Has google_calendar_webhook_url attr",
                has_attr=hasattr(self.settings, "google_calendar_webhook_url"),
            )

            if hasattr(self.settings, "google_calendar_webhook_url"):
                logger.debug(
                    "Raw webhook URL from settings",
                    webhook_url=self.settings.google_calendar_webhook_url,
                )

            if (
                not hasattr(self.settings, "google_calendar_webhook_url")
                or not self.settings.google_calendar_webhook_url
            ):
                logger.error("Google Calendar webhook URL not configured")
                return None

            # Validate that webhook URL is HTTPS (required by Google Calendar)
            webhook_url = self.settings.google_calendar_webhook_url
            # webhook_url = "https://99b5-103-173-221-201.ngrok-free.app/api/v1/webhooks/google-calendar"
            logger.info("Using webhook URL", webhook_url=webhook_url)

            if not webhook_url.startswith("https://"):
                logger.error(
                    f"Google Calendar webhook URL must use HTTPS, got: {webhook_url}",
                    extra={
                        "help": "For development, use ngrok to create an HTTPS tunnel: 'ngrok http 8000'"
                    },
                )
                return None

            # Generate unique channel ID (like your implementation)
            import uuid

            channel_id = f"trigger-{trigger_config.trigger_id}-{str(uuid.uuid4())[:8]}"

            # Calculate expiration time (like your webhook implementation)
            from datetime import datetime, timezone

            expiration = int(
                (datetime.now(timezone.utc).timestamp() + webhook_ttl) * 1000
            )

            # Prepare webhook subscription request (enhanced from your implementation)
            body = {
                "id": channel_id,
                "type": "web_hook",
                "address": webhook_url,
                "expiration": str(expiration),
                "params": {
                    "ttl": str(webhook_ttl),
                },
            }

            # Add token for verification if configured (like your implementation)
            webhook_secret = getattr(
                self.settings, "google_calendar_webhook_secret", None
            )
            if webhook_secret:
                body["token"] = webhook_secret

            # Create the subscription with timeout
            result = await asyncio.wait_for(
                self._execute_watch_request(service, calendar_id, body),
                timeout=30.0,  # 30 second timeout
            )

            if result:
                subscription_id = result.get("id")
                resource_id = result.get("resourceId")

                # 🔥 DATABASE STORAGE: Save channel_id to database using the passed session
                try:
                    if session:
                        # Use the passed session (same transaction as TriggerManager)
                        await session.execute(
                            update(Trigger)
                            .where(Trigger.id == trigger_config.trigger_id)
                            .values(channel_id=channel_id)
                        )
                        # Don't commit here - let TriggerManager handle the commit
                    else:
                        # Fallback: create our own session if none was passed
                        async for db_session in get_async_session():
                            await db_session.execute(
                                update(Trigger)
                                .where(Trigger.id == trigger_config.trigger_id)
                                .values(channel_id=channel_id)
                            )
                            await db_session.commit()
                            break  # Exit after first (and only) iteration

                    logger.info(
                        "Successfully saved channel_id to database",
                        channel_id=channel_id,
                        resource_id=resource_id,
                        trigger_id=trigger_config.trigger_id,
                        user_id=trigger_config.user_id,
                        calendar_id=calendar_id,
                    )

                    # Store channel info for management (keeping minimal in-memory cache for compatibility)
                    self._active_channels[channel_id] = {
                        "resource_id": resource_id,
                        "expiration": expiration,
                        "trigger_id": trigger_config.trigger_id,
                        "user_id": trigger_config.user_id,
                        "calendar_id": calendar_id,
                        "event_types": trigger_config.event_types,
                        "created_at": datetime.now(timezone.utc).timestamp(),
                    }

                    return channel_id  # Return channel_id instead of subscription_id

                except Exception as db_error:
                    logger.error(
                        "Failed to save channel_id to database",
                        error=str(db_error),
                        channel_id=channel_id,
                        trigger_id=trigger_config.trigger_id,
                    )
                    # Fall back to in-memory storage if database fails
                    self._active_channels[channel_id] = {
                        "resource_id": resource_id,
                        "expiration": expiration,
                        "trigger_id": trigger_config.trigger_id,
                        "user_id": trigger_config.user_id,
                        "calendar_id": calendar_id,
                        "event_types": trigger_config.event_types,
                        "created_at": datetime.now(timezone.utc).timestamp(),
                    }
                    return channel_id

            return None

        except asyncio.TimeoutError:
            logger.error("Timeout creating webhook subscription")
            return None
        except Exception as e:
            logger.error(f"Failed to create webhook subscription", error=str(e))
            return None

    async def _execute_watch_request(
        self, service, calendar_id: str, body: Dict[str, Any]
    ):
        """
        Execute the Google Calendar watch request.

        Args:
            service: Google Calendar service instance
            calendar_id: Calendar ID to watch
            body: Request body for the watch request

        Returns:
            Dict: Response from the watch request
        """
        try:
            # Execute the watch request asynchronously
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                lambda: service.events()
                .watch(calendarId=calendar_id, body=body)
                .execute(),
            )
            return result

        except HttpError as e:
            if e.resp.status in [500, 502, 503, 504]:
                # Retryable HTTP errors
                raise GoogleCalendarAPIError(f"Google Calendar API error: {str(e)}")
            else:
                # Non-retryable errors
                raise GoogleCalendarError(f"Google Calendar API error: {str(e)}")
        except Exception as e:
            raise GoogleCalendarAPIError(f"Unexpected error: {str(e)}")

    async def _remove_webhook_subscription(
        self, service, channel_id: str, resource_id: Optional[str] = None
    ) -> bool:
        """
        Remove a webhook subscription.

        Args:
            service: Google Calendar service instance
            channel_id: ID of the channel to stop
            resource_id: Resource ID of the subscription (optional)

        Returns:
            bool: True if removal was successful
        """
        try:
            # Stop the webhook subscription
            body = {
                "id": channel_id,
            }

            # Add resource ID if provided
            if resource_id:
                body["resourceId"] = resource_id

            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None, lambda: service.channels().stop(body=body).execute()
            )

            logger.info(
                "Successfully removed webhook subscription", channel_id=channel_id
            )
            return True

        except HttpError as e:
            if e.resp.status == 404:
                # Subscription already removed or doesn't exist
                logger.info(
                    f"Webhook subscription {channel_id} not found (already removed)"
                )
                return True
            else:
                logger.error(
                    f"Failed to remove webhook subscription {channel_id}",
                    error=str(e),
                )
                return False
        except Exception as e:
            logger.error(
                f"Failed to remove webhook subscription {channel_id}", error=str(e)
            )
            return False

    def _validate_webhook(
        self, headers: Dict[str, str], event_data: Dict[str, Any]
    ) -> bool:
        """
        Validate Google Calendar webhook authenticity.
        Enhanced based on your webhook implementation with signature verification.

        Args:
            headers: Webhook headers
            event_data: Event data

        Returns:
            bool: True if webhook is valid
        """
        try:
            # Basic validation - check for required headers
            required_headers = ["x-goog-channel-id", "x-goog-resource-state"]
            for header in required_headers:
                if header not in headers:
                    logger.warning("Missing required header", header=header)
                    return False

            # Validate resource state
            resource_state = headers.get("x-goog-resource-state")
            valid_states = ["exists", "not_exists", "sync"]

            if resource_state not in valid_states:
                logger.warning("Invalid resource state", resource_state=resource_state)
                return False

            logger.debug("Webhook resource state", resource_state=resource_state)

            # Verify webhook signature if secret is configured
            webhook_secret = getattr(
                self.settings, "google_calendar_webhook_secret", None
            )

            if webhook_secret:
                signature = headers.get("x-goog-channel-token")
                payload = event_data.get("raw_payload", b"")

                if signature and not self._verify_webhook_signature(
                    payload, signature, webhook_secret
                ):
                    logger.warning("Invalid webhook signature")
                    return False

            logger.debug("Webhook validation passed")
            return True

        except Exception as e:
            logger.error("Webhook validation failed", error=str(e))
            return False

    def _verify_webhook_signature(
        self, payload: bytes, signature: str, secret: str
    ) -> bool:
        """
        Verify the webhook signature for security.
        Based on your verify_webhook_signature function.

        Args:
            payload: Raw payload bytes
            signature: Signature from headers
            secret: Webhook secret

        Returns:
            bool: True if signature is valid
        """
        try:
            if not signature or not secret:
                return True  # Skip verification if no secret is configured

            import hmac
            import hashlib

            expected_signature = hmac.new(
                secret.encode("utf-8"), payload, hashlib.sha256
            ).hexdigest()

            return hmac.compare_digest(signature, expected_signature)

        except Exception as e:
            logger.error(f"Signature verification failed", error=str(e))
            return False

    async def _parse_calendar_event(
        self, raw_event: Dict[str, Any], headers: Dict[str, str]
    ) -> Optional[Dict[str, Any]]:
        """
        Parse Google Calendar webhook event into standardized format.

        Args:
            raw_event: Raw event data from webhook
            headers: Webhook headers

        Returns:
            Dict: Parsed event data, or None if event should be ignored
        """
        try:
            # Extract information from headers
            channel_id = headers.get("x-goog-channel-id", "")
            resource_state = headers.get("x-goog-resource-state", "")
            resource_id = headers.get("x-goog-resource-id", "")

            # Determine event type based on resource state
            event_type_mapping = {
                "exists": TriggerEventType.UPDATED,  # Event created or updated
                "not_exists": TriggerEventType.DELETED,  # Event deleted
                "sync": TriggerEventType.TRIGGERED,  # Sync event (ignore)
            }

            event_type = event_type_mapping.get(resource_state)
            if not event_type or event_type == TriggerEventType.TRIGGERED:
                logger.debug(
                    "Ignoring sync event with state", resource_state=resource_state
                )
                return None

            # Generate event ID
            event_id = f"{channel_id}-{resource_id}-{datetime.now().isoformat()}"

            # Extract calendar information from channel ID
            # Channel ID format: "trigger-{trigger_id}"
            trigger_id_part = (
                channel_id.replace("trigger-", "")
                if channel_id.startswith("trigger-")
                else ""
            )

            # Build event data
            event_data = {
                "event_id": event_id,
                "event_type": event_type,
                "timestamp": datetime.now(),
                "data": {
                    "channel_id": channel_id,
                    "resource_state": resource_state,
                    "resource_id": resource_id,
                    "trigger_id": trigger_id_part,
                    "calendar_event": raw_event.get("calendar_event", {}),
                },
                "metadata": {
                    "webhook_headers": headers,
                    "raw_event": raw_event,
                },
            }

            # Apply event filters if configured
            if not self._should_process_event(event_data, raw_event):
                logger.debug("Event filtered out by configuration")
                return None

            logger.debug("Successfully parsed calendar event", event_id=event_id)
            return event_data

        except Exception as e:
            logger.error(f"Failed to parse calendar event", error=str(e))
            return None

    def _should_process_event(
        self, event_data: Dict[str, Any], raw_event: Dict[str, Any]
    ) -> bool:
        """
        Check if event should be processed based on filters.

        Args:
            event_data: Parsed event data
            raw_event: Raw event data

        Returns:
            bool: True if event should be processed
        """
        try:
            # For now, process all events
            # In the future, this could check trigger-specific filters
            # like event title patterns, attendee filters, etc.

            # Basic validation - ensure we have required data
            if not event_data.get("event_id") or not event_data.get("event_type"):
                return False

            return True

        except Exception as e:
            logger.error(f"Error checking event filters", error=str(e))
            return False

    async def get_subscription_info(self, trigger_id: UUID) -> Optional[Dict[str, Any]]:
        """
        Get webhook subscription information for a trigger.

        Args:
            trigger_id: Trigger ID

        Returns:
            Dict: Subscription information, or None if not found
        """
        return self._webhook_subscriptions.get(trigger_id)

    async def renew_subscription(self, trigger_id: UUID) -> bool:
        """
        Renew a webhook subscription that is about to expire.

        Args:
            trigger_id: Trigger ID

        Returns:
            bool: True if renewal was successful
        """
        try:
            subscription_info = self._webhook_subscriptions.get(trigger_id)
            if not subscription_info:
                logger.error(f"No subscription found for trigger {trigger_id}")
                return False

            # Get user credentials
            credentials = await self._get_user_credentials(subscription_info["user_id"])
            if not credentials:
                logger.error(f"Failed to get credentials for subscription renewal")
                return False

            # Create service
            service = await self._create_calendar_service(credentials)
            if not service:
                logger.error("Failed to create service for subscription renewal")
                return False

            # Remove old subscription
            await self._remove_webhook_subscription(
                service,
                subscription_info["subscription_id"],
                subscription_info.get("resource_id"),
            )

            # Create new subscription (this would need trigger config)
            # For now, just log that renewal is needed
            logger.info(
                "Subscription renewal needed for trigger", trigger_id=trigger_id
            )

            return True

        except Exception as e:
            logger.error(
                f"Failed to renew subscription for trigger {trigger_id}", error=str(e)
            )
            return False

    async def _setup_polling_trigger(
        self, trigger_config: TriggerConfiguration, service
    ) -> bool:
        """
        Set up polling-based trigger monitoring (fallback when webhooks fail).
        Based on the POC polling approach.

        Args:
            trigger_config: Trigger configuration
            service: Google Calendar service instance

        Returns:
            bool: True if polling setup was successful
        """
        try:
            trigger_id = trigger_config.trigger_id
            poll_interval = trigger_config.config.get("poll_interval_seconds", 60)

            # Initialize polling state
            self._polling_states[trigger_id] = {
                "last_check_time": datetime.now(timezone.utc).isoformat(),
                "calendar_id": trigger_config.config["calendar_id"],
                "user_id": trigger_config.user_id,
                "event_types": trigger_config.event_types,
                "poll_interval": poll_interval,
                "created_at": datetime.now(),
            }

            # Start polling task
            task = asyncio.create_task(
                self._polling_loop(trigger_id, trigger_config, service)
            )
            self._polling_tasks[trigger_id] = task

            logger.info(
                "🔄 Started Google Calendar polling",
                trigger_id=trigger_id,
                user_id=trigger_config.user_id,
                poll_interval=poll_interval,
                calendar_id=trigger_config.config["calendar_id"],
            )
            return True

        except Exception as e:
            logger.error(f"Failed to setup polling trigger {trigger_id}", error=str(e))
            return False

    async def _polling_loop(
        self, trigger_id: UUID, trigger_config: TriggerConfiguration, service
    ):
        """
        Main polling loop for a trigger (based on your POC).
        Continuously checks for new calendar events.

        Args:
            trigger_id: Trigger ID
            trigger_config: Trigger configuration
            service: Google Calendar service instance
        """
        try:
            poll_interval = self._polling_states[trigger_id]["poll_interval"]
            calendar_id = self._polling_states[trigger_id]["calendar_id"]

            logger.info("Starting polling loop for trigger", trigger_id=trigger_id)

            while trigger_id in self._polling_states:
                try:
                    # Get current state
                    state = self._polling_states[trigger_id]
                    last_check_time_str = state["last_check_time"]
                    last_check_time_dt = datetime.fromisoformat(
                        last_check_time_str.replace("Z", "+00:00")
                    )

                    logger.debug(
                        f"Checking for new events for trigger {trigger_id}",
                        last_check_time=last_check_time_str,
                    )

                    # Find newly created events (based on your POC logic)
                    newly_created_events = await self._get_newly_created_events(
                        service, calendar_id, last_check_time_dt
                    )

                    if newly_created_events:
                        logger.info(
                            "📅 Found new calendar events",
                            trigger_id=trigger_id,
                            user_id=trigger_config.user_id,
                            event_count=len(newly_created_events),
                            calendar_id=calendar_id,
                        )

                        latest_creation_time = last_check_time_dt

                        for event in newly_created_events:
                            # Process each event
                            trigger_event = (
                                await self._create_trigger_event_from_calendar_event(
                                    event, trigger_id, "created"
                                )
                            )

                            if trigger_event:
                                # Send to trigger manager for processing
                                await self._emit_trigger_event(trigger_event)

                            # Update latest creation time
                            event_created_dt = datetime.fromisoformat(
                                event["created"].replace("Z", "+00:00")
                            )
                            if event_created_dt > latest_creation_time:
                                latest_creation_time = event_created_dt

                        # Update state with latest processed time
                        new_state_time = latest_creation_time.isoformat().replace(
                            "+00:00", "Z"
                        )
                        self._polling_states[trigger_id][
                            "last_check_time"
                        ] = new_state_time

                        logger.debug(
                            f"Updated polling state for trigger {trigger_id}",
                            new_last_check_time=new_state_time,
                        )

                    # Wait for next poll
                    await asyncio.sleep(poll_interval)

                except asyncio.CancelledError:
                    logger.info(
                        "Polling loop cancelled for trigger", trigger_id=trigger_id
                    )
                    break
                except Exception as e:
                    logger.error(
                        f"Error in polling loop for trigger {trigger_id}",
                        error=str(e),
                    )
                    # Wait longer after an error
                    await asyncio.sleep(poll_interval * 2)

        except Exception as e:
            logger.error(
                f"Fatal error in polling loop for trigger {trigger_id}",
                error=str(e),
            )
        finally:
            # Clean up
            if trigger_id in self._polling_tasks:
                del self._polling_tasks[trigger_id]
            logger.info("Polling loop ended for trigger", trigger_id=trigger_id)

    async def _get_newly_created_events(
        self, service, calendar_id: str, last_check_time_dt: datetime
    ) -> List[Dict[str, Any]]:
        """
        Get newly created calendar events since the last check time.
        Based on your POC logic for finding new events.

        Args:
            service: Google Calendar service instance
            calendar_id: Calendar ID to check
            last_check_time_dt: Last check timestamp

        Returns:
            List[Dict]: List of newly created events
        """
        try:
            # Get all upcoming events (similar to your POC)
            now_utc = datetime.now(timezone.utc).isoformat()
            loop = asyncio.get_event_loop()
            events_result = await loop.run_in_executor(
                None,
                lambda: service.events()
                .list(
                    calendarId=calendar_id,
                    timeMin=now_utc,
                    singleEvents=True,
                    orderBy="startTime",
                    maxResults=50,  # Reasonable limit
                )
                .execute(),
            )

            all_upcoming_events = events_result.get("items", [])
            newly_created_events = []

            # Filter by creation time (your POC logic)
            for event in all_upcoming_events:
                if "created" not in event:
                    continue

                created_str = event["created"]
                created_dt = datetime.fromisoformat(created_str.replace("Z", "+00:00"))

                if created_dt > last_check_time_dt:
                    newly_created_events.append(event)

            return newly_created_events

        except Exception as e:
            logger.error(f"Failed to get newly created events", error=str(e))
            return []

    async def _create_trigger_event_from_calendar_event(
        self, calendar_event: Dict[str, Any], trigger_id: UUID, event_type: str
    ) -> Optional[TriggerEvent]:
        """
        Create a standardized trigger event from a Google Calendar event.

        Args:
            calendar_event: Google Calendar event data
            trigger_id: Trigger ID that detected this event
            event_type: Type of event (created, updated, etc.)

        Returns:
            TriggerEvent: Standardized trigger event
        """
        try:
            # Generate event ID
            event_id = f"gcal-{calendar_event.get('id', 'unknown')}-{trigger_id}"

            # Extract event details
            summary = calendar_event.get("summary", "Untitled Event")
            start_time = calendar_event.get("start", {}).get(
                "dateTime", calendar_event.get("start", {}).get("date")
            )
            created_time = calendar_event.get("created")

            # Create trigger event
            trigger_event = TriggerEvent(
                event_id=event_id,
                event_type=(
                    TriggerEventType.CREATED
                    if event_type == "created"
                    else TriggerEventType.UPDATED
                ),
                source="google_calendar",
                timestamp=(
                    datetime.fromisoformat(created_time.replace("Z", "+00:00"))
                    if created_time
                    else datetime.now(timezone.utc)
                ),
                data={
                    "calendar_event": calendar_event,
                    "summary": summary,
                    "start_time": start_time,
                    "trigger_id": str(trigger_id),
                    "detection_method": "polling",
                },
                metadata={
                    "google_calendar_event_id": calendar_event.get("id"),
                    "calendar_id": calendar_event.get("organizer", {}).get(
                        "email", "unknown"
                    ),
                    "event_type": event_type,
                },
            )

            logger.debug(
                f"Created trigger event from calendar event",
                event_id=event_id,
                summary=summary,
                trigger_id=trigger_id,
            )

            return trigger_event

        except Exception as e:
            logger.error(
                f"Failed to create trigger event from calendar event",
                error=str(e),
                calendar_event_id=calendar_event.get("id"),
            )
            return None

    async def _emit_trigger_event(self, trigger_event: TriggerEvent):
        """
        Emit a trigger event for processing by the trigger manager.
        This is a placeholder - in a real implementation, this would
        send the event to the trigger manager or event queue.

        Args:
            trigger_event: Trigger event to emit
        """
        try:
            # For now, just log the event
            # In a real implementation, this would send to the trigger manager
            logger.info(
                "🎯 TRIGGER EVENT FIRED",
                event_type=trigger_event.event_type.value,
                event_title=trigger_event.data.get("summary"),
                event_id=trigger_event.event_id,
                source=trigger_event.source,
                trigger_id=trigger_event.data.get("trigger_id"),
                detection_method=trigger_event.data.get("detection_method"),
                timestamp=trigger_event.timestamp.isoformat(),
            )

            # TODO: Integrate with actual trigger manager event processing
            # await self.trigger_manager.process_trigger_event(trigger_event)

        except Exception as e:
            logger.error(f"Failed to emit trigger event", error=str(e))

    async def get_webhook_status(self) -> Dict[str, Any]:
        """
        Get the status of active webhook channels.
        Based on your webhook_status endpoint.

        Returns:
            Dict: Status information for all active channels
        """
        try:
            from datetime import datetime, timezone

            status = []
            current_time = datetime.now(timezone.utc).timestamp() * 1000

            for channel_id, channel_info in self._active_channels.items():
                expiration = channel_info["expiration"]
                is_expired = current_time > expiration

                status.append(
                    {
                        "channel_id": channel_id,
                        "resource_id": channel_info["resource_id"],
                        "trigger_id": str(channel_info["trigger_id"]),
                        "user_id": channel_info["user_id"],
                        "calendar_id": channel_info["calendar_id"],
                        "expires_at": datetime.fromtimestamp(
                            expiration / 1000, timezone.utc
                        ).isoformat(),
                        "is_expired": is_expired,
                        "created_at": datetime.fromtimestamp(
                            channel_info["created_at"], timezone.utc
                        ).isoformat(),
                    }
                )

            return {
                "active_channels": status,
                "total_channels": len(status),
                "expired_channels": sum(1 for s in status if s["is_expired"]),
            }

        except Exception as e:
            logger.error(f"Failed to get webhook status", error=str(e))
            return {"error": str(e)}

    async def cleanup_expired_channels(self) -> int:
        """
        Clean up expired webhook channels.
        Based on your cleanup_expired_channels function.

        Returns:
            int: Number of channels cleaned up
        """
        try:
            from datetime import datetime, timezone

            current_time = datetime.now(timezone.utc).timestamp() * 1000
            expired_channels = []

            # Find expired channels
            for channel_id, channel_info in self._active_channels.items():
                if current_time > channel_info["expiration"]:
                    expired_channels.append((channel_id, channel_info))

            # Clean up expired channels
            cleaned_count = 0
            for channel_id, channel_info in expired_channels:
                try:
                    # Get user credentials and service
                    user_id = channel_info["user_id"]
                    credentials = await self._get_user_credentials(user_id)
                    if credentials:
                        service = await self._create_calendar_service(credentials)
                        if service:
                            # Stop the webhook subscription
                            await self._remove_webhook_subscription(
                                service, channel_id, channel_info["resource_id"]
                            )

                    # Remove from active channels
                    del self._active_channels[channel_id]
                    cleaned_count += 1

                    logger.info("Cleaned up expired channel", channel_id=channel_id)

                except Exception as e:
                    logger.error(
                        f"Failed to cleanup channel {channel_id}", error=str(e)
                    )

            if cleaned_count > 0:
                logger.info(f"Cleaned up {cleaned_count} expired webhook channels")

            return cleaned_count

        except Exception as e:
            logger.error(f"Error during channel cleanup", error=str(e))
            return 0

    async def stop_webhook_channel(self, channel_id: str) -> bool:
        """
        Stop a specific webhook channel.
        Based on your stop_webhook endpoint.

        Args:
            channel_id: Channel ID to stop

        Returns:
            bool: True if successfully stopped
        """
        try:
            if channel_id not in self._active_channels:
                logger.warning(f"Channel not found: {channel_id}")
                return False

            channel_info = self._active_channels[channel_id]
            user_id = channel_info["user_id"]
            resource_id = channel_info["resource_id"]

            # Get user credentials and service
            credentials = await self._get_user_credentials(user_id)
            if not credentials:
                logger.error(f"No credentials found for user {user_id}")
                return False

            service = await self._create_calendar_service(credentials)
            if not service:
                logger.error(f"Failed to create service for user {user_id}")
                return False

            # Stop the webhook subscription
            success = await self._remove_webhook_subscription(
                service, channel_id, resource_id
            )

            if success:
                # Remove from active channels
                del self._active_channels[channel_id]
                logger.info(
                    "Successfully stopped webhook channel", channel_id=channel_id
                )

            return success

        except Exception as e:
            logger.error(f"Failed to stop webhook channel {channel_id}", error=str(e))
            return False

    def _extract_trigger_id_from_channel_id(self, channel_id: str) -> Optional[str]:
        """
        Extract trigger ID from channel ID format.
        Channel ID format: 'trigger-{trigger_id}-{random_suffix}'

        Args:
            channel_id: Channel ID to parse

        Returns:
            str: Trigger ID if found, None otherwise
        """
        try:
            if not channel_id or not channel_id.startswith("trigger-"):
                return None

            # Split by '-' and get the trigger ID part
            parts = channel_id.split("-")
            if len(parts) >= 2:
                # The trigger ID is the second part (after 'trigger-')
                trigger_id = parts[1]
                logger.debug(
                    f"Extracted trigger ID '{trigger_id}' from channel ID '{channel_id}'"
                )
                return trigger_id

            return None

        except Exception as e:
            logger.error(
                f"Failed to extract trigger ID from channel ID '{channel_id}'",
                error=str(e),
            )
            return None

    async def get_trigger_by_channel_id(
        self, channel_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get trigger information from database using channel_id.

        Args:
            channel_id: Channel ID to lookup

        Returns:
            Dict: Trigger information if found, None otherwise
        """
        try:
            logger.debug("Looking up trigger by channel_id", channel_id=channel_id)

            async for session in get_async_session():
                # Query database for trigger with this channel_id
                result = await session.execute(
                    select(Trigger).where(Trigger.channel_id == channel_id)
                )
                trigger_record = result.scalar_one_or_none()

                if trigger_record:
                    trigger_info = {
                        "trigger_id": trigger_record.id,
                        "user_id": trigger_record.user_id,
                        "calendar_id": trigger_record.trigger_config.get("calendar_id"),
                        "event_types": trigger_record.event_types,
                        "is_active": trigger_record.is_active,
                        "channel_id": trigger_record.channel_id,
                        "trigger_config": trigger_record.trigger_config,
                        "created_at": trigger_record.created_at,
                        "updated_at": trigger_record.updated_at,
                    }

                    logger.debug(
                        f"Found trigger in database",
                        trigger_id=trigger_info["trigger_id"],
                        user_id=trigger_info["user_id"],
                        calendar_id=trigger_info["calendar_id"],
                        is_active=trigger_info["is_active"],
                        channel_id=channel_id,
                    )

                    return trigger_info
                else:
                    logger.debug(
                        "No trigger found with channel_id", channel_id=channel_id
                    )
                    return None

                break  # Exit after first (and only) iteration

        except Exception as e:
            logger.error(
                f"Failed to get trigger by channel_id '{channel_id}'",
                error=str(e),
            )
            return None

    async def update_webhook_status_from_database(self) -> Dict[str, Any]:
        """
        Get webhook status using database-based channel storage instead of in-memory.
        This replaces the in-memory _active_channels approach with database lookups.

        Returns:
            Dict: Status information for all active channels from database
        """
        try:
            from datetime import datetime, timezone

            status = []
            current_time = datetime.now(timezone.utc).timestamp() * 1000

            # Query all triggers with channel_id from database
            async for session in get_async_session():
                result = await session.execute(
                    select(Trigger).where(
                        and_(Trigger.channel_id.isnot(None), Trigger.is_active == True)
                    )
                )
                triggers_with_channels = result.scalars().all()

                for trigger in triggers_with_channels:
                    try:
                        # Get channel info from in-memory cache if available
                        channel_info = self._active_channels.get(trigger.channel_id, {})

                        # Calculate expiration (default to 7 days if not in cache)
                        webhook_ttl = trigger.trigger_config.get(
                            "webhook_ttl", 604800
                        )  # 7 days
                        created_timestamp = (
                            trigger.created_at.timestamp()
                            if trigger.created_at
                            else datetime.now(timezone.utc).timestamp()
                        )
                        expiration = (created_timestamp + webhook_ttl) * 1000
                        is_expired = current_time > expiration

                        status.append(
                            {
                                "channel_id": trigger.channel_id,
                                "resource_id": channel_info.get(
                                    "resource_id", "unknown"
                                ),
                                "trigger_id": str(trigger.id),
                                "user_id": trigger.user_id,
                                "calendar_id": trigger.trigger_config.get(
                                    "calendar_id", "unknown"
                                ),
                                "expires_at": datetime.fromtimestamp(
                                    expiration / 1000, timezone.utc
                                ).isoformat(),
                                "is_expired": is_expired,
                                "created_at": (
                                    trigger.created_at.isoformat()
                                    if trigger.created_at
                                    else datetime.now(timezone.utc).isoformat()
                                ),
                                "storage_type": "database",
                            }
                        )

                    except Exception as trigger_error:
                        logger.error(
                            f"Error processing trigger {trigger.id} for webhook status",
                            error=str(trigger_error),
                            trigger_id=trigger.id,
                            channel_id=trigger.channel_id,
                        )
                        continue

                break  # Exit after first (and only) iteration

            logger.info(
                f"Retrieved webhook status from database",
                total_channels=len(status),
                expired_channels=sum(1 for s in status if s["is_expired"]),
            )

            return {
                "active_channels": status,
                "total_channels": len(status),
                "expired_channels": sum(1 for s in status if s["is_expired"]),
                "storage_type": "database",
            }

        except Exception as e:
            logger.error(f"Failed to get webhook status from database", error=str(e))
            return {"error": str(e), "storage_type": "database"}

    async def cleanup_expired_channels_from_database(self) -> int:
        """
        Clean up expired webhook channels using database-based storage.
        This replaces the in-memory _active_channels cleanup with database queries.

        Returns:
            int: Number of channels cleaned up
        """
        try:
            from datetime import datetime, timezone

            current_time = datetime.now(timezone.utc).timestamp() * 1000
            cleaned_count = 0

            # Query all triggers with channel_id from database
            async for session in get_async_session():
                result = await session.execute(
                    select(Trigger).where(
                        and_(Trigger.channel_id.isnot(None), Trigger.is_active == True)
                    )
                )
                triggers_with_channels = result.scalars().all()

                for trigger in triggers_with_channels:
                    try:
                        # Calculate expiration
                        webhook_ttl = trigger.trigger_config.get(
                            "webhook_ttl", 604800
                        )  # 7 days
                        created_timestamp = (
                            trigger.created_at.timestamp()
                            if trigger.created_at
                            else datetime.now(timezone.utc).timestamp()
                        )
                        expiration = (created_timestamp + webhook_ttl) * 1000

                        # Check if expired
                        if current_time > expiration:
                            logger.info(
                                f"Cleaning up expired channel",
                                channel_id=trigger.channel_id,
                                trigger_id=trigger.id,
                                user_id=trigger.user_id,
                                expired_by_ms=current_time - expiration,
                            )

                            # Get user credentials and service for cleanup
                            credentials = await self._get_user_credentials(
                                trigger.user_id
                            )
                            if credentials:
                                service = await self._create_calendar_service(
                                    credentials
                                )
                                if service:
                                    # Stop the webhook subscription
                                    await self._remove_webhook_subscription(
                                        service, trigger.channel_id, None
                                    )

                            # Clear channel_id from database
                            await session.execute(
                                update(Trigger)
                                .where(Trigger.id == trigger.id)
                                .values(channel_id=None)
                            )

                            # Remove from in-memory cache if exists
                            if trigger.channel_id in self._active_channels:
                                del self._active_channels[trigger.channel_id]

                            cleaned_count += 1

                            logger.info(
                                f"Successfully cleaned up expired channel: {trigger.channel_id}",
                                trigger_id=trigger.id,
                            )

                    except Exception as trigger_error:
                        logger.error(
                            f"Failed to cleanup channel for trigger {trigger.id}",
                            error=str(trigger_error),
                            trigger_id=trigger.id,
                            channel_id=trigger.channel_id,
                        )
                        continue

                # Commit all changes
                await session.commit()
                break  # Exit after first (and only) iteration

            if cleaned_count > 0:
                logger.info(
                    f"Cleaned up {cleaned_count} expired webhook channels from database"
                )

            return cleaned_count

        except Exception as e:
            logger.error(f"Error during database-based channel cleanup", error=str(e))
            return 0

    def _determine_event_type(
        self, event: Dict[str, Any], headers: Dict[str, str]
    ) -> Optional[TriggerEventType]:
        """
        Determine the event type based on Google Calendar event data and webhook headers.

        Args:
            event: Google Calendar event data
            headers: Webhook headers

        Returns:
            TriggerEventType: The determined event type, or None if cannot be determined
        """
        try:
            # Get resource state from headers
            resource_state = headers.get("x-goog-resource-state", "")

            # Basic mapping based on resource state
            if resource_state == "not_exists":
                return TriggerEventType.DELETED
            elif resource_state == "sync":
                return None  # Ignore sync events
            elif resource_state == "exists":
                # For 'exists' state, we need to determine if it's created or updated
                # Check if the event was recently created (within last 5 minutes)
                if event.get("created") and event.get("updated"):
                    try:
                        from datetime import datetime, timezone, timedelta

                        created_time = datetime.fromisoformat(
                            event["created"].replace("Z", "+00:00")
                        )
                        updated_time = datetime.fromisoformat(
                            event["updated"].replace("Z", "+00:00")
                        )

                        # If created and updated times are very close (within 1 minute),
                        # it's likely a new event
                        time_diff = abs((updated_time - created_time).total_seconds())
                        if time_diff < 60:  # Within 1 minute
                            return TriggerEventType.CREATED
                        else:
                            return TriggerEventType.UPDATED

                    except Exception as date_error:
                        logger.warning(
                            f"Failed to parse event timestamps for type determination: {date_error}"
                        )
                        # Default to UPDATED if we can't parse timestamps
                        return TriggerEventType.UPDATED
                else:
                    # Default to UPDATED if timestamps are missing
                    return TriggerEventType.UPDATED
            else:
                logger.warning(f"Unknown resource state: {resource_state}")
                return TriggerEventType.UPDATED  # Default fallback

        except Exception as e:
            logger.error(f"Failed to determine event type: {e}")
            return TriggerEventType.UPDATED  # Safe fallback

    def _should_process_event_type(
        self, event_type: Optional[TriggerEventType], configured_event_types: List[str]
    ) -> bool:
        """
        Check if the event type should be processed based on configured event types.

        Args:
            event_type: The event type to check
            configured_event_types: List of configured event type strings

        Returns:
            bool: True if the event should be processed
        """
        try:
            if not event_type:
                return False

            # Convert event_type enum to string for comparison
            event_type_str = event_type.value

            # Check if this event type is in the configured list
            return event_type_str in configured_event_types

        except Exception as e:
            logger.error(f"Failed to check event type filter: {e}")
            return False  # Safe fallback - don't process if we can't determine
