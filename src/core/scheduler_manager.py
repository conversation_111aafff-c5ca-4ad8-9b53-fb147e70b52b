from typing import List, Optional, Dict, Any, AsyncGenerator
from datetime import datetime, timezone
from uuid import uuid4
import json

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from sqlalchemy import select, and_, or_, text
from sqlalchemy.orm import selectinload

from src.database.models import Scheduler
from src.schemas.scheduler import (
    SimplifiedSchedulerCreate,
    SimplifiedSchedulerUpdate,
    SimplifiedSchedulerResponse,
)
from src.utils.logger import get_logger
from src.utils.schedule_parser import ScheduleParser

logger = get_logger(__name__)


class SchedulerManager:
    """
    Manages CRUD operations and lifecycle for Scheduler entities using simplified schema.
    """

    def __init__(self, db: AsyncSession):
        self.db = db

    async def create(
        self, scheduler_data: SimplifiedSchedulerCreate, user_id: str
    ) -> SimplifiedSchedulerResponse:
        """
        Creates a new scheduler entry in the database using simplified schema.
        """
        try:
            # Create a temporary response object for schedule parsing
            temp_scheduler = SimplifiedSchedulerResponse(
                id="temp",
                user_id=user_id,
                workflow_id=scheduler_data.workflow_id,
                name=scheduler_data.name,
                frequency=scheduler_data.frequency,
                time=scheduler_data.time,
                days_of_week=scheduler_data.days_of_week,
                days_of_month=scheduler_data.days_of_month,
                cron_expression=scheduler_data.cron_expression,
                timezone=scheduler_data.timezone,
                is_active=scheduler_data.is_active,
                scheduler_metadata=scheduler_data.scheduler_metadata,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
                last_run_at=None,
                next_run_at=None,
            )

            # Calculate next_run_at using the new schedule parser
            next_run_at = ScheduleParser.get_next_run_time(temp_scheduler)

            db_scheduler = Scheduler(
                id=str(uuid4()),
                user_id=user_id,
                name=scheduler_data.name,
                workflow_id=scheduler_data.workflow_id,
                # Map frequency to schedule_type for backward compatibility
                schedule_type=scheduler_data.frequency.value,
                # Keep legacy schedule_config as empty dict for backward compatibility
                schedule_config={},
                workflow_config={"workflow_id": scheduler_data.workflow_id},
                timezone=scheduler_data.timezone,
                is_active=scheduler_data.is_active,
                scheduler_metadata=scheduler_data.scheduler_metadata,
                next_run_at=next_run_at,
                # New simplified fields
                frequency=scheduler_data.frequency.value,
                time=scheduler_data.time,
                days_of_week=scheduler_data.days_of_week,
                days_of_month=scheduler_data.days_of_month,
                cron_expression=scheduler_data.cron_expression,
            )
            self.db.add(db_scheduler)
            await self.db.commit()
            await self.db.refresh(db_scheduler)
            logger.info(
                f"Scheduler '{db_scheduler.name}' created with ID: {db_scheduler.id}"
            )
            return SimplifiedSchedulerResponse.model_validate(db_scheduler)
        except IntegrityError as e:
            await self.db.rollback()
            logger.error(f"Integrity error creating scheduler: {e}")
            raise ValueError(
                f"Scheduler with name '{scheduler_data.name}' already exists or invalid data."
            )
        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Database error creating scheduler: {e}")
            raise RuntimeError("Failed to create scheduler due to a database error.")
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Unexpected error creating scheduler: {e}")
            raise RuntimeError(f"An unexpected error occurred: {e}")

    async def get(
        self, scheduler_id: str, user_id: str
    ) -> Optional[SimplifiedSchedulerResponse]:
        """
        Retrieves a scheduler by its ID with user isolation.
        """
        stmt = select(Scheduler).where(
            Scheduler.id == scheduler_id, Scheduler.user_id == user_id
        )
        result = await self.db.execute(stmt)
        scalars = result.scalars()
        db_scheduler = scalars.first()
        if db_scheduler:
            return SimplifiedSchedulerResponse.model_validate(db_scheduler)
        return None

    async def update(
        self, scheduler_id: str, scheduler_data: SimplifiedSchedulerUpdate, user_id: str
    ) -> Optional[SimplifiedSchedulerResponse]:
        """
        Updates an existing scheduler entry with user isolation.
        """
        try:
            stmt = select(Scheduler).where(
                Scheduler.id == scheduler_id, Scheduler.user_id == user_id
            )
            result = await self.db.execute(stmt)
            scalars = result.scalars()
            db_scheduler = scalars.first()
            if not db_scheduler:
                return None

            update_data = scheduler_data.model_dump(exclude_unset=True)
            schedule_changed = False

            # Check if any schedule-related fields are being updated
            schedule_fields = [
                "frequency",
                "time",
                "days_of_week",
                "days_of_month",
                "cron_expression",
                "timezone",
            ]
            for field in schedule_fields:
                if field in update_data:
                    schedule_changed = True
                    break

            # Update the database fields
            for key, value in update_data.items():
                if hasattr(db_scheduler, key):
                    setattr(db_scheduler, key, value)

            # If frequency is updated, also update schedule_type for backward compatibility
            if "frequency" in update_data:
                db_scheduler.schedule_type = update_data["frequency"]

            # If schedule changed, recalculate next_run_at
            if schedule_changed:
                # Create a temporary response object for schedule parsing
                temp_scheduler = SimplifiedSchedulerResponse(
                    id=db_scheduler.id,
                    user_id=db_scheduler.user_id,
                    workflow_id=db_scheduler.workflow_id,
                    name=db_scheduler.name,
                    frequency=db_scheduler.frequency,
                    time=db_scheduler.time,
                    days_of_week=db_scheduler.days_of_week,
                    days_of_month=db_scheduler.days_of_month,
                    cron_expression=db_scheduler.cron_expression,
                    timezone=db_scheduler.timezone,
                    is_active=db_scheduler.is_active,
                    scheduler_metadata=db_scheduler.scheduler_metadata,
                    created_at=db_scheduler.created_at,
                    updated_at=db_scheduler.updated_at,
                    last_run_at=db_scheduler.last_run_at,
                    next_run_at=db_scheduler.next_run_at,
                )

                db_scheduler.next_run_at = ScheduleParser.get_next_run_time(
                    temp_scheduler, db_scheduler.last_run_at
                )

            db_scheduler.updated_at = datetime.now(timezone.utc)
            await self.db.commit()
            await self.db.refresh(db_scheduler)
            logger.info(
                f"Scheduler '{db_scheduler.name}' with ID: {db_scheduler.id} updated."
            )
            return SimplifiedSchedulerResponse.model_validate(db_scheduler)
        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Database error updating scheduler {scheduler_id}: {e}")
            raise RuntimeError("Failed to update scheduler due to a database error.")
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Unexpected error updating scheduler {scheduler_id}: {e}")
            raise RuntimeError(f"An unexpected error occurred: {e}")

    async def delete(self, scheduler_id: str, user_id: str) -> bool:
        """
        Deletes a scheduler by its ID with user isolation.
        """
        try:
            stmt = select(Scheduler).where(
                Scheduler.id == scheduler_id, Scheduler.user_id == user_id
            )
            result = await self.db.execute(stmt)
            scalars = result.scalars()
            db_scheduler = scalars.first()
            if db_scheduler:
                await self.db.delete(db_scheduler)
                await self.db.commit()
                logger.info("Scheduler deleted", scheduler_id=scheduler_id)
                return True
            return False
        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Database error deleting scheduler {scheduler_id}: {e}")
            raise RuntimeError("Failed to delete scheduler due to a database error.")
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Unexpected error deleting scheduler {scheduler_id}: {e}")
            raise RuntimeError(f"An unexpected error occurred: {e}")

    async def list(
        self, user_id: str, skip: int = 0, limit: int = 100
    ) -> List[SimplifiedSchedulerResponse]:
        """
        Lists schedulers for a specific user with pagination.
        """
        stmt = (
            select(Scheduler)
            .where(Scheduler.user_id == user_id)
            .offset(skip)
            .limit(limit)
        )
        result = await self.db.execute(stmt)
        scalars = result.scalars()
        schedulers = scalars.all()
        return [SimplifiedSchedulerResponse.model_validate(s) for s in schedulers]

    async def get_due_schedulers(
        self, limit: int = 100, offset: int = 0
    ) -> List[SimplifiedSchedulerResponse]:
        """
        Retrieves schedulers that are active and due for execution with optimizations.
        """
        now_utc = datetime.now(timezone.utc)

        # Optimized query with proper indexing and pagination
        stmt = (
            select(Scheduler)
            .where(
                and_(
                    Scheduler.is_active == True,
                    Scheduler.next_run_at <= now_utc,
                    or_(Scheduler.expires_at.is_(None), Scheduler.expires_at > now_utc),
                )
            )
            .order_by(Scheduler.next_run_at.asc())  # Process oldest first
            .limit(limit)
            .offset(offset)
            # Eager load relationships to avoid N+1 queries
            .options(selectinload(Scheduler.executions))
        )

        result = await self.db.execute(stmt)
        due_schedulers = result.scalars().all()

        # Convert to Pydantic models within the async session context
        return [SimplifiedSchedulerResponse.model_validate(s) for s in due_schedulers]

    async def get_due_schedulers_batch(
        self, batch_size: int = 50
    ) -> AsyncGenerator[List[SimplifiedSchedulerResponse], None]:
        """
        Generator that yields batches of due schedulers for memory-efficient processing.
        """
        offset = 0
        while True:
            batch = await self.get_due_schedulers(limit=batch_size, offset=offset)
            if not batch:
                break
            yield batch
            offset += batch_size

    async def mark_scheduler_processing(self, scheduler_id: str) -> bool:
        """
        Atomically mark a scheduler as being processed to prevent duplicate execution.
        """
        now_utc = datetime.now(timezone.utc)

        # Use UPDATE with WHERE to atomically check and update
        stmt = text(
            """
            UPDATE schedulers
            SET
                last_run_at = :now,
                scheduler_metadata = COALESCE(scheduler_metadata, '{}') || '{"processing": true}'::jsonb
            WHERE
                id = :scheduler_id
                AND is_active = true
                AND next_run_at <= :now
                AND (scheduler_metadata->>'processing' IS NULL OR scheduler_metadata->>'processing' = 'false')
            RETURNING id
        """
        )

        result = await self.db.execute(
            stmt, {"scheduler_id": scheduler_id, "now": now_utc}
        )

        return result.rowcount > 0

    async def reset_scheduler_processing(self, scheduler_id: str) -> bool:
        """
        Reset a scheduler's processing status (mark as not processing).

        Args:
            scheduler_id: ID of the scheduler to reset

        Returns:
            bool: True if successfully reset, False if not found
        """
        stmt = text(
            """
            UPDATE schedulers
            SET
                scheduler_metadata = COALESCE(scheduler_metadata, '{}') || '{"processing": false}'::jsonb
            WHERE
                id = :scheduler_id
            RETURNING id
        """
        )

        result = await self.db.execute(stmt, {"scheduler_id": scheduler_id})
        return result.rowcount > 0

    async def update_scheduler_next_run(self, scheduler_id: str, schedule: str) -> bool:
        """
        Update a scheduler's next run time based on its schedule.

        Args:
            scheduler_id: ID of the scheduler to update
            schedule: Cron schedule string

        Returns:
            bool: True if successfully updated, False if not found
        """
        from src.utils.schedule_parser import parse_schedule

        try:
            # Calculate next run time
            next_run_at = parse_schedule(schedule)

            stmt = text(
                """
                UPDATE schedulers
                SET
                    next_run_at = :next_run_at,
                    scheduler_metadata = COALESCE(scheduler_metadata, '{}') || '{"processing": false}'::jsonb
                WHERE
                    id = :scheduler_id
                RETURNING id
            """
            )

            result = await self.db.execute(
                stmt, {"scheduler_id": scheduler_id, "next_run_at": next_run_at}
            )
            return result.rowcount > 0

        except Exception as e:
            logger.error(f"Error updating scheduler {scheduler_id} next run time: {e}")
            return False

    # Legacy method names for backward compatibility
    async def create_scheduler(
        self, scheduler_data: SimplifiedSchedulerCreate
    ) -> SimplifiedSchedulerResponse:
        """
        DEPRECATED: Use create() instead. Creates a new scheduler entry.
        """
        user_id = scheduler_data.user_id or "system"
        return await self.create(scheduler_data, user_id)

    async def get_scheduler(
        self, scheduler_id: str
    ) -> Optional[SimplifiedSchedulerResponse]:
        """
        DEPRECATED: Use get() instead. Retrieves a scheduler by its ID.
        Note: This method doesn't enforce user isolation for backward compatibility.
        """
        stmt = select(Scheduler).where(Scheduler.id == scheduler_id)
        result = await self.db.execute(stmt)
        scalars = result.scalars()
        db_scheduler = scalars.first()
        if db_scheduler:
            return SimplifiedSchedulerResponse.model_validate(db_scheduler)
        return None

    async def update_scheduler(
        self, scheduler_id: str, scheduler_data: SimplifiedSchedulerUpdate
    ) -> Optional[SimplifiedSchedulerResponse]:
        """
        DEPRECATED: Use update() instead. Updates an existing scheduler entry.
        Note: This method doesn't enforce user isolation for backward compatibility.
        """
        # Get the scheduler first to find the user_id
        existing = await self.get_scheduler(scheduler_id)
        if not existing:
            return None
        return await self.update(scheduler_id, scheduler_data, existing.user_id)

    async def delete_scheduler(self, scheduler_id: str) -> bool:
        """
        DEPRECATED: Use delete() instead. Deletes a scheduler by its ID.
        Note: This method doesn't enforce user isolation for backward compatibility.
        """
        # Get the scheduler first to find the user_id
        existing = await self.get_scheduler(scheduler_id)
        if not existing:
            return False
        return await self.delete(scheduler_id, existing.user_id)

    async def list_schedulers(
        self, skip: int = 0, limit: int = 100
    ) -> List[SimplifiedSchedulerResponse]:
        """
        DEPRECATED: Use list() instead. Lists all schedulers with pagination.
        Note: This method doesn't enforce user isolation for backward compatibility.
        """
        stmt = select(Scheduler).offset(skip).limit(limit)
        result = await self.db.execute(stmt)
        scalars = result.scalars()
        schedulers = scalars.all()
        return [SimplifiedSchedulerResponse.model_validate(s) for s in schedulers]
