import asyncio
import time
from typing import Optional
from abc import ABC, abstractmethod
import redis.asyncio as redis
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from src.utils.logger import get_logger

logger = get_logger(__name__)


class DistributedLock(ABC):
    """Abstract base class for distributed locking mechanisms."""

    @abstractmethod
    async def acquire(self, key: str, ttl: int = 300) -> bool:
        """Acquire a distributed lock."""
        pass

    @abstractmethod
    async def release(self, key: str) -> bool:
        """Release a distributed lock."""
        pass


class RedisDistributedLock(DistributedLock):
    """Redis-based distributed locking implementation."""

    def __init__(self, redis_url: str):
        self.redis_client = redis.from_url(redis_url)
        self.lock_prefix = "scheduler_lock:"

    async def acquire(self, key: str, ttl: int = 300) -> bool:
        """Acquire lock using Redis SET with NX and EX options."""
        lock_key = f"{self.lock_prefix}{key}"
        try:
            result = await self.redis_client.set(
                lock_key,
                f"{time.time()}:{asyncio.current_task().get_name()}",
                nx=True,
                ex=ttl,
            )
            if result:
                logger.debug(f"Acquired Redis lock for key: {key}")
            else:
                logger.debug(f"Failed to acquire Redis lock for key: {key}")
            return result is not None
        except Exception as e:
            logger.error(
                f"Error acquiring Redis lock for key {key}: {e}", exc_info=True
            )
            return False

    async def release(self, key: str) -> bool:
        """Release lock by deleting the Redis key."""
        lock_key = f"{self.lock_prefix}{key}"
        try:
            result = await self.redis_client.delete(lock_key)
            if result > 0:
                logger.debug(f"Released Redis lock for key: {key}")
            else:
                logger.debug(f"Redis lock for key {key} not found or already released")
            return result > 0
        except Exception as e:
            logger.error(
                f"Error releasing Redis lock for key {key}: {e}", exc_info=True
            )
            return False


class DatabaseDistributedLock(DistributedLock):
    """Database-based distributed locking using advisory locks."""

    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session

    async def acquire(self, key: str, ttl: int = 300) -> bool:
        """Acquire PostgreSQL advisory lock."""
        # Using a consistent hash for the lock ID
        lock_id = hash(key) % (2**31 - 1)  # Ensure positive and within int range
        try:
            # pg_try_advisory_lock returns true if lock is acquired, false otherwise
            result = await self.db_session.execute(
                text("SELECT pg_try_advisory_lock(:lock_id)"), {"lock_id": lock_id}
            )
            acquired = result.scalar()
            if acquired:
                logger.debug(
                    f"Acquired DB advisory lock for key: {key} (ID: {lock_id})"
                )
            else:
                logger.debug(
                    f"Failed to acquire DB advisory lock for key: {key} (ID: {lock_id})"
                )
            return acquired
        except Exception as e:
            logger.error(
                f"Error acquiring DB advisory lock for key {key} (ID: {lock_id}): {e}",
                exc_info=True,
            )
            # Rollback the transaction in case of error during lock acquisition
            await self.db_session.rollback()
            return False

    async def release(self, key: str) -> bool:
        """Release PostgreSQL advisory lock."""
        lock_id = hash(key) % (2**31 - 1)
        try:
            # pg_advisory_unlock returns true if lock was held and released, false otherwise
            result = await self.db_session.execute(
                text("SELECT pg_advisory_unlock(:lock_id)"), {"lock_id": lock_id}
            )
            released = result.scalar()
            if released:
                logger.debug(
                    f"Released DB advisory lock for key: {key} (ID: {lock_id})"
                )
            else:
                logger.debug(
                    f"DB advisory lock for key {key} (ID: {lock_id}) not held or already released"
                )
            return released
        except Exception as e:
            logger.error(
                f"Error releasing DB advisory lock for key {key} (ID: {lock_id}): {e}",
                exc_info=True,
            )
            # Rollback the transaction in case of error during lock release
            await self.db_session.rollback()
            return False


class LockManager:
    """High-level lock manager with fallback mechanisms."""

    def __init__(
        self,
        primary_lock: DistributedLock,
        fallback_lock: Optional[DistributedLock] = None,
    ):
        self.primary_lock = primary_lock
        self.fallback_lock = fallback_lock
        logger.info("LockManager initialized")

    async def acquire_scheduler_lock(self, scheduler_id: str, ttl: int = 300) -> bool:
        """Acquire lock for scheduler execution."""
        lock_key = f"scheduler:{scheduler_id}"
        logger.debug(f"Attempting to acquire lock for scheduler: {scheduler_id}")

        # Try primary lock first
        if await self.primary_lock.acquire(lock_key, ttl):
            logger.debug(f"Acquired primary lock for scheduler: {scheduler_id}")
            return True

        # Fallback to secondary lock if available
        if self.fallback_lock:
            logger.debug(
                f"Primary lock failed, attempting fallback lock for scheduler: {scheduler_id}"
            )
            if await self.fallback_lock.acquire(lock_key, ttl):
                logger.debug(f"Acquired fallback lock for scheduler: {scheduler_id}")
                return True

        logger.warning(f"Failed to acquire any lock for scheduler: {scheduler_id}")
        return False

    async def release_scheduler_lock(self, scheduler_id: str) -> bool:
        """Release lock for scheduler execution."""
        lock_key = f"scheduler:{scheduler_id}"
        logger.debug(f"Attempting to release lock for scheduler: {scheduler_id}")

        # Try to release from primary lock
        primary_released = await self.primary_lock.release(lock_key)
        if primary_released:
            logger.debug(f"Released primary lock for scheduler: {scheduler_id}")

        # Try to release from fallback lock if available
        fallback_released = True  # Assume true if no fallback lock
        if self.fallback_lock:
            fallback_released = await self.fallback_lock.release(lock_key)
            if fallback_released:
                logger.debug(f"Released fallback lock for scheduler: {scheduler_id}")

        # Return True if either lock was successfully released
        if primary_released or fallback_released:
            logger.debug(f"Lock released for scheduler: {scheduler_id}")
            return True
        else:
            logger.warning(f"Failed to release any lock for scheduler: {scheduler_id}")
            return False
