import asyncio
from typing import Dict, Any
from datetime import datetime, timezone
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from src.database.models import SchedulerExecution
from src.core.workflow_executor import WorkflowExecutor
from src.database.connection import get_db_manager
from src.utils.logger import get_logger

logger = get_logger(__name__)


class WorkflowTaskHandler:
    """Handler for processing scheduler workflow tasks from the task queue."""

    def __init__(self, workflow_executor: WorkflowExecutor = None):
        self.workflow_executor = workflow_executor or WorkflowExecutor()
        self.db_manager = get_db_manager()

    async def handle_scheduler_workflow(
        self, payload: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Handle a scheduler workflow task.

        Args:
            payload: Task payload containing scheduler and workflow information

        Returns:
            Dict containing execution results
        """
        scheduler_id = payload.get("scheduler_id")
        execution_id = payload.get("execution_id")
        user_id = payload.get("user_id")
        workflow_id = payload.get("workflow_id")
        event_data = payload.get("event_data", {})

        logger.info(
            f"Processing scheduler workflow task for scheduler {scheduler_id}, execution {execution_id}"
        )

        start_time = datetime.now(timezone.utc)

        # Use a fresh database session for each task to avoid transaction conflicts
        async with self.db_manager.get_async_session(auto_commit=False) as db_session:
            try:
                # Get the execution record
                stmt = select(SchedulerExecution).where(
                    SchedulerExecution.id == execution_id
                )
                result = await db_session.execute(stmt)
                scheduler_execution = result.scalars().first()

                if not scheduler_execution:
                    raise ValueError(f"SchedulerExecution {execution_id} not found")

                # Update execution status to running
                scheduler_execution.status = "running"
                await db_session.commit()

                # Fetch workflow details
                workflow_data = await self.workflow_executor.fetch_workflow(workflow_id)
                if not workflow_data:
                    raise ValueError(
                        f"Failed to fetch workflow details for workflow_id: {workflow_id}"
                    )

                logger.info(
                    f"Executing workflow {workflow_id} for scheduler {scheduler_id}"
                )

                # Execute the workflow
                correlation_id = await self.workflow_executor.execute_workflow(
                    user_id=user_id,
                    workflow_id=workflow_id,
                    workflow_data=workflow_data,
                    event_data=event_data,
                )

                if not correlation_id:
                    raise ValueError(
                        f"Workflow execution failed for scheduler_id: {scheduler_id}"
                    )

                # Calculate execution duration
                end_time = datetime.now(timezone.utc)
                duration_ms = int((end_time - start_time).total_seconds() * 1000)

                # Update execution record with success
                scheduler_execution.status = "completed"
                scheduler_execution.result = {
                    "correlation_id": correlation_id,
                    "workflow_id": workflow_id,
                    "execution_time": start_time.isoformat(),
                    "success": True,
                }
                scheduler_execution.duration_ms = duration_ms

                await db_session.commit()

                logger.info(
                    f"Workflow {workflow_id} for scheduler {scheduler_id} executed successfully "
                    f"with correlation_id: {correlation_id} in {duration_ms}ms"
                )

                return {
                    "success": True,
                    "correlation_id": correlation_id,
                    "workflow_id": workflow_id,
                    "scheduler_id": scheduler_id,
                    "execution_id": execution_id,
                    "duration_ms": duration_ms,
                }

            except Exception as e:
                # Calculate execution duration even for failures
                end_time = datetime.now(timezone.utc)
                duration_ms = int((end_time - start_time).total_seconds() * 1000)

                logger.error(
                    f"Failed to execute workflow {workflow_id} for scheduler {scheduler_id}: {e}",
                    exc_info=True,
                )

                # Update execution record with failure
                try:
                    if "scheduler_execution" in locals():
                        scheduler_execution.status = "failed"
                        scheduler_execution.error_message = str(e)
                        scheduler_execution.duration_ms = duration_ms
                        scheduler_execution.result = {
                            "success": False,
                            "error": str(e),
                            "execution_time": start_time.isoformat(),
                        }
                        await db_session.commit()
                except Exception as commit_error:
                    logger.error(
                        f"Failed to update execution record after workflow failure: {commit_error}"
                    )

                # Re-raise the exception to mark the task as failed
                raise

    def get_handlers(self) -> Dict[str, callable]:
        """Get a dictionary of task type to handler mappings."""
        return {
            "scheduler_workflow": self.handle_scheduler_workflow,
        }
