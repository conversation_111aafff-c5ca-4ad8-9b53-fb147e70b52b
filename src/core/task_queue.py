import asyncio
import json
from typing import Dict, Any, Optional, Callable, List
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
import redis.asyncio as redis
from datetime import datetime, timedelta
from src.utils.logger import get_logger
from src.core.metrics import TaskQueueMetrics

logger = get_logger(__name__)


class TaskStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"


@dataclass
class Task:
    id: str
    queue_name: str
    task_type: str
    payload: Dict[str, Any]
    status: TaskStatus
    created_at: datetime
    scheduled_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    retry_count: int = 0
    max_retries: int = 3
    error_message: Optional[str] = None


class TaskQueue(ABC):
    """Abstract base class for task queue implementations."""

    @abstractmethod
    async def enqueue(self, task: Task) -> str:
        """Enqueue a task for processing."""
        pass

    @abstractmethod
    async def dequeue(self, queue_name: str, timeout: int = 30) -> Optional[Task]:
        """Dequeue a task from the specified queue."""
        pass

    @abstractmethod
    async def complete_task(self, task_id: str, result: Dict[str, Any]) -> bool:
        """Mark a task as completed."""
        pass

    @abstractmethod
    async def fail_task(self, task_id: str, error: str, retry: bool = True) -> bool:
        """Mark a task as failed."""
        pass


class RedisTaskQueue(TaskQueue):
    """Redis-based task queue implementation."""

    def __init__(self, redis_url: str):
        self.redis_client = redis.from_url(redis_url)
        self.task_prefix = "task:"
        self.queue_prefix = "queue:"
        self.processing_prefix = "processing:"
        self.metrics = TaskQueueMetrics()
        logger.info("RedisTaskQueue initialized")

    async def enqueue(self, task: Task) -> str:
        """Enqueue task to Redis list or sorted set for delayed tasks."""
        task_key = f"{self.task_prefix}{task.id}"
        queue_key = f"{self.queue_prefix}{task.queue_name}"

        # Store task data - filter out None values for Redis compatibility
        task_data = {
            "id": task.id,
            "queue_name": task.queue_name,
            "task_type": task.task_type,
            "payload": json.dumps(task.payload),
            "status": task.status.value,
            "created_at": task.created_at.isoformat(),
            "retry_count": task.retry_count,
            "max_retries": task.max_retries,
            "error_message": task.error_message
            or "",  # Ensure error_message is always present
        }

        # Only add scheduled_at if it's not None (Redis doesn't accept None values)
        if task.scheduled_at:
            task_data["scheduled_at"] = task.scheduled_at.isoformat()

        await self.redis_client.hset(task_key, mapping=task_data)
        # Set a reasonable TTL for task data, e.g., 7 days (604800 seconds)
        await self.redis_client.expire(task_key, 604800)

        # Add to queue
        if task.scheduled_at and task.scheduled_at > datetime.utcnow():
            # Delayed task - add to sorted set with timestamp score
            await self.redis_client.zadd(
                f"{queue_key}:delayed", {task.id: task.scheduled_at.timestamp()}
            )
            logger.debug(
                f"Enqueued delayed task {task.id} to queue {task.queue_name} scheduled for {task.scheduled_at}"
            )
        else:
            # Immediate task - add to list
            await self.redis_client.lpush(queue_key, task.id)
            logger.debug(
                f"Enqueued immediate task {task.id} to queue {task.queue_name}"
            )

        # Record metrics
        self.metrics.record_task_enqueued(task.queue_name)

        return task.id

    async def dequeue(self, queue_name: str, timeout: int = 30) -> Optional[Task]:
        """Dequeue task with delayed task processing."""
        queue_key = f"{self.queue_prefix}{queue_name}"
        delayed_key = f"{queue_key}:delayed"
        processing_key = f"{self.processing_prefix}{queue_name}"

        # First, move any ready delayed tasks to the main queue
        now = datetime.utcnow().timestamp()
        ready_tasks = await self.redis_client.zrangebyscore(
            delayed_key, 0, now, withscores=False
        )

        if ready_tasks:
            # Move ready tasks to main queue
            pipe = self.redis_client.pipeline()
            for task_id in ready_tasks:
                pipe.lpush(queue_key, task_id)
                pipe.zrem(delayed_key, task_id)
            await pipe.execute()
            logger.debug(
                f"Moved {len(ready_tasks)} delayed tasks to main queue {queue_name}"
            )

        # Dequeue from main queue with timeout
        result = await self.redis_client.brpoplpush(queue_key, processing_key, timeout)

        if not result:
            return None

        task_id = result.decode("utf-8")
        task_key = f"{self.task_prefix}{task_id}"

        # Load task data
        task_data = await self.redis_client.hgetall(task_key)
        if not task_data:
            logger.warning(f"Task data not found for dequeued task ID: {task_id}")
            # Attempt to remove from processing queue if data is missing
            await self._remove_from_processing(task_id, processing_key)
            return None

        # Convert to Task object
        try:
            task = Task(
                id=task_data[b"id"].decode("utf-8"),
                queue_name=task_data[b"queue_name"].decode("utf-8"),
                task_type=task_data[b"task_type"].decode("utf-8"),
                payload=json.loads(task_data[b"payload"].decode("utf-8")),
                status=TaskStatus(task_data[b"status"].decode("utf-8")),
                created_at=datetime.fromisoformat(
                    task_data[b"created_at"].decode("utf-8")
                ),
                scheduled_at=(
                    datetime.fromisoformat(task_data[b"scheduled_at"].decode("utf-8"))
                    if task_data.get(b"scheduled_at") and task_data[b"scheduled_at"]
                    else None
                ),
                retry_count=int(task_data[b"retry_count"]),
                max_retries=int(task_data[b"max_retries"]),
                error_message=task_data.get(b"error_message", b"").decode("utf-8")
                or None,  # Handle potential missing key
            )
        except Exception as e:
            logger.error(
                f"Failed to parse task data for task ID {task_id}: {e}", exc_info=True
            )
            # Mark as failed if parsing fails
            await self.fail_task(
                task_id, f"Failed to parse task data: {e}", retry=False
            )
            return None

        # Update task status
        task.status = TaskStatus.RUNNING
        task.started_at = datetime.utcnow()
        await self._update_task(task)

        logger.debug(f"Dequeued task {task.id} from queue {queue_name}")

        # Record metrics
        self.metrics.record_task_dequeued(queue_name)

        return task

    async def complete_task(self, task_id: str, result: Dict[str, Any]) -> bool:
        """Mark task as completed and remove from processing."""
        task_key = f"{self.task_prefix}{task_id}"

        # Update task status
        await self.redis_client.hset(
            task_key,
            mapping={
                "status": TaskStatus.COMPLETED.value,
                "completed_at": datetime.utcnow().isoformat(),
                "result": json.dumps(result),
            },
        )
        logger.debug(f"Marked task {task_id} as completed")

        # Remove from all processing queues
        await self._remove_from_processing_by_task_id(task_id)
        return True

    async def fail_task(self, task_id: str, error: str, retry: bool = True) -> bool:
        """Mark task as failed and handle retries."""
        task_key = f"{self.task_prefix}{task_id}"
        task_data = await self.redis_client.hgetall(task_key)

        if not task_data:
            logger.warning(f"Task data not found for failed task ID: {task_id}")
            # Attempt to remove from processing queue even if data is missing
            await self._remove_from_processing_by_task_id(task_id)
            return False

        retry_count = int(task_data.get(b"retry_count", 0))
        max_retries = int(task_data.get(b"max_retries", 3))
        queue_name = task_data.get(b"queue_name", b"").decode("utf-8")

        if retry and retry_count < max_retries:
            # Retry the task
            retry_count += 1
            # Exponential backoff with jitter: delay = min(max_delay, base * (2^retry_count) + random_jitter)
            base_delay = 5  # seconds
            max_delay = 300  # seconds (5 minutes)
            jitter = asyncio.get_event_loop().time() % base_delay  # Simple jitter
            delay = min(max_delay, base_delay * (2 ** (retry_count - 1)) + jitter)
            scheduled_at = datetime.utcnow() + timedelta(seconds=delay)

            await self.redis_client.hset(
                task_key,
                mapping={
                    "status": TaskStatus.RETRYING.value,
                    "retry_count": retry_count,
                    "error_message": error,
                    "scheduled_at": scheduled_at.isoformat(),
                },
            )

            # Re-queue as delayed task
            delayed_key = f"{self.queue_prefix}{queue_name}:delayed"
            await self.redis_client.zadd(
                delayed_key, {task_id: scheduled_at.timestamp()}
            )
            logger.warning(
                f"Task {task_id} failed, retrying ({retry_count}/{max_retries}) scheduled for {scheduled_at}"
            )
        else:
            # Final failure
            await self.redis_client.hset(
                task_key,
                mapping={
                    "status": TaskStatus.FAILED.value,
                    "completed_at": datetime.utcnow().isoformat(),
                    "error_message": error,
                },
            )
            logger.error(
                f"Task {task_id} failed after {retry_count} retries. Error: {error}"
            )

        # Remove from processing
        await self._remove_from_processing_by_task_id(task_id)
        return True

    async def _update_task(self, task: Task):
        """Update task data in Redis."""
        task_key = f"{self.task_prefix}{task.id}"
        update_data = {"status": task.status.value, "retry_count": task.retry_count}

        if task.started_at:
            update_data["started_at"] = task.started_at.isoformat()
        if task.completed_at:
            update_data["completed_at"] = task.completed_at.isoformat()
        if task.error_message:
            update_data["error_message"] = task.error_message

        await self.redis_client.hset(task_key, mapping=update_data)

    async def _remove_from_processing_by_task_id(self, task_id: str):
        """Remove task from all processing queues by searching."""
        # This is less efficient than knowing the specific queue, but safer if queue name isn't readily available
        processing_keys = await self.redis_client.keys(f"{self.processing_prefix}*")
        pipe = self.redis_client.pipeline()
        for key in processing_keys:
            pipe.lrem(key, 0, task_id)
        await pipe.execute()
        logger.debug(f"Attempted to remove task {task_id} from all processing queues.")

    async def _remove_from_processing(self, task_id: str, processing_key: str):
        """Remove task from a specific processing queue."""
        await self.redis_client.lrem(processing_key, 0, task_id)
        logger.debug(f"Removed task {task_id} from processing queue {processing_key}")


class TaskWorker:
    """Generic task worker that processes tasks from queues."""

    def __init__(self, task_queue: TaskQueue, handlers: Dict[str, Callable]):
        self.task_queue = task_queue
        self.handlers = handlers
        self.running = False
        self.workers: List[asyncio.Task] = []
        self.metrics = TaskQueueMetrics()
        logger.info("TaskWorker initialized")

    async def start(self, queue_names: List[str], concurrency: int = 5):
        """Start processing tasks from specified queues."""
        if self.running:
            logger.warning("TaskWorker is already running.")
            return

        self.running = True
        logger.info(
            f"Starting TaskWorker with concurrency {concurrency} for queues: {queue_names}"
        )

        # Create worker coroutines
        for i in range(concurrency):
            for queue_name in queue_names:
                worker_task = asyncio.create_task(
                    self._worker(queue_name, f"worker-{i}-{queue_name}")
                )
                self.workers.append(worker_task)
                self.metrics.record_worker_started()

        # Wait for all workers to complete (they will run until stop is called)
        await asyncio.gather(*self.workers)
        logger.info("All TaskWorker coroutines finished.")

    async def stop(self):
        """Stop processing tasks."""
        if not self.running:
            logger.warning("TaskWorker is not running.")
            return

        logger.info("Stopping TaskWorker...")
        self.running = False

        # Cancel all worker tasks
        for worker in self.workers:
            worker.cancel()

        # Wait for tasks to be cancelled
        await asyncio.gather(*self.workers, return_exceptions=True)

        # Update metrics for stopped workers
        for _ in self.workers:
            self.metrics.record_worker_stopped()

        logger.info("TaskWorker stopped.")

    async def _worker(self, queue_name: str, worker_id: str):
        """Individual worker coroutine."""
        logger.info(f"Worker {worker_id} started for queue {queue_name}")

        while self.running:
            try:
                # Dequeue task with timeout
                task = await self.task_queue.dequeue(queue_name, timeout=5)

                if not task:
                    # No task available, continue loop
                    continue

                logger.info(
                    f"Worker {worker_id} processing task {task.id} of type {task.task_type}"
                )

                # Find and execute handler
                handler = self.handlers.get(task.task_type)
                if not handler:
                    logger.error(
                        f"No handler found for task type: {task.task_type} for task {task.id}"
                    )
                    await self.task_queue.fail_task(
                        task.id,
                        f"No handler found for task type: {task.task_type}",
                        retry=False,  # Do not retry if handler is missing
                    )
                    continue

                try:
                    # Execute task handler
                    import time

                    start_time = time.time()
                    result = await handler(task.payload)
                    processing_time = time.time() - start_time

                    await self.task_queue.complete_task(task.id, result or {})
                    self.metrics.record_task_completed(task.task_type, processing_time)
                    logger.info(f"Worker {worker_id} completed task {task.id}")

                except asyncio.CancelledError:
                    logger.info(f"Worker {worker_id} task {task.id} was cancelled.")
                    # Re-raise to allow cancellation to propagate
                    raise
                except Exception as e:
                    logger.error(
                        f"Worker {worker_id} failed to process task {task.id}: {e}",
                        exc_info=True,
                    )
                    await self.task_queue.fail_task(task.id, str(e))
                    self.metrics.record_task_failed(task.task_type, str(e))

            except asyncio.CancelledError:
                logger.info(f"Worker {worker_id} was cancelled.")
                break  # Exit loop on cancellation
            except Exception as e:
                logger.error(
                    f"Worker {worker_id} encountered error during dequeue or processing loop: {e}",
                    exc_info=True,
                )
                self.metrics.record_worker_error(str(e))
                await asyncio.sleep(1)  # Brief pause before retrying loop

        logger.info(f"Worker {worker_id} stopped")
