"""
Dead Letter Queue implementation for the Trigger Service.

This module provides functionality for handling permanently failed trigger executions,
including storage, analysis, manual retry capabilities, and cleanup policies.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple
from uuid import UUID

from sqlalchemy import and_, desc, func, select, update
from sqlalchemy.ext.asyncio import AsyncSession

from src.database.connection import get_async_session
from src.database.models import Trigger, TriggerExecution
from src.core.workflow_executor import WorkflowExecutor
from src.utils.config import get_settings
from src.utils.logger import get_logger
from src.utils.retry import RetryHandler

logger = get_logger(__name__)


class DeadLetterQueue:
    """
    Manages permanently failed trigger executions.

    This class provides functionality for:
    - Storing failed executions with full context
    - Manual retry capabilities
    - Failed execution analysis and reporting
    - Cleanup policies for old failures
    - Batch operations for efficiency
    """

    def __init__(self):
        """Initialize the Dead Letter Queue."""
        self.settings = get_settings()
        self.workflow_executor = WorkflowExecutor()
        self.retry_handler = RetryHandler()

    async def add_failed_execution(
        self,
        execution_id: UUID,
        final_error: str,
        context: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Add a permanently failed execution to the dead letter queue.

        Args:
            execution_id: ID of the failed execution
            final_error: Final error message
            context: Additional context about the failure

        Returns:
            bool: True if successfully added to DLQ
        """
        try:
            async with get_async_session() as session:
                # Update the execution status to permanently failed
                result = await session.execute(
                    update(TriggerExecution)
                    .where(TriggerExecution.id == execution_id)
                    .values(
                        status="failed",
                        error_message=final_error,
                        completed_at=datetime.utcnow(),
                    )
                )

                if result.rowcount == 0:
                    logger.warning(
                        "Failed to update execution status for DLQ",
                        execution_id=execution_id,
                    )
                    return False

                await session.commit()

                logger.info(
                    "Added execution to dead letter queue",
                    execution_id=execution_id,
                    error=final_error,
                    context=context,
                )

                return True

        except Exception as e:
            logger.error(
                "Failed to add execution to dead letter queue",
                execution_id=execution_id,
                error=str(e),
            )
            return False

    async def get_failed_executions(
        self,
        limit: int = 100,
        offset: int = 0,
        trigger_id: Optional[UUID] = None,
        user_id: Optional[str] = None,
        since: Optional[datetime] = None,
    ) -> List[TriggerExecution]:
        """
        Retrieve failed executions from the dead letter queue.

        Args:
            limit: Maximum number of executions to return
            offset: Number of executions to skip
            trigger_id: Filter by specific trigger ID
            user_id: Filter by user ID
            since: Only return failures since this timestamp

        Returns:
            List[TriggerExecution]: List of failed executions
        """
        try:
            async with get_async_session() as session:
                # Build the query
                query = (
                    select(TriggerExecution)
                    .join(Trigger)
                    .where(TriggerExecution.status == "failed")
                    .where(TriggerExecution.completed_at.isnot(None))
                )

                # Apply filters
                if trigger_id:
                    query = query.where(TriggerExecution.trigger_id == trigger_id)

                if user_id:
                    query = query.where(Trigger.user_id == user_id)

                if since:
                    query = query.where(TriggerExecution.completed_at >= since)

                # Order by completion time (most recent first)
                query = query.order_by(desc(TriggerExecution.completed_at))

                # Apply pagination
                query = query.offset(offset).limit(limit)

                result = await session.execute(query)
                executions = result.scalars().all()

                logger.debug(
                    "Retrieved failed executions from DLQ",
                    count=len(executions),
                    trigger_id=trigger_id,
                    user_id=user_id,
                )

                return list(executions)

        except Exception as e:
            logger.error(
                "Failed to retrieve executions from dead letter queue", error=str(e)
            )
            return []

    async def retry_execution(
        self, execution_id: UUID, force: bool = False
    ) -> Tuple[bool, Optional[str]]:
        """
        Manually retry a failed execution.

        Args:
            execution_id: ID of the execution to retry
            force: Whether to force retry even if not retryable

        Returns:
            Tuple[bool, Optional[str]]: (Success, correlation_id or error_message)
        """
        try:
            async with get_async_session() as session:
                # Get the execution with its trigger
                result = await session.execute(
                    select(TriggerExecution, Trigger)
                    .join(Trigger)
                    .where(TriggerExecution.id == execution_id)
                )

                row = result.first()
                if not row:
                    return False, "Execution not found"

                execution, trigger = row

                # Check if execution can be retried
                if not force and not execution.is_retryable:
                    return False, "Execution is not retryable"

                # Reset execution status for retry
                execution.status = "retrying"
                execution.retry_count += 1
                execution.error_message = None
                execution.completed_at = None

                await session.commit()

                logger.info(
                    "Starting manual retry of failed execution",
                    execution_id=execution_id,
                    trigger_id=trigger.id,
                    retry_count=execution.retry_count,
                )

                # Execute the workflow
                correlation_id = await self.workflow_executor.execute_workflow(
                    user_id=trigger.user_id,
                    workflow_id=trigger.workflow_id,
                    workflow_data=trigger.trigger_config,
                    event_data=execution.event_data,
                )

                # Update execution status based on result
                if correlation_id:
                    execution.status = "success"
                    execution.workflow_execution_id = correlation_id
                    execution.completed_at = datetime.utcnow()
                    execution.error_message = None

                    await session.commit()

                    logger.info(
                        "Manual retry succeeded",
                        execution_id=execution_id,
                        correlation_id=correlation_id,
                    )

                    return True, correlation_id
                else:
                    execution.status = "failed"
                    execution.error_message = "Manual retry failed - workflow execution returned no correlation ID"
                    execution.completed_at = datetime.utcnow()

                    await session.commit()

                    logger.warning("Manual retry failed", execution_id=execution_id)

                    return False, execution.error_message

        except Exception as e:
            error_msg = f"Manual retry failed with exception: {str(e)}"
            logger.error(
                "Manual retry failed with exception",
                execution_id=execution_id,
                error=str(e),
            )

            # Try to update execution status
            try:
                async with get_async_session() as session:
                    await session.execute(
                        update(TriggerExecution)
                        .where(TriggerExecution.id == execution_id)
                        .values(
                            status="failed",
                            error_message=error_msg,
                            completed_at=datetime.utcnow(),
                        )
                    )
                    await session.commit()
            except Exception:
                pass  # Best effort

            return False, error_msg

    async def retry_batch(
        self, execution_ids: List[UUID], force: bool = False
    ) -> Dict[str, Any]:
        """
        Retry multiple failed executions in batch.

        Args:
            execution_ids: List of execution IDs to retry
            force: Whether to force retry even if not retryable

        Returns:
            Dict[str, Any]: Batch retry results with statistics
        """
        results: Dict[str, Any] = {
            "total": len(execution_ids),
            "successful": 0,
            "failed": 0,
            "skipped": 0,
            "details": [],
        }

        logger.info(
            "Starting batch retry of failed executions",
            count=len(execution_ids),
            force=force,
        )

        # Process executions in parallel with limited concurrency
        semaphore = asyncio.Semaphore(5)  # Limit to 5 concurrent retries

        async def retry_single(execution_id: UUID) -> Dict[str, Any]:
            async with semaphore:
                success, result = await self.retry_execution(execution_id, force)
                return {
                    "execution_id": str(execution_id),
                    "success": success,
                    "result": result,
                }

        # Execute all retries
        retry_tasks = [retry_single(exec_id) for exec_id in execution_ids]
        batch_results = await asyncio.gather(*retry_tasks, return_exceptions=True)

        # Process results
        for result in batch_results:
            if isinstance(result, Exception):
                results["failed"] += 1
                results["details"].append(
                    {
                        "execution_id": "unknown",
                        "success": False,
                        "result": f"Exception: {str(result)}",
                    }
                )
            elif isinstance(result, dict) and result.get("success"):
                results["successful"] += 1
                results["details"].append(result)
            elif isinstance(result, dict):
                results["failed"] += 1
                results["details"].append(result)
            else:
                results["failed"] += 1
                results["details"].append(
                    {
                        "execution_id": "unknown",
                        "success": False,
                        "result": f"Unexpected result type: {type(result)}",
                    }
                )

        logger.info(
            "Batch retry completed",
            total=results["total"],
            successful=results["successful"],
            failed=results["failed"],
        )

        return results

    async def get_failure_statistics(
        self, since: Optional[datetime] = None, user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get statistics about failed executions.

        Args:
            since: Only include failures since this timestamp
            user_id: Filter by user ID

        Returns:
            Dict[str, Any]: Failure statistics
        """
        try:
            async with get_async_session() as session:
                # Base query for failed executions
                base_query = (
                    select(TriggerExecution)
                    .join(Trigger)
                    .where(TriggerExecution.status == "failed")
                    .where(TriggerExecution.completed_at.isnot(None))
                )

                if since:
                    base_query = base_query.where(
                        TriggerExecution.completed_at >= since
                    )

                if user_id:
                    base_query = base_query.where(Trigger.user_id == user_id)

                # Total failed executions
                total_result = await session.execute(
                    select(func.count()).select_from(base_query.subquery())
                )
                total_failed = total_result.scalar() or 0

                # Failures by trigger type
                type_result = await session.execute(
                    select(
                        Trigger.trigger_type,
                        func.count(TriggerExecution.id).label("count"),
                    )
                    .select_from(
                        base_query.join(
                            Trigger, TriggerExecution.trigger_id == Trigger.id
                        ).subquery()
                    )
                    .group_by(Trigger.trigger_type)
                )
                failures_by_type = {row.trigger_type: row.count for row in type_result}

                # Recent failures (last 24 hours)
                recent_cutoff = datetime.utcnow() - timedelta(hours=24)
                recent_result = await session.execute(
                    select(func.count()).select_from(
                        base_query.where(
                            TriggerExecution.completed_at >= recent_cutoff
                        ).subquery()
                    )
                )
                recent_failures = recent_result.scalar() or 0

                # Most common error patterns
                error_result = await session.execute(
                    select(TriggerExecution.error_message, func.count().label("count"))
                    .select_from(base_query.subquery())
                    .where(TriggerExecution.error_message.isnot(None))
                    .group_by(TriggerExecution.error_message)
                    .order_by(desc(func.count()))
                    .limit(10)
                )
                common_errors = [
                    {"error": row.error_message, "count": row.count}
                    for row in error_result
                ]

                statistics = {
                    "total_failed_executions": total_failed,
                    "recent_failures_24h": recent_failures,
                    "failures_by_trigger_type": failures_by_type,
                    "common_error_patterns": common_errors,
                    "generated_at": datetime.utcnow().isoformat(),
                }

                logger.debug(
                    "Generated failure statistics",
                    total_failed=total_failed,
                    recent_failures=recent_failures,
                )

                return statistics

        except Exception as e:
            logger.error("Failed to generate failure statistics", error=str(e))
            return {
                "error": f"Failed to generate statistics: {str(e)}",
                "generated_at": datetime.utcnow().isoformat(),
            }

    async def cleanup_old_failures(
        self, older_than_days: int = 30, dry_run: bool = False
    ) -> Dict[str, Any]:
        """
        Clean up old failed executions from the dead letter queue.

        Args:
            older_than_days: Remove failures older than this many days
            dry_run: If True, only count what would be deleted

        Returns:
            Dict[str, Any]: Cleanup results
        """
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=older_than_days)

            async with get_async_session() as session:
                # Find executions to clean up
                query = (
                    select(TriggerExecution)
                    .where(TriggerExecution.status == "failed")
                    .where(TriggerExecution.completed_at.isnot(None))
                    .where(TriggerExecution.completed_at < cutoff_date)
                )

                result = await session.execute(query)
                executions_to_delete = result.scalars().all()

                count = len(executions_to_delete)

                if dry_run:
                    logger.info(
                        "Dry run: would delete old failed executions",
                        count=count,
                        cutoff_date=cutoff_date.isoformat(),
                    )

                    return {
                        "dry_run": True,
                        "would_delete_count": count,
                        "cutoff_date": cutoff_date.isoformat(),
                        "older_than_days": older_than_days,
                    }

                # Actually delete the executions
                if count > 0:
                    execution_ids = [exec.id for exec in executions_to_delete]

                    # Delete in batches to avoid large transactions
                    batch_size = 100
                    deleted_count = 0

                    for i in range(0, len(execution_ids), batch_size):
                        batch_ids = execution_ids[i : i + batch_size]

                        delete_result = await session.execute(
                            update(TriggerExecution)
                            .where(TriggerExecution.id.in_(batch_ids))
                            .values(status="deleted")  # Soft delete for audit trail
                        )

                        deleted_count += delete_result.rowcount
                        await session.commit()

                    logger.info(
                        "Cleaned up old failed executions",
                        deleted_count=deleted_count,
                        cutoff_date=cutoff_date.isoformat(),
                    )

                    return {
                        "dry_run": False,
                        "deleted_count": deleted_count,
                        "cutoff_date": cutoff_date.isoformat(),
                        "older_than_days": older_than_days,
                    }
                else:
                    logger.info(
                        "No old failed executions to clean up",
                        cutoff_date=cutoff_date.isoformat(),
                    )

                    return {
                        "dry_run": False,
                        "deleted_count": 0,
                        "cutoff_date": cutoff_date.isoformat(),
                        "older_than_days": older_than_days,
                    }

        except Exception as e:
            logger.error(
                "Failed to cleanup old failures",
                error=str(e),
                older_than_days=older_than_days,
            )
            return {
                "error": f"Cleanup failed: {str(e)}",
                "older_than_days": older_than_days,
            }

    async def get_dlq_health(self) -> Dict[str, Any]:
        """
        Get health information about the dead letter queue.

        Returns:
            Dict[str, Any]: DLQ health status
        """
        try:
            async with get_async_session() as session:
                # Count current failed executions
                failed_result = await session.execute(
                    select(func.count())
                    .select_from(TriggerExecution)
                    .where(TriggerExecution.status == "failed")
                    .where(TriggerExecution.completed_at.isnot(None))
                )
                current_failed = failed_result.scalar() or 0

                # Count recent failures (last hour)
                recent_cutoff = datetime.utcnow() - timedelta(hours=1)
                recent_result = await session.execute(
                    select(func.count())
                    .select_from(TriggerExecution)
                    .where(TriggerExecution.status == "failed")
                    .where(TriggerExecution.completed_at >= recent_cutoff)
                )
                recent_failed = recent_result.scalar() or 0

                # Determine health status
                if current_failed == 0:
                    status = "healthy"
                elif recent_failed > 10:  # More than 10 failures in last hour
                    status = "critical"
                elif current_failed > 100:  # More than 100 total failures
                    status = "warning"
                else:
                    status = "healthy"

                return {
                    "status": status,
                    "current_failed_executions": current_failed,
                    "recent_failures_1h": recent_failed,
                    "checked_at": datetime.utcnow().isoformat(),
                }

        except Exception as e:
            logger.error("Failed to check DLQ health", error=str(e))
            return {
                "status": "error",
                "error": str(e),
                "checked_at": datetime.utcnow().isoformat(),
            }
