#!/usr/bin/env python3
"""
Task Worker Runner Script

This script starts the Redis-based task queue worker for processing
background workflow execution tasks.
"""

import asyncio
import sys
import signal
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from core.task_queue import RedisTaskQueue, TaskWorker
from core.workflow_task_handler import WorkflowTaskHandler
from utils.config import get_settings
from utils.logger import get_logger

logger = get_logger(__name__)


async def main():
    """Main task worker function."""
    settings = get_settings()

    logger.info("🚀 Starting Task Queue Worker...")
    logger.info(f"Redis URL: {settings.task_queue_redis_url}")
    logger.info(f"Worker Concurrency: {settings.task_worker_concurrency}")
    logger.info(f"Worker Queues: {settings.task_worker_queues}")

    # Initialize Redis task queue
    task_queue = RedisTaskQueue(settings.task_queue_redis_url)

    # Initialize workflow task handler
    workflow_handler = WorkflowTaskHandler()

    # Get task handlers
    handlers = workflow_handler.get_handlers()

    # Initialize task worker
    worker = TaskWorker(task_queue, handlers)

    # Parse queue names from settings
    queue_names = [q.strip() for q in settings.task_worker_queues.split(",")]

    # Set up signal handlers for graceful shutdown
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        asyncio.create_task(worker.stop())

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        # Start the worker
        await worker.start(queue_names, settings.task_worker_concurrency)
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
    except Exception as e:
        logger.error(f"Task worker failed: {e}", exc_info=True)
        sys.exit(1)
    finally:
        logger.info("Task worker stopped")


if __name__ == "__main__":
    asyncio.run(main())
