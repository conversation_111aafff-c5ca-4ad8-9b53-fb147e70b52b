#!/usr/bin/env python3
"""
Startup script for the Trigger Service.

This script provides an easy way to start the trigger service with proper
environment setup and configuration validation.
"""

import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    # Load environment variables from .env file first
    env_file = project_root / ".env"
    if env_file.exists():
        from dotenv import load_dotenv

        load_dotenv(env_file)

    from src.main import main

    if __name__ == "__main__":
        # Set default environment if not specified
        if not os.getenv("DATABASE_URL"):
            print("⚠️  DATABASE_URL not set. Using default development database.")
            os.environ["DATABASE_URL"] = (
                "postgresql://trigger_user:trigger_password@localhost:5432/trigger_db"
            )

        if not os.getenv("SECRET_KEY"):
            print("⚠️  SECRET_KEY not set. Using development key (NOT FOR PRODUCTION).")
            os.environ["SECRET_KEY"] = "dev-secret-key-not-for-production"

        if not os.getenv("API_KEY"):
            print("⚠️  API_KEY not set. Using development key (NOT FOR PRODUCTION).")
            os.environ["API_KEY"] = "dev-api-key-not-for-production"
        else:
            print(f"✅ Using API_KEY from environment: {os.getenv('API_KEY')}")

        print("🚀 Starting Trigger Service...")
        main()

except ImportError as e:
    print(f"❌ Failed to import application: {e}")
    print("💡 Make sure you have installed the dependencies:")
    print("   poetry install")
    print("   or")
    print("   pip install -r requirements.txt")
    sys.exit(1)
except Exception as e:
    print(f"❌ Failed to start application: {e}")
    sys.exit(1)
