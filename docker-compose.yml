version: "3.8"

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: trigger-service-postgres
    environment:
      POSTGRES_DB: trigger_db
      POSTGRES_USER: trigger_user
      POSTGRES_PASSWORD: trigger_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U trigger_user -d trigger_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - trigger-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: trigger-service-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - trigger-network

  # Trigger Service Application
  trigger-service:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: trigger-service-app
    environment:
      - DEBUG=false
      - HOST=0.0.0.0
      - PORT=8000
      - LOG_LEVEL=INFO
      - LOG_FORMAT=json
      - DATABASE_URL=********************************************************/trigger_db
      - REDIS_URL=redis://redis:6379/0
      - TASK_QUEUE_REDIS_URL=redis://redis:6379/0
      - DISTRIBUTED_LOCK_REDIS_URL=redis://redis:6379/0
      - AUTH_SERVICE_URL=https://auth-service.example.com
      - AUTH_SERVICE_API_KEY=your-auth-service-api-key
      - WORKFLOW_SERVICE_URL=https://ruh-test-api.rapidinnovation.dev
      - WORKFLOW_SERVICE_API_KEY=your-workflow-service-api-key
      - GOOGLE_CALENDAR_WEBHOOK_URL=https://trigger-service.example.com/api/v1/webhooks/google-calendar
      - SECRET_KEY=your-secret-key-here
      - API_KEY=your-api-key-for-internal-services
      - PRODUCTION_MODE=true
      - ENABLE_CONCURRENT_PROCESSING=true
      - ENABLE_TASK_QUEUE=true
      - ENABLE_DISTRIBUTED_LOCKING=true
      - SCHEDULER_BATCH_SIZE=50
      - SCHEDULER_CONCURRENCY=10
      - MAX_CONCURRENT_SCHEDULERS=100
      - SEMAPHORE_LIMIT=10
      - TASK_WORKER_CONCURRENCY=5
      - ENABLE_METRICS=true
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - trigger-network
    restart: unless-stopped

  # Task Queue Worker (for background workflow execution)
  task-worker:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: trigger-service-task-worker
    environment:
      - DEBUG=false
      - LOG_LEVEL=INFO
      - LOG_FORMAT=json
      - DATABASE_URL=********************************************************/trigger_db
      - REDIS_URL=redis://redis:6379/0
      - TASK_QUEUE_REDIS_URL=redis://redis:6379/0
      - DISTRIBUTED_LOCK_REDIS_URL=redis://redis:6379/0
      - AUTH_SERVICE_URL=https://auth-service.example.com
      - AUTH_SERVICE_API_KEY=your-auth-service-api-key
      - WORKFLOW_SERVICE_URL=https://ruh-test-api.rapidinnovation.dev
      - WORKFLOW_SERVICE_API_KEY=your-workflow-service-api-key
      - SECRET_KEY=your-secret-key-here
      - API_KEY=your-api-key-for-internal-services
      - PRODUCTION_MODE=true
      - TASK_WORKER_CONCURRENCY=5
      - TASK_WORKER_QUEUES=workflow_execution,scheduler_tasks
      - ENABLE_METRICS=true
    command: ["python", "-m", "scripts.run_task_worker"]
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - trigger-network
    restart: unless-stopped

  # Scheduler Service (for running scheduled tasks)
  scheduler:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: trigger-service-scheduler
    environment:
      - DEBUG=false
      - LOG_LEVEL=INFO
      - LOG_FORMAT=json
      - DATABASE_URL=********************************************************/trigger_db
      - REDIS_URL=redis://redis:6379/0
      - TASK_QUEUE_REDIS_URL=redis://redis:6379/0
      - DISTRIBUTED_LOCK_REDIS_URL=redis://redis:6379/0
      - PRODUCTION_MODE=true
      - ENABLE_CONCURRENT_PROCESSING=true
      - ENABLE_TASK_QUEUE=true
      - ENABLE_DISTRIBUTED_LOCKING=true
      - SCHEDULER_BATCH_SIZE=50
      - SCHEDULER_CONCURRENCY=10
      - MAX_CONCURRENT_SCHEDULERS=100
      - SEMAPHORE_LIMIT=10
      - SCHEDULER_CYCLE_INTERVAL=30
      - ENABLE_METRICS=true
    command: ["python", "-m", "scripts.run_scheduler"]
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - trigger-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  trigger-network:
    driver: bridge
