#!/usr/bin/env python3
"""
Test script to verify the duplicate trigger handling fix.

This script tests the scenario where a user tries to create a trigger
that already exists for the same user/workflow/trigger_type combination.
"""

import asyncio
import sys
from uuid import uuid4

# Add the src directory to the path so we can import our modules
sys.path.insert(0, "src")

from core.trigger_manager import TriggerManager
from adapters.google_calendar import GoogleCalendarAdapter
from database.connection import get_db_manager


async def test_duplicate_trigger_handling():
    """Test that duplicate trigger creation is handled gracefully."""

    print("🧪 Testing duplicate trigger handling...")

    # Initialize trigger manager
    trigger_manager = TriggerManager()

    # Register Google Calendar adapter
    google_adapter = GoogleCalendarAdapter()
    trigger_manager.register_adapter(google_adapter)

    # Test data
    user_id = "test_user_123"
    workflow_id = "test_workflow_456"
    trigger_type = "google_calendar"
    trigger_name = "Test Calendar Trigger"
    trigger_config = {"calendar_id": "primary"}
    event_types = ["created", "updated"]

    try:
        print(f"📝 Creating first trigger...")

        # Create the first trigger
        first_trigger_id = await trigger_manager.create_trigger(
            user_id=user_id,
            workflow_id=workflow_id,
            trigger_type=trigger_type,
            trigger_name=trigger_name,
            trigger_config=trigger_config,
            event_types=event_types,
        )

        if first_trigger_id:
            print(f"✅ First trigger created successfully: {first_trigger_id}")
        else:
            print("❌ Failed to create first trigger")
            return False

        print(f"📝 Attempting to create duplicate trigger...")

        # Try to create the same trigger again (should return existing trigger ID)
        second_trigger_id = await trigger_manager.create_trigger(
            user_id=user_id,
            workflow_id=workflow_id,
            trigger_type=trigger_type,
            trigger_name="Different Name",  # Different name, but same user/workflow/type
            trigger_config={"calendar_id": "different_calendar"},  # Different config
            event_types=["deleted"],  # Different event types
        )

        if second_trigger_id:
            print(f"✅ Second trigger call returned: {second_trigger_id}")

            # Check if both IDs are the same (should be, since it's the same trigger)
            if first_trigger_id == second_trigger_id:
                print("✅ SUCCESS: Duplicate trigger returned existing trigger ID")
                print(f"   Both calls returned the same ID: {first_trigger_id}")
                return True
            else:
                print("❌ FAILURE: Different trigger IDs returned")
                print(f"   First ID: {first_trigger_id}")
                print(f"   Second ID: {second_trigger_id}")
                return False
        else:
            print("❌ Failed to handle duplicate trigger creation")
            return False

    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        return False

    finally:
        # Clean up - remove the test trigger
        if "first_trigger_id" in locals() and first_trigger_id:
            print(f"🧹 Cleaning up test trigger...")
            try:
                await trigger_manager.remove_trigger(first_trigger_id)
                print("✅ Test trigger cleaned up")
            except Exception as e:
                print(f"⚠️  Failed to clean up test trigger: {e}")


async def main():
    """Main test function."""
    print("🚀 Starting duplicate trigger handling test...\n")

    # Test the duplicate handling
    success = await test_duplicate_trigger_handling()

    print(f"\n{'='*50}")
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Duplicate trigger handling is working correctly")
    else:
        print("💥 TESTS FAILED!")
        print("❌ Duplicate trigger handling needs attention")
    print(f"{'='*50}")

    return success


if __name__ == "__main__":
    # Run the test
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
