"""
Integration tests for API endpoints.

This module tests the complete API flow including authentication,
request processing, database operations, and response formatting.
"""

import pytest
import asyncio
from fastapi.testclient import TestClient
from httpx import AsyncClient

from src.main import app
from src.database.connection import init_database, close_database
from tests.fixtures.database import test_session
from tests.fixtures.http import auth_headers


class TestAPIIntegration:
    """Integration tests for API endpoints."""
    
    @pytest.fixture(scope="class", autouse=True)
    async def setup_database(self):
        """Set up test database."""
        await init_database()
        yield
        await close_database()
    
    @pytest.fixture
    def client(self):
        """Create a test client."""
        return TestClient(app)
    
    @pytest.fixture
    async def async_client(self):
        """Create an async test client."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client
    
    def test_health_endpoint_integration(self, client):
        """Test basic health endpoint integration."""
        response = client.get("/api/v1/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "version" in data
    
    def test_detailed_health_endpoint_integration(self, client, auth_headers):
        """Test detailed health endpoint with authentication."""
        response = client.get(
            "/api/v1/health/detailed",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] in ["healthy", "degraded"]
        assert "dependencies" in data
        assert "timestamp" in data
    
    def test_trigger_crud_flow_integration(self, client, auth_headers):
        """Test complete trigger CRUD flow."""
        # Create trigger
        trigger_data = {
            "user_id": "integration-test-user",
            "workflow_id": "integration-test-workflow",
            "trigger_type": "google_calendar",
            "trigger_name": "Integration Test Trigger",
            "trigger_config": {
                "calendar_id": "<EMAIL>",
                "event_types": ["created", "updated"],
                "filters": {
                    "summary_contains": "meeting"
                }
            },
            "event_types": ["created", "updated"],
            "is_active": True
        }
        
        # POST - Create trigger
        create_response = client.post(
            "/api/v1/triggers",
            json=trigger_data,
            headers=auth_headers
        )
        
        assert create_response.status_code == 201
        created_trigger = create_response.json()
        assert created_trigger["trigger_name"] == "Integration Test Trigger"
        assert created_trigger["is_active"] is True
        trigger_id = created_trigger["id"]
        
        # GET - List triggers
        list_response = client.get(
            "/api/v1/triggers",
            headers=auth_headers,
            params={"user_id": "integration-test-user"}
        )
        
        assert list_response.status_code == 200
        triggers_list = list_response.json()
        assert len(triggers_list["triggers"]) >= 1
        assert any(t["id"] == trigger_id for t in triggers_list["triggers"])
        
        # GET - Get specific trigger
        get_response = client.get(
            f"/api/v1/triggers/{trigger_id}",
            headers=auth_headers
        )
        
        assert get_response.status_code == 200
        retrieved_trigger = get_response.json()
        assert retrieved_trigger["id"] == trigger_id
        assert retrieved_trigger["trigger_name"] == "Integration Test Trigger"
        
        # PUT - Update trigger (if implemented)
        update_data = {
            "trigger_name": "Updated Integration Test Trigger",
            "is_active": False
        }
        
        update_response = client.put(
            f"/api/v1/triggers/{trigger_id}",
            json=update_data,
            headers=auth_headers
        )
        
        # Note: Update might return 501 if not implemented
        if update_response.status_code != 501:
            assert update_response.status_code == 200
            updated_trigger = update_response.json()
            assert updated_trigger["trigger_name"] == "Updated Integration Test Trigger"
        
        # DELETE - Delete trigger
        delete_response = client.delete(
            f"/api/v1/triggers/{trigger_id}",
            headers=auth_headers
        )
        
        assert delete_response.status_code == 204
        
        # Verify deletion
        get_deleted_response = client.get(
            f"/api/v1/triggers/{trigger_id}",
            headers=auth_headers
        )
        
        assert get_deleted_response.status_code == 404
    
    def test_authentication_flow_integration(self, client):
        """Test authentication flow integration."""
        # Test without authentication
        response = client.get("/api/v1/triggers")
        assert response.status_code == 401
        
        # Test with invalid authentication
        invalid_headers = {"Authorization": "Bearer invalid-key"}
        response = client.get("/api/v1/triggers", headers=invalid_headers)
        assert response.status_code == 401
        
        # Test with valid authentication
        valid_headers = {"Authorization": "Bearer dev-api-key-not-for-production-use-only"}
        response = client.get("/api/v1/triggers", headers=valid_headers)
        assert response.status_code == 200
    
    def test_webhook_processing_integration(self, client):
        """Test webhook processing integration."""
        # Test Google Calendar webhook
        webhook_payload = {
            "kind": "api#channel",
            "id": "test-channel-integration",
            "resourceId": "test-resource-integration",
            "resourceUri": "https://www.googleapis.com/calendar/v3/calendars/<EMAIL>/events",
            "token": "test-token",
            "expiration": "1640995200000"
        }
        
        webhook_headers = {
            "X-Goog-Channel-ID": "test-channel-integration",
            "X-Goog-Channel-Token": "test-token",
            "X-Goog-Resource-ID": "test-resource-integration",
            "X-Goog-Resource-State": "exists"
        }
        
        response = client.post(
            "/api/v1/webhooks/google-calendar",
            json=webhook_payload,
            headers=webhook_headers
        )
        
        # Should process successfully even without triggers
        assert response.status_code in [200, 500]  # 500 if no triggers found
        
        # Test webhook verification
        verify_response = client.get(
            "/api/v1/webhooks/google-calendar/verify",
            params={"challenge": "test-challenge-integration"}
        )
        
        assert verify_response.status_code == 200
        assert verify_response.text == "test-challenge-integration"
    
    @pytest.mark.asyncio
    async def test_async_endpoint_integration(self, async_client, auth_headers):
        """Test async endpoint integration."""
        # Test async health check
        response = await async_client.get("/api/v1/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        
        # Test async trigger listing
        response = await async_client.get(
            "/api/v1/triggers",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "triggers" in data
    
    def test_error_handling_integration(self, client, auth_headers):
        """Test error handling integration."""
        # Test 404 for non-existent trigger
        response = client.get(
            "/api/v1/triggers/00000000-0000-0000-0000-000000000000",
            headers=auth_headers
        )
        
        assert response.status_code == 404
        error_data = response.json()
        assert "detail" in error_data
        
        # Test 422 for invalid data
        invalid_trigger_data = {
            "trigger_name": "Invalid Trigger"
            # Missing required fields
        }
        
        response = client.post(
            "/api/v1/triggers",
            json=invalid_trigger_data,
            headers=auth_headers
        )
        
        assert response.status_code == 422
        error_data = response.json()
        assert "detail" in error_data
    
    def test_pagination_integration(self, client, auth_headers):
        """Test pagination integration."""
        # Create multiple triggers first
        triggers_created = []
        for i in range(5):
            trigger_data = {
                "user_id": "pagination-test-user",
                "workflow_id": f"pagination-workflow-{i}",
                "trigger_type": "google_calendar",
                "trigger_name": f"Pagination Test Trigger {i}",
                "trigger_config": {
                    "calendar_id": f"test{i}@pagination.com",
                    "event_types": ["created"]
                },
                "event_types": ["created"],
                "is_active": True
            }
            
            response = client.post(
                "/api/v1/triggers",
                json=trigger_data,
                headers=auth_headers
            )
            
            if response.status_code == 201:
                triggers_created.append(response.json()["id"])
        
        # Test pagination
        response = client.get(
            "/api/v1/triggers",
            headers=auth_headers,
            params={
                "user_id": "pagination-test-user",
                "page": 1,
                "page_size": 2
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["triggers"]) <= 2
        assert data["page"] == 1
        assert data["page_size"] == 2
        
        # Cleanup
        for trigger_id in triggers_created:
            client.delete(
                f"/api/v1/triggers/{trigger_id}",
                headers=auth_headers
            )
    
    def test_cors_integration(self, client):
        """Test CORS integration."""
        # Test preflight request
        response = client.options(
            "/api/v1/health",
            headers={
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "GET",
                "Access-Control-Request-Headers": "Authorization"
            }
        )
        
        assert response.status_code == 200
        assert "Access-Control-Allow-Origin" in response.headers
    
    def test_content_type_handling_integration(self, client, auth_headers):
        """Test content type handling integration."""
        # Test JSON content type
        trigger_data = {
            "user_id": "content-type-user",
            "workflow_id": "content-type-workflow",
            "trigger_type": "google_calendar",
            "trigger_name": "Content Type Test Trigger",
            "trigger_config": {"calendar_id": "<EMAIL>"},
            "event_types": ["created"],
            "is_active": True
        }
        
        response = client.post(
            "/api/v1/triggers",
            json=trigger_data,
            headers={**auth_headers, "Content-Type": "application/json"}
        )
        
        assert response.status_code == 201
        assert response.headers["content-type"] == "application/json"
        
        # Cleanup
        if response.status_code == 201:
            trigger_id = response.json()["id"]
            client.delete(
                f"/api/v1/triggers/{trigger_id}",
                headers=auth_headers
            )
    
    def test_rate_limiting_integration(self, client, auth_headers):
        """Test rate limiting integration (if implemented)."""
        # Make multiple rapid requests
        responses = []
        for i in range(10):
            response = client.get("/api/v1/health", headers=auth_headers)
            responses.append(response.status_code)
        
        # All should succeed if no rate limiting, or some should be 429
        assert all(status in [200, 429] for status in responses)
    
    def test_request_id_integration(self, client):
        """Test request ID integration."""
        response = client.get("/api/v1/health")
        
        assert response.status_code == 200
        # Check if correlation ID header is present (if implemented)
        if "X-Correlation-ID" in response.headers:
            assert len(response.headers["X-Correlation-ID"]) > 0
