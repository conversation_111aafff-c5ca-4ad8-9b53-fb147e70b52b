"""
Comprehensive End-to-End Integration Test for Simplified Scheduler System

This test verifies the complete scheduler workflow using the new simplified schema:
- Database operations with all frequency types
- Schedule parsing and next run time calculations
- API endpoints integration
- Timezone handling
- Error handling scenarios
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
import pytz
import os

from src.main import app
from src.database.models import Base, Scheduler
from src.database.connection import get_db
from src.core.scheduler_manager import SchedulerManager
from src.utils.schedule_parser import ScheduleParser
from src.schemas.scheduler import (
    SimplifiedSchedulerCreate,
    SimplifiedSchedulerUpdate,
    SimplifiedSchedulerResponse,
    ScheduleFrequency,
)

# Use a separate test database
DATABASE_URL = "sqlite+aiosqlite:///./test_scheduler_e2e.db"


@pytest.fixture(scope="module")
def test_app():
    """Set up test application with test database."""

    async def override_get_db():
        engine = create_async_engine(DATABASE_URL, echo=False)
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        AsyncSessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=engine,
            class_=AsyncSession,
            expire_on_commit=False,
        )
        async with AsyncSessionLocal() as session:
            yield session

        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
        await engine.dispose()

    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as client:
        yield client

    # Clean up the test database file after tests
    if os.path.exists("./test_scheduler_e2e.db"):
        os.remove("./test_scheduler_e2e.db")


@pytest.fixture(autouse=True)
def mock_auth_middleware(monkeypatch):
    """Mock authentication middleware for testing."""

    async def mock_verify_token(request):
        request.state.user_id = "test_user_e2e"
        request.state.is_admin = True

    monkeypatch.setattr("src.api.middleware.auth.verify_token", mock_verify_token)


def get_auth_headers(user_id: str = "test_user_e2e"):
    """Helper to get mock auth headers for testing."""
    return {"X-Test-User-Id": user_id}


class TestSchedulerE2E:
    """Comprehensive end-to-end tests for the simplified scheduler system."""

    @pytest.mark.asyncio
    async def test_every_minute_frequency_complete_workflow(self, test_app):
        """Test complete workflow for every_minute frequency."""
        # Create scheduler
        create_data = {
            "name": "Every Minute Test",
            "workflow_id": "wf_every_minute_123",
            "frequency": "every_minute",
            "timezone": "UTC",
            "is_active": True,
        }

        response = test_app.post(
            "/api/v1/schedulers/schedulers",
            json=create_data,
            headers=get_auth_headers(),
        )
        assert response.status_code == 201

        scheduler_data = response.json()
        scheduler_id = scheduler_data["id"]

        # Verify database storage
        assert scheduler_data["frequency"] == "every_minute"
        assert scheduler_data["time"] is None
        assert scheduler_data["days_of_week"] is None
        assert scheduler_data["days_of_month"] is None
        assert scheduler_data["cron_expression"] is None
        assert scheduler_data["next_run_at"] is not None

        # Verify next run time calculation
        next_run = datetime.fromisoformat(
            scheduler_data["next_run_at"].replace("Z", "+00:00")
        )
        now = datetime.now(pytz.utc)
        time_diff = (next_run - now).total_seconds()
        assert 0 < time_diff <= 60  # Should be within next minute

        # Test retrieval
        get_response = test_app.get(
            f"/api/v1/schedulers/schedulers/{scheduler_id}",
            headers=get_auth_headers(),
        )
        assert get_response.status_code == 200
        retrieved_data = get_response.json()
        assert retrieved_data["frequency"] == "every_minute"

        # Test update
        update_response = test_app.put(
            f"/api/v1/schedulers/schedulers/{scheduler_id}",
            json={"is_active": False},
            headers=get_auth_headers(),
        )
        assert update_response.status_code == 200
        assert update_response.json()["is_active"] is False

    @pytest.mark.asyncio
    async def test_hourly_frequency_complete_workflow(self, test_app):
        """Test complete workflow for hourly frequency."""
        create_data = {
            "name": "Hourly Test",
            "workflow_id": "wf_hourly_456",
            "frequency": "hourly",
            "timezone": "America/New_York",
            "is_active": True,
        }

        response = test_app.post(
            "/api/v1/schedulers/schedulers",
            json=create_data,
            headers=get_auth_headers(),
        )
        assert response.status_code == 201

        scheduler_data = response.json()
        assert scheduler_data["frequency"] == "hourly"
        assert scheduler_data["timezone"] == "America/New_York"

        # Verify next run time is approximately 1 hour from now
        next_run = datetime.fromisoformat(
            scheduler_data["next_run_at"].replace("Z", "+00:00")
        )
        now = datetime.now(pytz.utc)
        time_diff = (next_run - now).total_seconds()
        assert 3500 < time_diff <= 3700  # Should be around 1 hour (3600 seconds)

    @pytest.mark.asyncio
    async def test_daily_frequency_complete_workflow(self, test_app):
        """Test complete workflow for daily frequency."""
        create_data = {
            "name": "Daily Test",
            "workflow_id": "wf_daily_789",
            "frequency": "daily",
            "time": "14:30",
            "timezone": "Asia/Kolkata",
            "is_active": True,
        }

        response = test_app.post(
            "/api/v1/schedulers/schedulers",
            json=create_data,
            headers=get_auth_headers(),
        )
        assert response.status_code == 201

        scheduler_data = response.json()
        assert scheduler_data["frequency"] == "daily"
        assert scheduler_data["time"] == "14:30"
        assert scheduler_data["timezone"] == "Asia/Kolkata"

        # Verify next run time is at 14:30 in Asia/Kolkata timezone
        next_run = datetime.fromisoformat(
            scheduler_data["next_run_at"].replace("Z", "+00:00")
        )
        kolkata_tz = pytz.timezone("Asia/Kolkata")
        next_run_local = next_run.astimezone(kolkata_tz)
        assert next_run_local.hour == 14
        assert next_run_local.minute == 30

    @pytest.mark.asyncio
    async def test_weekly_frequency_complete_workflow(self, test_app):
        """Test complete workflow for weekly frequency."""
        create_data = {
            "name": "Weekly Test",
            "workflow_id": "wf_weekly_abc",
            "frequency": "weekly",
            "time": "09:15",
            "days_of_week": ["Monday", "Wednesday", "Friday"],
            "timezone": "Europe/London",
            "is_active": True,
        }

        response = test_app.post(
            "/api/v1/schedulers/schedulers",
            json=create_data,
            headers=get_auth_headers(),
        )
        assert response.status_code == 201

        scheduler_data = response.json()
        assert scheduler_data["frequency"] == "weekly"
        assert scheduler_data["time"] == "09:15"
        assert set(scheduler_data["days_of_week"]) == {"Monday", "Wednesday", "Friday"}
        assert scheduler_data["timezone"] == "Europe/London"

        # Verify next run time is on one of the specified days at 09:15
        next_run = datetime.fromisoformat(
            scheduler_data["next_run_at"].replace("Z", "+00:00")
        )
        london_tz = pytz.timezone("Europe/London")
        next_run_local = next_run.astimezone(london_tz)
        assert next_run_local.hour == 9
        assert next_run_local.minute == 15

        weekday_names = [
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday",
            "Saturday",
            "Sunday",
        ]
        next_weekday = weekday_names[next_run_local.weekday()]
        assert next_weekday in ["Monday", "Wednesday", "Friday"]

    @pytest.mark.asyncio
    async def test_monthly_frequency_complete_workflow(self, test_app):
        """Test complete workflow for monthly frequency."""
        create_data = {
            "name": "Monthly Test",
            "workflow_id": "wf_monthly_def",
            "frequency": "monthly",
            "time": "08:00",
            "days_of_month": [1, 15, 30],
            "timezone": "UTC",
            "is_active": True,
        }

        response = test_app.post(
            "/api/v1/schedulers/schedulers",
            json=create_data,
            headers=get_auth_headers(),
        )
        assert response.status_code == 201

        scheduler_data = response.json()
        assert scheduler_data["frequency"] == "monthly"
        assert scheduler_data["time"] == "08:00"
        assert set(scheduler_data["days_of_month"]) == {1, 15, 30}

        # Verify next run time is on one of the specified days at 08:00
        next_run = datetime.fromisoformat(
            scheduler_data["next_run_at"].replace("Z", "+00:00")
        )
        assert next_run.hour == 8
        assert next_run.minute == 0
        assert next_run.day in [1, 15, 30]

    @pytest.mark.asyncio
    async def test_custom_frequency_complete_workflow(self, test_app):
        """Test complete workflow for custom frequency with cron expression."""
        create_data = {
            "name": "Custom Cron Test",
            "workflow_id": "wf_custom_ghi",
            "frequency": "custom",
            "cron_expression": "30 10 * * 1-5",  # 10:30 AM on weekdays
            "timezone": "UTC",
            "is_active": True,
        }

        response = test_app.post(
            "/api/v1/schedulers/schedulers",
            json=create_data,
            headers=get_auth_headers(),
        )
        assert response.status_code == 201

        scheduler_data = response.json()
        assert scheduler_data["frequency"] == "custom"
        assert scheduler_data["cron_expression"] == "30 10 * * 1-5"

        # Verify next run time follows the cron expression
        next_run = datetime.fromisoformat(
            scheduler_data["next_run_at"].replace("Z", "+00:00")
        )
        assert next_run.hour == 10
        assert next_run.minute == 30
        assert next_run.weekday() < 5  # Monday=0, Friday=4

    @pytest.mark.asyncio
    async def test_timezone_handling_across_frequencies(self, test_app):
        """Test timezone handling across different frequency types."""
        timezones_to_test = [
            "UTC",
            "America/New_York",
            "Europe/London",
            "Asia/Tokyo",
            "Australia/Sydney",
        ]

        created_schedulers = []

        for i, tz in enumerate(timezones_to_test):
            create_data = {
                "name": f"Timezone Test {tz}",
                "workflow_id": f"wf_tz_{i}",
                "frequency": "daily",
                "time": "12:00",
                "timezone": tz,
                "is_active": True,
            }

            response = test_app.post(
                "/api/v1/schedulers/schedulers",
                json=create_data,
                headers=get_auth_headers(),
            )
            assert response.status_code == 201

            scheduler_data = response.json()
            created_schedulers.append(scheduler_data)

            # Verify timezone is stored correctly
            assert scheduler_data["timezone"] == tz

            # Verify next run time is calculated in the correct timezone
            next_run = datetime.fromisoformat(
                scheduler_data["next_run_at"].replace("Z", "+00:00")
            )
            local_tz = pytz.timezone(tz)
            next_run_local = next_run.astimezone(local_tz)
            assert next_run_local.hour == 12
            assert next_run_local.minute == 0

        # Verify all schedulers are created and have different next run times (due to timezones)
        assert len(created_schedulers) == 5
        next_run_times = [s["next_run_at"] for s in created_schedulers]
        assert (
            len(set(next_run_times)) > 1
        )  # Should have different times due to timezones

    @pytest.mark.asyncio
    async def test_validation_error_scenarios(self, test_app):
        """Test various validation error scenarios."""

        # Test missing required fields for each frequency
        test_cases = [
            {
                "name": "Daily without time",
                "data": {
                    "name": "Invalid Daily",
                    "workflow_id": "wf_invalid_1",
                    "frequency": "daily",
                    "timezone": "UTC",
                },
                "expected_error": "time is required for daily frequency",
            },
            {
                "name": "Weekly without time",
                "data": {
                    "name": "Invalid Weekly",
                    "workflow_id": "wf_invalid_2",
                    "frequency": "weekly",
                    "days_of_week": ["Monday"],
                    "timezone": "UTC",
                },
                "expected_error": "time is required for weekly frequency",
            },
            {
                "name": "Weekly without days_of_week",
                "data": {
                    "name": "Invalid Weekly 2",
                    "workflow_id": "wf_invalid_3",
                    "frequency": "weekly",
                    "time": "10:00",
                    "timezone": "UTC",
                },
                "expected_error": "days_of_week is required for weekly frequency",
            },
            {
                "name": "Monthly without time",
                "data": {
                    "name": "Invalid Monthly",
                    "workflow_id": "wf_invalid_4",
                    "frequency": "monthly",
                    "days_of_month": [1, 15],
                    "timezone": "UTC",
                },
                "expected_error": "time is required for monthly frequency",
            },
            {
                "name": "Monthly without days_of_month",
                "data": {
                    "name": "Invalid Monthly 2",
                    "workflow_id": "wf_invalid_5",
                    "frequency": "monthly",
                    "time": "10:00",
                    "timezone": "UTC",
                },
                "expected_error": "days_of_month is required for monthly frequency",
            },
            {
                "name": "Custom without cron_expression",
                "data": {
                    "name": "Invalid Custom",
                    "workflow_id": "wf_invalid_6",
                    "frequency": "custom",
                    "timezone": "UTC",
                },
                "expected_error": "cron_expression is required for custom frequency",
            },
            {
                "name": "Invalid time format",
                "data": {
                    "name": "Invalid Time Format",
                    "workflow_id": "wf_invalid_7",
                    "frequency": "daily",
                    "time": "25:00",  # Invalid hour
                    "timezone": "UTC",
                },
                "expected_error": "Time must be in HH:mm format",
            },
            {
                "name": "Invalid timezone",
                "data": {
                    "name": "Invalid Timezone",
                    "workflow_id": "wf_invalid_8",
                    "frequency": "daily",
                    "time": "10:00",
                    "timezone": "Invalid/Timezone",
                },
                "expected_error": "Invalid timezone",
            },
            {
                "name": "Invalid cron expression",
                "data": {
                    "name": "Invalid Cron",
                    "workflow_id": "wf_invalid_9",
                    "frequency": "custom",
                    "cron_expression": "invalid cron",
                    "timezone": "UTC",
                },
                "expected_error": "Cron expression must have exactly 5 parts",
            },
        ]

        for test_case in test_cases:
            response = test_app.post(
                "/api/v1/schedulers/schedulers",
                json=test_case["data"],
                headers=get_auth_headers(),
            )
            assert (
                response.status_code == 422
            ), f"Failed for test case: {test_case['name']}"

            error_detail = response.json()["detail"]
            # Check if the expected error message is in the response
            assert any(
                test_case["expected_error"] in str(error) for error in error_detail
            ), f"Expected error '{test_case['expected_error']}' not found in {error_detail}"

    @pytest.mark.asyncio
    async def test_schedule_parser_integration(self, test_app):
        """Test integration between API, database, and schedule parser."""

        # Create a scheduler
        create_data = {
            "name": "Parser Integration Test",
            "workflow_id": "wf_parser_test",
            "frequency": "weekly",
            "time": "15:45",
            "days_of_week": ["Tuesday", "Thursday"],
            "timezone": "America/Chicago",
            "is_active": True,
        }

        response = test_app.post(
            "/api/v1/schedulers/schedulers",
            json=create_data,
            headers=get_auth_headers(),
        )
        assert response.status_code == 201
        scheduler_data = response.json()

        # Test direct schedule parser functionality
        scheduler_response = SimplifiedSchedulerResponse(**scheduler_data)

        # Test next run time calculation
        next_run = ScheduleParser.get_next_run_time(scheduler_response)
        assert next_run is not None

        # Convert to Chicago timezone and verify
        chicago_tz = pytz.timezone("America/Chicago")
        next_run_local = next_run.astimezone(chicago_tz)
        assert next_run_local.hour == 15
        assert next_run_local.minute == 45

        weekday_names = [
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday",
            "Saturday",
            "Sunday",
        ]
        next_weekday = weekday_names[next_run_local.weekday()]
        assert next_weekday in ["Tuesday", "Thursday"]

        # Test with last run time
        last_run = datetime.now(pytz.utc) - timedelta(days=1)
        next_run_with_last = ScheduleParser.get_next_run_time(
            scheduler_response, last_run
        )
        assert next_run_with_last is not None
        assert next_run_with_last > last_run

    @pytest.mark.asyncio
    async def test_user_isolation_comprehensive(self, test_app):
        """Test comprehensive user isolation across all operations."""

        # Create schedulers for different users
        users = ["user_a", "user_b", "user_c"]
        user_schedulers = {}

        for user in users:
            headers = get_auth_headers(user)

            # Create multiple schedulers per user
            for i in range(3):
                create_data = {
                    "name": f"{user} Scheduler {i+1}",
                    "workflow_id": f"wf_{user}_{i+1}",
                    "frequency": "daily",
                    "time": f"{10+i}:00",
                    "timezone": "UTC",
                    "is_active": True,
                }

                response = test_app.post(
                    "/api/v1/schedulers/schedulers",
                    json=create_data,
                    headers=headers,
                )
                assert response.status_code == 201

                if user not in user_schedulers:
                    user_schedulers[user] = []
                user_schedulers[user].append(response.json())

        # Verify each user can only see their own schedulers
        for user in users:
            headers = get_auth_headers(user)

            # List schedulers
            response = test_app.get("/api/v1/schedulers/schedulers", headers=headers)
            assert response.status_code == 200

            user_list = response.json()
            assert len(user_list) == 3  # Each user should see exactly 3 schedulers

            # Verify all returned schedulers belong to the user
            for scheduler in user_list:
                assert any(
                    scheduler["id"] == user_scheduler["id"]
                    for user_scheduler in user_schedulers[user]
                )

            # Test cross-user access denial
            for other_user in users:
                if other_user != user:
                    for other_scheduler in user_schedulers[other_user]:
                        # Try to get another user's scheduler
                        response = test_app.get(
                            f"/api/v1/schedulers/schedulers/{other_scheduler['id']}",
                            headers=headers,
                        )
                        assert response.status_code == 404

                        # Try to update another user's scheduler
                        response = test_app.put(
                            f"/api/v1/schedulers/schedulers/{other_scheduler['id']}",
                            json={"is_active": False},
                            headers=headers,
                        )
                        assert response.status_code == 404

                        # Try to delete another user's scheduler
                        response = test_app.delete(
                            f"/api/v1/schedulers/schedulers/{other_scheduler['id']}",
                            headers=headers,
                        )
                        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_database_consistency_and_performance(self, test_app):
        """Test database consistency and basic performance characteristics."""

        # Create a large number of schedulers to test performance
        num_schedulers = 50
        created_schedulers = []

        start_time = datetime.now()

        for i in range(num_schedulers):
            create_data = {
                "name": f"Performance Test Scheduler {i+1}",
                "workflow_id": f"wf_perf_{i+1}",
                "frequency": "daily" if i % 2 == 0 else "weekly",
                "time": f"{(i % 12) + 8:02d}:00",
                "days_of_week": ["Monday", "Wednesday"] if i % 2 == 1 else None,
                "timezone": "UTC",
                "is_active": True,
            }

            response = test_app.post(
                "/api/v1/schedulers/schedulers",
                json=create_data,
                headers=get_auth_headers(),
            )
            assert response.status_code == 201
            created_schedulers.append(response.json())

        creation_time = (datetime.now() - start_time).total_seconds()
        print(f"Created {num_schedulers} schedulers in {creation_time:.2f} seconds")

        # Test bulk retrieval performance
        start_time = datetime.now()
        response = test_app.get(
            "/api/v1/schedulers/schedulers", headers=get_auth_headers()
        )
        retrieval_time = (datetime.now() - start_time).total_seconds()

        assert response.status_code == 200
        schedulers_list = response.json()
        assert len(schedulers_list) >= num_schedulers

        print(
            f"Retrieved {len(schedulers_list)} schedulers in {retrieval_time:.2f} seconds"
        )

        # Test individual retrieval and updates
        start_time = datetime.now()
        for i in range(min(10, len(created_schedulers))):  # Test first 10
            scheduler = created_schedulers[i]

            # Get individual scheduler
            get_response = test_app.get(
                f"/api/v1/schedulers/schedulers/{scheduler['id']}",
                headers=get_auth_headers(),
            )
            assert get_response.status_code == 200

            # Update scheduler
            update_response = test_app.put(
                f"/api/v1/schedulers/schedulers/{scheduler['id']}",
                json={"is_active": not scheduler["is_active"]},
                headers=get_auth_headers(),
            )
            assert update_response.status_code == 200

        update_time = (datetime.now() - start_time).total_seconds()
        print(f"Updated 10 schedulers in {update_time:.2f} seconds")

        # Verify data consistency
        for scheduler in created_schedulers[:5]:  # Check first 5
            response = test_app.get(
                f"/api/v1/schedulers/schedulers/{scheduler['id']}",
                headers=get_auth_headers(),
            )
            assert response.status_code == 200

            retrieved = response.json()
            assert retrieved["name"] == scheduler["name"]
            assert retrieved["workflow_id"] == scheduler["workflow_id"]
            assert retrieved["frequency"] == scheduler["frequency"]

    @pytest.mark.asyncio
    async def test_error_handling_and_recovery(self, test_app):
        """Test error handling and recovery scenarios."""

        # Test database constraint violations
        create_data = {
            "name": "Constraint Test",
            "workflow_id": "wf_constraint_test",
            "frequency": "daily",
            "time": "10:00",
            "timezone": "UTC",
            "is_active": True,
        }

        # Create first scheduler
        response = test_app.post(
            "/api/v1/schedulers/schedulers",
            json=create_data,
            headers=get_auth_headers(),
        )
        assert response.status_code == 201
        first_scheduler = response.json()

        # Test invalid updates
        invalid_updates = [
            {"frequency": "daily", "time": None},  # Missing required time for daily
            {
                "frequency": "weekly",
                "days_of_week": None,
            },  # Missing required days for weekly
            {"frequency": "custom", "cron_expression": None},  # Missing cron for custom
            {"timezone": "Invalid/Timezone"},  # Invalid timezone
        ]

        for invalid_update in invalid_updates:
            response = test_app.put(
                f"/api/v1/schedulers/schedulers/{first_scheduler['id']}",
                json=invalid_update,
                headers=get_auth_headers(),
            )
            assert response.status_code == 422

        # Verify original scheduler is unchanged after failed updates
        response = test_app.get(
            f"/api/v1/schedulers/schedulers/{first_scheduler['id']}",
            headers=get_auth_headers(),
        )
        assert response.status_code == 200
        unchanged_scheduler = response.json()
        assert unchanged_scheduler["frequency"] == first_scheduler["frequency"]
        assert unchanged_scheduler["time"] == first_scheduler["time"]
        assert unchanged_scheduler["timezone"] == first_scheduler["timezone"]

        # Test operations on non-existent schedulers
        non_existent_id = "00000000-0000-0000-0000-000000000000"

        response = test_app.get(
            f"/api/v1/schedulers/schedulers/{non_existent_id}",
            headers=get_auth_headers(),
        )
        assert response.status_code == 404

        response = test_app.put(
            f"/api/v1/schedulers/schedulers/{non_existent_id}",
            json={"is_active": False},
            headers=get_auth_headers(),
        )
        assert response.status_code == 404

        response = test_app.delete(
            f"/api/v1/schedulers/schedulers/{non_existent_id}",
            headers=get_auth_headers(),
        )
        assert response.status_code == 404


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
