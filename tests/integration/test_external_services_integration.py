"""
Integration tests for external service interactions.

This module tests the integration with external services like
Google Calendar API, workflow service, and auth service.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from src.core.auth_client import AuthClient
from src.core.workflow_executor import WorkflowExecutor
from src.adapters.google_calendar import GoogleCalendarAdapter
from src.core.dead_letter_queue import DeadLetterQueue
from tests.fixtures.mocks import (
    mock_settings,
    mock_logger,
    mock_google_calendar_service,
    mock_google_credentials
)


class TestExternalServicesIntegration:
    """Integration tests for external service interactions."""
    
    @pytest.mark.asyncio
    async def test_auth_service_integration(self, mock_settings, mock_logger):
        """Test integration with auth service."""
        with patch("src.core.auth_client.get_settings", return_value=mock_settings), \
             patch("src.core.auth_client.get_logger", return_value=mock_logger):
            
            auth_client = AuthClient()
            
            # Mock successful auth service response
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "credentials": {
                    "google_calendar": {
                        "access_token": "test-access-token",
                        "refresh_token": "test-refresh-token",
                        "expires_at": "2024-12-31T23:59:59Z"
                    }
                }
            }
            
            with patch("httpx.AsyncClient") as mock_client_class:
                mock_client = AsyncMock()
                mock_client.get = AsyncMock(return_value=mock_response)
                mock_client_class.return_value.__aenter__.return_value = mock_client
                
                # Test credential retrieval
                credentials = await auth_client.get_credentials("test-user", "google_calendar")
                
                assert credentials is not None
                assert credentials["access_token"] == "test-access-token"
                mock_client.get.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_workflow_service_integration(self, mock_settings, mock_logger):
        """Test integration with workflow service."""
        with patch("src.core.workflow_executor.get_settings", return_value=mock_settings), \
             patch("src.core.workflow_executor.get_logger", return_value=mock_logger):
            
            workflow_executor = WorkflowExecutor()
            
            # Mock successful workflow service response
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "correlationId": "test-correlation-123",
                "status": "initiated",
                "workflowId": "test-workflow-456"
            }
            
            with patch("httpx.AsyncClient") as mock_client_class:
                mock_client = AsyncMock()
                mock_client.post = AsyncMock(return_value=mock_response)
                mock_client_class.return_value.__aenter__.return_value = mock_client
                
                # Test workflow execution
                correlation_id = await workflow_executor.execute_workflow(
                    user_id="test-user",
                    workflow_id="test-workflow-456",
                    workflow_data={"calendar_id": "<EMAIL>"},
                    event_data={"event_type": "created", "event_id": "test-123"}
                )
                
                assert correlation_id == "test-correlation-123"
                mock_client.post.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_google_calendar_api_integration(
        self,
        mock_settings,
        mock_logger,
        mock_google_calendar_service,
        mock_google_credentials
    ):
        """Test integration with Google Calendar API."""
        with patch("src.adapters.google_calendar.get_settings", return_value=mock_settings), \
             patch("src.adapters.google_calendar.get_logger", return_value=mock_logger):
            
            adapter = GoogleCalendarAdapter()
            
            # Mock auth client
            mock_auth_client = AsyncMock()
            mock_auth_client.get_credentials = AsyncMock(return_value={
                "access_token": "test-token",
                "refresh_token": "refresh-token"
            })
            adapter.auth_client = mock_auth_client
            
            # Mock Google Calendar service
            with patch("src.adapters.google_calendar.build", return_value=mock_google_calendar_service), \
                 patch("src.adapters.google_calendar.Credentials", return_value=mock_google_credentials):
                
                # Test health check
                health = await adapter.get_health()
                
                assert health["status"] == "healthy"
                assert health["external_service_status"] == "available"
                mock_auth_client.get_credentials.assert_called()
    
    @pytest.mark.asyncio
    async def test_service_error_handling_integration(self, mock_settings, mock_logger):
        """Test error handling in service integrations."""
        with patch("src.core.auth_client.get_settings", return_value=mock_settings), \
             patch("src.core.auth_client.get_logger", return_value=mock_logger):
            
            auth_client = AuthClient()
            
            # Mock service error
            mock_response = MagicMock()
            mock_response.status_code = 500
            mock_response.text = "Internal server error"
            
            with patch("httpx.AsyncClient") as mock_client_class:
                mock_client = AsyncMock()
                mock_client.get = AsyncMock(return_value=mock_response)
                mock_client_class.return_value.__aenter__.return_value = mock_client
                
                # Test error handling
                credentials = await auth_client.get_credentials("test-user", "google_calendar")
                
                assert credentials is None
                mock_client.get.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_service_timeout_handling_integration(self, mock_settings, mock_logger):
        """Test timeout handling in service integrations."""
        with patch("src.core.workflow_executor.get_settings", return_value=mock_settings), \
             patch("src.core.workflow_executor.get_logger", return_value=mock_logger):
            
            workflow_executor = WorkflowExecutor()
            
            # Mock timeout error
            import httpx
            
            with patch("httpx.AsyncClient") as mock_client_class:
                mock_client = AsyncMock()
                mock_client.post = AsyncMock(side_effect=httpx.TimeoutException("Request timed out"))
                mock_client_class.return_value.__aenter__.return_value = mock_client
                
                # Test timeout handling
                correlation_id = await workflow_executor.execute_workflow(
                    user_id="test-user",
                    workflow_id="test-workflow",
                    workflow_data={},
                    event_data={}
                )
                
                assert correlation_id is None
                mock_client.post.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_service_retry_integration(self, mock_settings, mock_logger):
        """Test retry logic in service integrations."""
        with patch("src.core.auth_client.get_settings", return_value=mock_settings), \
             patch("src.core.auth_client.get_logger", return_value=mock_logger):
            
            auth_client = AuthClient()
            
            # Mock initial failure then success
            mock_responses = [
                MagicMock(status_code=503, text="Service unavailable"),  # First call fails
                MagicMock(status_code=200, json=lambda: {  # Second call succeeds
                    "credentials": {
                        "google_calendar": {
                            "access_token": "retry-success-token"
                        }
                    }
                })
            ]
            
            with patch("httpx.AsyncClient") as mock_client_class:
                mock_client = AsyncMock()
                mock_client.get = AsyncMock(side_effect=mock_responses)
                mock_client_class.return_value.__aenter__.return_value = mock_client
                
                # Test with retry logic (if implemented)
                credentials = await auth_client.get_credentials("test-user", "google_calendar")
                
                # First call should fail, but we don't have retry logic implemented yet
                # This test documents the expected behavior
                assert credentials is None  # Current behavior without retry
    
    @pytest.mark.asyncio
    async def test_dead_letter_queue_integration(self, mock_settings, mock_logger):
        """Test Dead Letter Queue integration with external services."""
        with patch("src.core.dead_letter_queue.get_settings", return_value=mock_settings), \
             patch("src.core.dead_letter_queue.get_logger", return_value=mock_logger):
            
            dlq = DeadLetterQueue()
            
            # Mock workflow executor
            mock_workflow_executor = AsyncMock()
            mock_workflow_executor.execute_workflow = AsyncMock(return_value="retry-correlation-123")
            dlq.workflow_executor = mock_workflow_executor
            
            # Mock database operations
            with patch("src.core.dead_letter_queue.get_async_session") as mock_get_session:
                mock_session = AsyncMock()
                mock_get_session.return_value.__aenter__.return_value = mock_session
                
                # Mock execution and trigger data
                from uuid import uuid4
                execution_id = uuid4()
                
                mock_execution = MagicMock()
                mock_execution.id = execution_id
                mock_execution.is_retryable = True
                mock_execution.retry_count = 3
                mock_execution.event_data = {"event_type": "created"}
                
                mock_trigger = MagicMock()
                mock_trigger.user_id = "test-user"
                mock_trigger.workflow_id = "test-workflow"
                mock_trigger.trigger_config = {"calendar_id": "<EMAIL>"}
                
                mock_result = MagicMock()
                mock_result.first.return_value = (mock_execution, mock_trigger)
                mock_session.execute = AsyncMock(return_value=mock_result)
                mock_session.commit = AsyncMock()
                
                # Test retry execution
                success, result = await dlq.retry_execution(execution_id)
                
                assert success is True
                assert result == "retry-correlation-123"
                mock_workflow_executor.execute_workflow.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_service_circuit_breaker_integration(self, mock_settings, mock_logger):
        """Test circuit breaker pattern in service integrations."""
        # This test documents expected circuit breaker behavior
        # Implementation would depend on adding circuit breaker logic
        
        with patch("src.core.auth_client.get_settings", return_value=mock_settings), \
             patch("src.core.auth_client.get_logger", return_value=mock_logger):
            
            auth_client = AuthClient()
            
            # Simulate multiple failures
            mock_response = MagicMock()
            mock_response.status_code = 500
            
            with patch("httpx.AsyncClient") as mock_client_class:
                mock_client = AsyncMock()
                mock_client.get = AsyncMock(return_value=mock_response)
                mock_client_class.return_value.__aenter__.return_value = mock_client
                
                # Multiple failed calls
                for _ in range(5):
                    credentials = await auth_client.get_credentials("test-user", "google_calendar")
                    assert credentials is None
                
                # Circuit breaker would prevent further calls
                # Current implementation doesn't have circuit breaker
                assert mock_client.get.call_count == 5
    
    @pytest.mark.asyncio
    async def test_service_health_monitoring_integration(
        self,
        mock_settings,
        mock_logger,
        mock_google_calendar_service
    ):
        """Test service health monitoring integration."""
        with patch("src.adapters.google_calendar.get_settings", return_value=mock_settings), \
             patch("src.adapters.google_calendar.get_logger", return_value=mock_logger):
            
            adapter = GoogleCalendarAdapter()
            
            # Mock auth client with no credentials (service unavailable)
            mock_auth_client = AsyncMock()
            mock_auth_client.get_credentials = AsyncMock(return_value=None)
            adapter.auth_client = mock_auth_client
            
            # Test health check with service unavailable
            health = await adapter.get_health()
            
            assert health["status"] == "error"
            assert "No credentials available" in health.get("error", "")
            
            # Test health check with service available
            mock_auth_client.get_credentials = AsyncMock(return_value={
                "access_token": "test-token"
            })
            
            with patch("src.adapters.google_calendar.build", return_value=mock_google_calendar_service):
                health = await adapter.get_health()
                
                assert health["status"] == "healthy"
                assert health["external_service_status"] == "available"
