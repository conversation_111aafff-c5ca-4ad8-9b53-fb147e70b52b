import pytest
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from src.main import app
from src.database.models import Base, Scheduler, SchedulerStatus, ScheduleType
from src.schemas.scheduler import SimplifiedSchedulerCreate, SimplifiedSchedulerUpdate
from src.database.connection import get_db
from datetime import datetime, timedelta, timezone
import os

# Use a separate test database
DATABASE_URL = "sqlite+aiosqlite:///./test_scheduler.db"


@pytest.fixture(scope="module")
def test_app():
    # Override the get_db dependency to use the test database
    async def override_get_db():
        engine = create_async_engine(DATABASE_URL, echo=False)
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        AsyncSessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=engine,
            class_=AsyncSession,
            expire_on_commit=False,
        )
        async with AsyncSessionLocal() as session:
            yield session

        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
        await engine.dispose()

    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as client:
        yield client
    # Clean up the test database file after tests
    if os.path.exists("./test_scheduler.db"):
        os.remove("./test_scheduler.db")


@pytest.fixture(autouse=True)
async def cleanup_db(test_app):
    """Clean up database before each test."""
    yield
    async with get_db() as session:
        await session.execute(Scheduler.__table__.delete())
        await session.commit()


# Mock authentication for integration tests
@pytest.fixture(autouse=True)
def mock_auth_middleware(monkeypatch):
    """Mocks the authentication middleware to allow requests without actual token validation."""

    async def mock_verify_token(request):
        request.state.user_id = "test_user"
        request.state.is_admin = True

    monkeypatch.setattr("src.api.middleware.auth.verify_token", mock_verify_token)


def get_auth_headers(user_id: str = "test_user", is_admin: bool = True):
    """Helper to get mock auth headers for testing."""
    # In a real scenario, this would generate a valid JWT.
    # For testing, we're relying on the mock_auth_middleware.
    return {"X-Test-User-Id": user_id, "X-Test-Is-Admin": str(is_admin).lower()}


@pytest.mark.asyncio
async def test_create_scheduler_daily(test_app):
    response = test_app.post(
        "/api/v1/schedulers/schedulers",
        json={
            "name": "Daily Report Scheduler",
            "workflow_id": "wf_daily_123",
            "frequency": "daily",
            "time": "09:00",
            "timezone": "UTC",
            "is_active": True,
        },
        headers=get_auth_headers(),
    )
    assert response.status_code == 201
    data = response.json()
    assert data["workflow_id"] == "wf_daily_123"
    assert data["frequency"] == "daily"
    assert data["time"] == "09:00"
    assert data["is_active"] == True
    assert "id" in data
    assert "created_at" in data
    assert "updated_at" in data


@pytest.mark.asyncio
async def test_create_scheduler_weekly(test_app):
    response = test_app.post(
        "/api/v1/schedulers/schedulers",
        json={
            "name": "Weekly Report Scheduler",
            "workflow_id": "wf_weekly_456",
            "frequency": "weekly",
            "time": "10:00",
            "days_of_week": ["Monday", "Friday"],
            "timezone": "UTC",
            "is_active": True,
        },
        headers=get_auth_headers(),
    )
    assert response.status_code == 201
    data = response.json()
    assert data["workflow_id"] == "wf_weekly_456"
    assert data["frequency"] == "weekly"
    assert data["time"] == "10:00"
    assert data["days_of_week"] == ["Monday", "Friday"]


@pytest.mark.asyncio
async def test_create_scheduler_custom_cron(test_app):
    response = test_app.post(
        "/api/v1/schedulers/schedulers",
        json={
            "name": "Custom Cron Scheduler",
            "workflow_id": "wf_custom_789",
            "frequency": "custom",
            "cron_expression": "0 9 * * 1-5",
            "timezone": "UTC",
            "is_active": True,
        },
        headers=get_auth_headers(),
    )
    assert response.status_code == 201
    data = response.json()
    assert data["workflow_id"] == "wf_custom_789"
    assert data["frequency"] == "custom"
    assert data["cron_expression"] == "0 9 * * 1-5"


@pytest.mark.asyncio
async def test_create_scheduler_invalid_config(test_app):
    response = test_app.post(
        "/api/v1/schedulers/schedulers",
        json={
            "name": "Invalid Scheduler",
            "workflow_id": "wf_invalid",
            "frequency": "custom",
            "cron_expression": "invalid cron expression",  # Invalid
            "timezone": "UTC",
            "is_active": True,
        },
        headers=get_auth_headers(),
    )
    assert response.status_code == 422  # Validation error


@pytest.mark.asyncio
async def test_get_scheduler_by_id(test_app):
    # First create a scheduler
    create_response = test_app.post(
        "/api/v1/schedulers/schedulers",
        json={
            "name": "Get Test Scheduler",
            "workflow_id": "wf_get_1",
            "frequency": "daily",
            "time": "09:00",
            "timezone": "UTC",
            "is_active": True,
        },
        headers=get_auth_headers(),
    )
    assert create_response.status_code == 201
    scheduler_id = create_response.json()["id"]

    # Then get it
    get_response = test_app.get(
        f"/api/v1/schedulers/schedulers/{scheduler_id}", headers=get_auth_headers()
    )
    assert get_response.status_code == 200
    data = get_response.json()
    assert data["id"] == scheduler_id
    assert data["workflow_id"] == "wf_get_1"


@pytest.mark.asyncio
async def test_get_scheduler_not_found(test_app):
    response = test_app.get(
        "/api/v1/schedulers/schedulers/nonexistent_id", headers=get_auth_headers()
    )
    assert response.status_code == 404
    assert "Scheduler not found" in response.json()["detail"]


@pytest.mark.asyncio
async def test_list_schedulers(test_app):
    # Create a few schedulers
    test_app.post(
        "/api/v1/schedulers/schedulers",
        json={
            "name": "List Test Scheduler 1",
            "workflow_id": "wf_list_1",
            "frequency": "daily",
            "time": "09:00",
            "timezone": "UTC",
            "is_active": True,
        },
        headers=get_auth_headers(),
    )
    test_app.post(
        "/api/v1/schedulers/schedulers",
        json={
            "name": "List Test Scheduler 2",
            "workflow_id": "wf_list_2",
            "frequency": "hourly",
            "timezone": "UTC",
            "is_active": True,
        },
        headers=get_auth_headers(),
    )

    response = test_app.get("/api/v1/schedulers/schedulers", headers=get_auth_headers())
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 2  # May have other schedulers from other tests if cleanup fails


@pytest.mark.asyncio
async def test_list_schedulers_with_filters(test_app):
    # Create specific schedulers for filtering
    test_app.post(
        "/api/v1/schedulers/schedulers",
        json={
            "name": "Filter Test Scheduler 1",
            "workflow_id": "wf_filter_1",
            "frequency": "daily",
            "time": "09:00",
            "timezone": "UTC",
            "is_active": True,
        },
        headers=get_auth_headers(),
    )
    test_app.post(
        "/api/v1/schedulers/schedulers",
        json={
            "name": "Filter Test Scheduler 2",
            "workflow_id": "wf_filter_2",
            "frequency": "hourly",
            "timezone": "UTC",
            "is_active": False,
        },
        headers=get_auth_headers(),
    )
    test_app.post(
        "/api/v1/schedulers/schedulers",
        json={
            "name": "Filter Test Scheduler 3",
            "workflow_id": "wf_filter_1",
            "frequency": "weekly",
            "time": "10:00",
            "days_of_week": ["Monday"],
            "timezone": "UTC",
            "is_active": True,
        },
        headers=get_auth_headers(),
    )

    response = test_app.get(
        "/api/v1/schedulers/schedulers",
        headers=get_auth_headers(),
    )
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 3
    # Filter by workflow_id in the response data
    wf_filter_1_schedulers = [
        s for s in data if s["workflow_id"] == "wf_filter_1" and s["is_active"]
    ]
    assert len(wf_filter_1_schedulers) == 2


@pytest.mark.asyncio
async def test_update_scheduler(test_app):
    # First create a scheduler
    create_response = test_app.post(
        "/api/v1/schedulers/schedulers",
        json={
            "name": "Update Test Scheduler",
            "workflow_id": "wf_update_1",
            "frequency": "daily",
            "time": "09:00",
            "timezone": "UTC",
            "is_active": True,
        },
        headers=get_auth_headers(),
    )
    assert create_response.status_code == 201
    scheduler_id = create_response.json()["id"]

    # Then update it
    update_response = test_app.put(
        f"/api/v1/schedulers/schedulers/{scheduler_id}",
        json={"name": "Updated Scheduler", "is_active": False, "time": "10:00"},
        headers=get_auth_headers(),
    )
    assert update_response.status_code == 200
    data = update_response.json()
    assert data["id"] == scheduler_id
    assert data["name"] == "Updated Scheduler"
    assert data["is_active"] == False
    assert data["time"] == "10:00"

    # Verify persistence
    get_response = test_app.get(
        f"/api/v1/schedulers/schedulers/{scheduler_id}", headers=get_auth_headers()
    )
    assert get_response.status_code == 200
    persisted_data = get_response.json()
    assert persisted_data["name"] == "Updated Scheduler"
    assert persisted_data["is_active"] == False
    assert persisted_data["time"] == "10:00"


@pytest.mark.asyncio
async def test_update_scheduler_not_found(test_app):
    response = test_app.put(
        "/api/v1/schedulers/schedulers/nonexistent_id",
        json={"is_active": False},
        headers=get_auth_headers(),
    )
    assert response.status_code == 404
    assert "Scheduler not found" in response.json()["detail"]


@pytest.mark.asyncio
async def test_delete_scheduler(test_app):
    # First create a scheduler
    create_response = test_app.post(
        "/api/v1/schedulers/schedulers",
        json={
            "name": "Delete Test Scheduler",
            "workflow_id": "wf_delete_1",
            "frequency": "daily",
            "time": "09:00",
            "timezone": "UTC",
            "is_active": True,
        },
        headers=get_auth_headers(),
    )
    assert create_response.status_code == 201
    scheduler_id = create_response.json()["id"]

    # Then delete it
    delete_response = test_app.delete(
        f"/api/v1/schedulers/schedulers/{scheduler_id}", headers=get_auth_headers()
    )
    assert delete_response.status_code == 204  # No Content

    # Verify it's gone
    get_response = test_app.get(
        f"/api/v1/schedulers/schedulers/{scheduler_id}", headers=get_auth_headers()
    )
    assert get_response.status_code == 404


@pytest.mark.asyncio
async def test_delete_scheduler_not_found(test_app):
    response = test_app.delete(
        "/api/v1/schedulers/schedulers/nonexistent_id", headers=get_auth_headers()
    )
    assert response.status_code == 404
    assert "Scheduler not found" in response.json()["detail"]


@pytest.mark.asyncio
async def test_scheduler_api_unauthorized_access(test_app):
    # Test POST without headers
    response = test_app.post(
        "/api/v1/schedulers/schedulers",
        json={
            "name": "Unauthorized Scheduler",
            "workflow_id": "wf_unauth",
            "frequency": "daily",
            "time": "09:00",
            "timezone": "UTC",
            "is_active": True,
        },
    )
    assert response.status_code == 401
    assert "User not authenticated" in response.json()["detail"]

    # Test GET without headers
    response = test_app.get("/api/v1/schedulers/schedulers/some_id")
    assert response.status_code == 401

    # Test PUT without headers
    response = test_app.put(
        "/api/v1/schedulers/schedulers/some_id", json={"is_active": False}
    )
    assert response.status_code == 401

    # Test DELETE without headers
    response = test_app.delete("/api/v1/schedulers/schedulers/some_id")
    assert response.status_code == 401


@pytest.mark.asyncio
async def test_scheduler_api_user_isolation(test_app):
    # This test verifies that users can only access their own schedulers
    # Create a scheduler for user1
    user1_headers = get_auth_headers(user_id="user1")
    create_response = test_app.post(
        "/api/v1/schedulers/schedulers",
        json={
            "name": "User1 Scheduler",
            "workflow_id": "wf_user1",
            "frequency": "daily",
            "time": "09:00",
            "timezone": "UTC",
            "is_active": True,
        },
        headers=user1_headers,
    )
    assert create_response.status_code == 201
    user1_scheduler_id = create_response.json()["id"]

    # Create a scheduler for user2
    user2_headers = get_auth_headers(user_id="user2")
    create_response = test_app.post(
        "/api/v1/schedulers/schedulers",
        json={
            "name": "User2 Scheduler",
            "workflow_id": "wf_user2",
            "frequency": "daily",
            "time": "10:00",
            "timezone": "UTC",
            "is_active": True,
        },
        headers=user2_headers,
    )
    assert create_response.status_code == 201
    user2_scheduler_id = create_response.json()["id"]

    # User1 should only see their own scheduler
    response = test_app.get("/api/v1/schedulers/schedulers", headers=user1_headers)
    assert response.status_code == 200
    user1_schedulers = response.json()
    assert len(user1_schedulers) == 1
    assert user1_schedulers[0]["id"] == user1_scheduler_id

    # User2 should only see their own scheduler
    response = test_app.get("/api/v1/schedulers/schedulers", headers=user2_headers)
    assert response.status_code == 200
    user2_schedulers = response.json()
    assert len(user2_schedulers) == 1
    assert user2_schedulers[0]["id"] == user2_scheduler_id

    # User1 should not be able to access User2's scheduler
    response = test_app.get(
        f"/api/v1/schedulers/schedulers/{user2_scheduler_id}", headers=user1_headers
    )
    assert response.status_code == 404

    # User2 should not be able to access User1's scheduler
    response = test_app.get(
        f"/api/v1/schedulers/schedulers/{user1_scheduler_id}", headers=user2_headers
    )
    assert response.status_code == 404
