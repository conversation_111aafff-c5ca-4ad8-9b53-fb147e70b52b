"""
Integration tests for database operations.

This module tests the integration between the application and the database,
including model relationships, transactions, and data persistence.
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from uuid import uuid4

from src.database.connection import get_async_session, init_database, close_database
from src.database.models import <PERSON><PERSON>, TriggerExecution
from src.core.trigger_manager import Tri<PERSON><PERSON>anager
from src.schemas.trigger import TriggerCreate


class TestDatabaseIntegration:
    """Integration tests for database operations."""
    
    @pytest.fixture(scope="class", autouse=True)
    async def setup_database(self):
        """Set up test database."""
        await init_database()
        yield
        await close_database()
    
    @pytest.mark.asyncio
    async def test_trigger_crud_operations(self):
        """Test complete CRUD operations for triggers."""
        async with get_async_session() as session:
            # Create
            trigger_data = {
                "user_id": "test-user-integration",
                "workflow_id": "test-workflow-integration",
                "trigger_type": "google_calendar",
                "trigger_name": "Integration Test Trigger",
                "trigger_config": {
                    "calendar_id": "<EMAIL>",
                    "event_types": ["created", "updated"]
                },
                "event_types": ["created", "updated"],
                "is_active": True
            }
            
            trigger = Trigger(**trigger_data)
            session.add(trigger)
            await session.commit()
            await session.refresh(trigger)
            
            # Verify creation
            assert trigger.id is not None
            assert trigger.user_id == "test-user-integration"
            assert trigger.created_at is not None
            
            # Read
            from sqlalchemy import select
            result = await session.execute(
                select(Trigger).where(Trigger.id == trigger.id)
            )
            retrieved_trigger = result.scalar_one_or_none()
            
            assert retrieved_trigger is not None
            assert retrieved_trigger.trigger_name == "Integration Test Trigger"
            
            # Update
            retrieved_trigger.trigger_name = "Updated Integration Test Trigger"
            retrieved_trigger.is_active = False
            await session.commit()
            
            # Verify update
            result = await session.execute(
                select(Trigger).where(Trigger.id == trigger.id)
            )
            updated_trigger = result.scalar_one_or_none()
            
            assert updated_trigger.trigger_name == "Updated Integration Test Trigger"
            assert updated_trigger.is_active is False
            
            # Delete
            await session.delete(updated_trigger)
            await session.commit()
            
            # Verify deletion
            result = await session.execute(
                select(Trigger).where(Trigger.id == trigger.id)
            )
            deleted_trigger = result.scalar_one_or_none()
            
            assert deleted_trigger is None
    
    @pytest.mark.asyncio
    async def test_trigger_execution_relationship(self):
        """Test the relationship between triggers and executions."""
        async with get_async_session() as session:
            # Create trigger
            trigger = Trigger(
                user_id="test-user-relationship",
                workflow_id="test-workflow-relationship",
                trigger_type="google_calendar",
                trigger_name="Relationship Test Trigger",
                trigger_config={"calendar_id": "<EMAIL>"},
                event_types=["created"],
                is_active=True
            )
            session.add(trigger)
            await session.commit()
            await session.refresh(trigger)
            
            # Create executions
            executions = []
            for i in range(3):
                execution = TriggerExecution(
                    trigger_id=trigger.id,
                    event_data={
                        "event_type": "created",
                        "event_id": f"test-event-{i}",
                        "summary": f"Test Event {i}"
                    },
                    status="success" if i % 2 == 0 else "failed",
                    executed_at=datetime.utcnow() - timedelta(hours=i),
                    completed_at=datetime.utcnow() - timedelta(hours=i, minutes=30)
                )
                session.add(execution)
                executions.append(execution)
            
            await session.commit()
            
            # Test relationship queries
            from sqlalchemy import select
            from sqlalchemy.orm import selectinload
            
            # Load trigger with executions
            result = await session.execute(
                select(Trigger)
                .options(selectinload(Trigger.executions))
                .where(Trigger.id == trigger.id)
            )
            trigger_with_executions = result.scalar_one()
            
            assert len(trigger_with_executions.executions) == 3
            
            # Test execution back-reference
            result = await session.execute(
                select(TriggerExecution)
                .options(selectinload(TriggerExecution.trigger))
                .where(TriggerExecution.trigger_id == trigger.id)
            )
            executions_with_trigger = result.scalars().all()
            
            assert len(executions_with_trigger) == 3
            for execution in executions_with_trigger:
                assert execution.trigger.id == trigger.id
                assert execution.trigger.trigger_name == "Relationship Test Trigger"
            
            # Cleanup
            for execution in executions:
                await session.delete(execution)
            await session.delete(trigger)
            await session.commit()
    
    @pytest.mark.asyncio
    async def test_transaction_rollback(self):
        """Test transaction rollback on error."""
        async with get_async_session() as session:
            try:
                # Create a trigger
                trigger = Trigger(
                    user_id="test-user-transaction",
                    workflow_id="test-workflow-transaction",
                    trigger_type="google_calendar",
                    trigger_name="Transaction Test Trigger",
                    trigger_config={"calendar_id": "<EMAIL>"},
                    event_types=["created"],
                    is_active=True
                )
                session.add(trigger)
                await session.flush()  # Get the ID without committing
                
                # Create an execution
                execution = TriggerExecution(
                    trigger_id=trigger.id,
                    event_data={"event_type": "created"},
                    status="pending"
                )
                session.add(execution)
                
                # Simulate an error by trying to create invalid data
                invalid_trigger = Trigger(
                    user_id=None,  # This should cause a constraint violation
                    workflow_id="invalid",
                    trigger_type="invalid",
                    trigger_name="Invalid Trigger",
                    trigger_config={},
                    event_types=[],
                    is_active=True
                )
                session.add(invalid_trigger)
                
                # This should fail and rollback the transaction
                await session.commit()
                
            except Exception:
                # Transaction should be rolled back
                await session.rollback()
                
                # Verify that nothing was committed
                from sqlalchemy import select
                result = await session.execute(
                    select(Trigger).where(Trigger.trigger_name == "Transaction Test Trigger")
                )
                rolled_back_trigger = result.scalar_one_or_none()
                
                assert rolled_back_trigger is None
    
    @pytest.mark.asyncio
    async def test_concurrent_operations(self):
        """Test concurrent database operations."""
        async def create_trigger(user_id: str, session_num: int):
            async with get_async_session() as session:
                trigger = Trigger(
                    user_id=user_id,
                    workflow_id=f"concurrent-workflow-{session_num}",
                    trigger_type="google_calendar",
                    trigger_name=f"Concurrent Trigger {session_num}",
                    trigger_config={"calendar_id": f"test{session_num}@concurrent.com"},
                    event_types=["created"],
                    is_active=True
                )
                session.add(trigger)
                await session.commit()
                return trigger.id
        
        # Create multiple triggers concurrently
        user_id = "test-user-concurrent"
        tasks = [create_trigger(user_id, i) for i in range(5)]
        trigger_ids = await asyncio.gather(*tasks)
        
        # Verify all triggers were created
        async with get_async_session() as session:
            from sqlalchemy import select
            result = await session.execute(
                select(Trigger).where(Trigger.user_id == user_id)
            )
            concurrent_triggers = result.scalars().all()
            
            assert len(concurrent_triggers) == 5
            assert all(trigger.id in trigger_ids for trigger in concurrent_triggers)
            
            # Cleanup
            for trigger in concurrent_triggers:
                await session.delete(trigger)
            await session.commit()
    
    @pytest.mark.asyncio
    async def test_query_performance(self):
        """Test query performance with larger datasets."""
        async with get_async_session() as session:
            # Create multiple triggers
            triggers = []
            for i in range(50):
                trigger = Trigger(
                    user_id=f"perf-user-{i % 5}",  # 5 different users
                    workflow_id=f"perf-workflow-{i}",
                    trigger_type="google_calendar" if i % 2 == 0 else "slack",
                    trigger_name=f"Performance Test Trigger {i}",
                    trigger_config={"test": f"config-{i}"},
                    event_types=["created"],
                    is_active=i % 3 != 0  # Mix of active/inactive
                )
                session.add(trigger)
                triggers.append(trigger)
            
            await session.commit()
            
            # Test various query patterns
            from sqlalchemy import select, func
            
            # Count queries
            start_time = datetime.utcnow()
            result = await session.execute(
                select(func.count(Trigger.id)).where(Trigger.is_active == True)
            )
            active_count = result.scalar()
            query_time = (datetime.utcnow() - start_time).total_seconds()
            
            assert active_count > 0
            assert query_time < 1.0  # Should be fast
            
            # Filtered queries
            start_time = datetime.utcnow()
            result = await session.execute(
                select(Trigger)
                .where(Trigger.trigger_type == "google_calendar")
                .where(Trigger.is_active == True)
                .limit(10)
            )
            filtered_triggers = result.scalars().all()
            query_time = (datetime.utcnow() - start_time).total_seconds()
            
            assert len(filtered_triggers) <= 10
            assert query_time < 1.0  # Should be fast
            
            # Cleanup
            for trigger in triggers:
                await session.delete(trigger)
            await session.commit()
    
    @pytest.mark.asyncio
    async def test_data_integrity_constraints(self):
        """Test database constraints and data integrity."""
        async with get_async_session() as session:
            # Test foreign key constraint
            with pytest.raises(Exception):  # Should raise foreign key violation
                execution = TriggerExecution(
                    trigger_id=uuid4(),  # Non-existent trigger ID
                    event_data={"event_type": "created"},
                    status="pending"
                )
                session.add(execution)
                await session.commit()
            
            await session.rollback()
            
            # Test unique constraints (if any)
            trigger1 = Trigger(
                user_id="constraint-user",
                workflow_id="constraint-workflow",
                trigger_type="google_calendar",
                trigger_name="Constraint Test Trigger",
                trigger_config={"calendar_id": "<EMAIL>"},
                event_types=["created"],
                is_active=True
            )
            session.add(trigger1)
            await session.commit()
            
            # Cleanup
            await session.delete(trigger1)
            await session.commit()
