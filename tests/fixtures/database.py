"""
Database fixtures for testing the Trigger Service.

This module provides database-related fixtures for unit and integration tests,
including test database setup, model factories, and data fixtures.
"""

import asyncio
import uuid
from datetime import datetime, timedelta
from typing import Any, AsyncGenerator, Dict, List, Optional
from unittest.mock import AsyncMock

import pytest
import pytest_asyncio
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.pool import StaticPool

from src.database.connection import get_async_session
from src.database.models import Base, Trigger, TriggerExecution
from src.utils.config import get_settings


@pytest_asyncio.fixture(scope="session")
async def test_engine():
    """Create a test database engine."""
    # Use in-memory SQLite for fast testing
    engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        poolclass=StaticPool,
        connect_args={"check_same_thread": False},
        echo=False,
    )
    
    # Create all tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    await engine.dispose()


@pytest_asyncio.fixture
async def test_session(test_engine) -> AsyncGenerator[AsyncSession, None]:
    """Create a test database session."""
    async_session = async_sessionmaker(
        test_engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session() as session:
        yield session
        await session.rollback()


@pytest.fixture
def mock_get_async_session(test_session):
    """Mock the get_async_session dependency."""
    async def _mock_session():
        yield test_session
    
    return _mock_session


@pytest.fixture
def sample_trigger_data() -> Dict[str, Any]:
    """Sample trigger data for testing."""
    return {
        "user_id": "test-user-123",
        "workflow_id": "test-workflow-456",
        "trigger_type": "google_calendar",
        "trigger_name": "Test Calendar Trigger",
        "trigger_config": {
            "calendar_id": "<EMAIL>",
            "event_types": ["created", "updated"],
            "filters": {
                "summary_contains": "meeting"
            }
        },
        "event_types": ["created", "updated"],
        "is_active": True
    }


@pytest.fixture
def sample_execution_data() -> Dict[str, Any]:
    """Sample execution data for testing."""
    return {
        "event_data": {
            "event_type": "created",
            "event_id": "test-event-123",
            "summary": "Test Meeting",
            "start_time": "2024-01-15T10:00:00Z",
            "end_time": "2024-01-15T11:00:00Z",
            "attendees": ["<EMAIL>", "<EMAIL>"]
        },
        "status": "pending",
        "retry_count": 0
    }


@pytest_asyncio.fixture
async def sample_trigger(test_session: AsyncSession, sample_trigger_data: Dict[str, Any]) -> Trigger:
    """Create a sample trigger in the test database."""
    trigger = Trigger(**sample_trigger_data)
    test_session.add(trigger)
    await test_session.commit()
    await test_session.refresh(trigger)
    return trigger


@pytest_asyncio.fixture
async def sample_execution(
    test_session: AsyncSession,
    sample_trigger: Trigger,
    sample_execution_data: Dict[str, Any]
) -> TriggerExecution:
    """Create a sample execution in the test database."""
    execution_data = sample_execution_data.copy()
    execution_data["trigger_id"] = sample_trigger.id
    
    execution = TriggerExecution(**execution_data)
    test_session.add(execution)
    await test_session.commit()
    await test_session.refresh(execution)
    return execution


@pytest_asyncio.fixture
async def multiple_triggers(
    test_session: AsyncSession,
    sample_trigger_data: Dict[str, Any]
) -> List[Trigger]:
    """Create multiple triggers for testing."""
    triggers = []
    
    for i in range(3):
        trigger_data = sample_trigger_data.copy()
        trigger_data["trigger_name"] = f"Test Trigger {i+1}"
        trigger_data["workflow_id"] = f"workflow-{i+1}"
        
        trigger = Trigger(**trigger_data)
        test_session.add(trigger)
        triggers.append(trigger)
    
    await test_session.commit()
    
    for trigger in triggers:
        await test_session.refresh(trigger)
    
    return triggers


@pytest_asyncio.fixture
async def failed_executions(
    test_session: AsyncSession,
    sample_trigger: Trigger
) -> List[TriggerExecution]:
    """Create multiple failed executions for testing."""
    executions = []
    
    for i in range(5):
        execution = TriggerExecution(
            trigger_id=sample_trigger.id,
            event_data={
                "event_type": "created",
                "event_id": f"failed-event-{i+1}",
                "summary": f"Failed Event {i+1}"
            },
            status="failed",
            error_message=f"Test error {i+1}",
            retry_count=5,
            executed_at=datetime.utcnow() - timedelta(hours=i),
            completed_at=datetime.utcnow() - timedelta(hours=i, minutes=30)
        )
        test_session.add(execution)
        executions.append(execution)
    
    await test_session.commit()
    
    for execution in executions:
        await test_session.refresh(execution)
    
    return executions


class MockAsyncSession:
    """Mock async session for testing without database."""
    
    def __init__(self):
        self.add = AsyncMock()
        self.commit = AsyncMock()
        self.rollback = AsyncMock()
        self.refresh = AsyncMock()
        self.execute = AsyncMock()
        self.close = AsyncMock()
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()


@pytest.fixture
def mock_async_session():
    """Create a mock async session."""
    return MockAsyncSession()


@pytest.fixture
def mock_database_error():
    """Mock database error for testing error handling."""
    from sqlalchemy.exc import SQLAlchemyError
    return SQLAlchemyError("Mock database error")


# Event loop fixture for async tests
@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()
