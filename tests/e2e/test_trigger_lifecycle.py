"""
End-to-end tests for trigger lifecycle.

This module tests the complete trigger lifecycle from creation
to execution, including webhook processing and workflow execution.
"""

import pytest
import asyncio
from datetime import datetime
from uuid import uuid4
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, MagicMock, patch

from src.main import app
from src.database.connection import init_database, close_database
from tests.fixtures.http import auth_headers


class TestTriggerLifecycleE2E:
    """End-to-end tests for trigger lifecycle."""
    
    @pytest.fixture(scope="class", autouse=True)
    async def setup_database(self):
        """Set up test database."""
        await init_database()
        yield
        await close_database()
    
    @pytest.fixture
    def client(self):
        """Create a test client."""
        return TestClient(app)
    
    @pytest.fixture
    def mock_external_services(self):
        """Mock all external services for E2E tests."""
        mocks = {}
        
        # Mock Google Calendar API
        mock_calendar_service = MagicMock()
        mock_calendar_service.events().watch().execute.return_value = {
            "kind": "api#channel",
            "id": "e2e-test-channel",
            "resourceId": "e2e-test-resource",
            "expiration": "1640995200000"
        }
        mocks["google_calendar_service"] = mock_calendar_service
        
        # Mock Auth Service
        mock_auth_response = MagicMock()
        mock_auth_response.status_code = 200
        mock_auth_response.json.return_value = {
            "credentials": {
                "google_calendar": {
                    "access_token": "e2e-test-token",
                    "refresh_token": "e2e-refresh-token",
                    "expires_at": "2024-12-31T23:59:59Z"
                }
            }
        }
        mocks["auth_response"] = mock_auth_response
        
        # Mock Workflow Service
        mock_workflow_response = MagicMock()
        mock_workflow_response.status_code = 200
        mock_workflow_response.json.return_value = {
            "correlationId": "e2e-correlation-123",
            "status": "initiated",
            "workflowId": "e2e-workflow-456"
        }
        mocks["workflow_response"] = mock_workflow_response
        
        return mocks
    
    def test_complete_trigger_lifecycle_e2e(self, client, auth_headers, mock_external_services):
        """Test complete trigger lifecycle from creation to execution."""
        
        with patch("src.adapters.google_calendar.build", return_value=mock_external_services["google_calendar_service"]), \
             patch("httpx.AsyncClient") as mock_http_client:
            
            # Setup HTTP client mocks
            mock_client = AsyncMock()
            mock_client.get = AsyncMock(return_value=mock_external_services["auth_response"])
            mock_client.post = AsyncMock(return_value=mock_external_services["workflow_response"])
            mock_http_client.return_value.__aenter__.return_value = mock_client
            
            # Step 1: Create a trigger
            trigger_data = {
                "user_id": "e2e-test-user",
                "workflow_id": "e2e-test-workflow",
                "trigger_type": "google_calendar",
                "trigger_name": "E2E Test Trigger",
                "trigger_config": {
                    "calendar_id": "<EMAIL>",
                    "event_types": ["created", "updated"],
                    "filters": {
                        "summary_contains": "meeting"
                    }
                },
                "event_types": ["created", "updated"],
                "is_active": True
            }
            
            create_response = client.post(
                "/api/v1/triggers",
                json=trigger_data,
                headers=auth_headers
            )
            
            assert create_response.status_code == 201
            created_trigger = create_response.json()
            trigger_id = created_trigger["id"]
            
            # Step 2: Verify trigger is active and properly configured
            get_response = client.get(
                f"/api/v1/triggers/{trigger_id}",
                headers=auth_headers
            )
            
            assert get_response.status_code == 200
            trigger_details = get_response.json()
            assert trigger_details["is_active"] is True
            assert trigger_details["trigger_type"] == "google_calendar"
            
            # Step 3: Simulate webhook event
            webhook_payload = {
                "kind": "api#channel",
                "id": "e2e-test-channel",
                "resourceId": "e2e-test-resource",
                "resourceUri": f"https://www.googleapis.com/calendar/v3/calendars/{trigger_data['trigger_config']['calendar_id']}/events",
                "token": "e2e-test-token",
                "expiration": "1640995200000",
                "event_data": {
                    "kind": "calendar#event",
                    "id": "e2e-test-event-123",
                    "status": "confirmed",
                    "summary": "Important meeting",  # Matches filter
                    "start": {
                        "dateTime": "2024-01-15T10:00:00-08:00",
                        "timeZone": "America/Los_Angeles"
                    },
                    "end": {
                        "dateTime": "2024-01-15T11:00:00-08:00",
                        "timeZone": "America/Los_Angeles"
                    }
                }
            }
            
            webhook_headers = {
                "X-Goog-Channel-ID": "e2e-test-channel",
                "X-Goog-Channel-Token": "e2e-test-token",
                "X-Goog-Resource-ID": "e2e-test-resource",
                "X-Goog-Resource-State": "exists"
            }
            
            webhook_response = client.post(
                "/api/v1/webhooks/google-calendar",
                json=webhook_payload,
                headers=webhook_headers
            )
            
            # Should process successfully
            assert webhook_response.status_code == 200
            webhook_result = webhook_response.json()
            assert webhook_result["status"] == "processed"
            
            # Step 4: Verify workflow was executed
            # Check that the workflow service was called
            mock_client.post.assert_called()
            workflow_call = mock_client.post.call_args
            assert "e2e-test-workflow" in str(workflow_call)
            
            # Step 5: Check execution history
            executions_response = client.get(
                f"/api/v1/triggers/{trigger_id}/executions",
                headers=auth_headers
            )
            
            assert executions_response.status_code == 200
            executions = executions_response.json()
            assert len(executions) >= 1
            
            # Verify execution details
            latest_execution = executions[0]
            assert latest_execution["status"] in ["success", "pending"]
            assert "e2e-test-event-123" in str(latest_execution["event_data"])
            
            # Step 6: Test trigger toggle
            toggle_response = client.post(
                f"/api/v1/triggers/{trigger_id}/toggle",
                json={"is_active": False},
                headers=auth_headers
            )
            
            assert toggle_response.status_code == 200
            toggled_trigger = toggle_response.json()
            assert toggled_trigger["is_active"] is False
            
            # Step 7: Verify inactive trigger doesn't process events
            webhook_response_inactive = client.post(
                "/api/v1/webhooks/google-calendar",
                json=webhook_payload,
                headers=webhook_headers
            )
            
            # Should still return 200 but not process
            assert webhook_response_inactive.status_code in [200, 500]
            
            # Step 8: Clean up - delete trigger
            delete_response = client.delete(
                f"/api/v1/triggers/{trigger_id}",
                headers=auth_headers
            )
            
            assert delete_response.status_code == 204
            
            # Verify deletion
            get_deleted_response = client.get(
                f"/api/v1/triggers/{trigger_id}",
                headers=auth_headers
            )
            
            assert get_deleted_response.status_code == 404
    
    def test_error_recovery_e2e(self, client, auth_headers, mock_external_services):
        """Test error recovery in end-to-end flow."""
        
        with patch("src.adapters.google_calendar.build", return_value=mock_external_services["google_calendar_service"]), \
             patch("httpx.AsyncClient") as mock_http_client:
            
            # Setup HTTP client with initial failure then success
            mock_client = AsyncMock()
            mock_client.get = AsyncMock(return_value=mock_external_services["auth_response"])
            
            # First workflow call fails, second succeeds
            workflow_responses = [
                MagicMock(status_code=500, text="Service unavailable"),
                mock_external_services["workflow_response"]
            ]
            mock_client.post = AsyncMock(side_effect=workflow_responses)
            mock_http_client.return_value.__aenter__.return_value = mock_client
            
            # Create trigger
            trigger_data = {
                "user_id": "e2e-error-user",
                "workflow_id": "e2e-error-workflow",
                "trigger_type": "google_calendar",
                "trigger_name": "E2E Error Recovery Trigger",
                "trigger_config": {
                    "calendar_id": "<EMAIL>",
                    "event_types": ["created"]
                },
                "event_types": ["created"],
                "is_active": True
            }
            
            create_response = client.post(
                "/api/v1/triggers",
                json=trigger_data,
                headers=auth_headers
            )
            
            assert create_response.status_code == 201
            trigger_id = create_response.json()["id"]
            
            # Send webhook that will cause workflow failure
            webhook_payload = {
                "kind": "api#channel",
                "id": "error-test-channel",
                "resourceId": "error-test-resource",
                "event_data": {
                    "id": "error-test-event",
                    "summary": "Error Test Event",
                    "start": {"dateTime": "2024-01-15T10:00:00Z"}
                }
            }
            
            webhook_headers = {
                "X-Goog-Channel-ID": "error-test-channel",
                "X-Goog-Channel-Token": "error-test-token",
                "X-Goog-Resource-ID": "error-test-resource",
                "X-Goog-Resource-State": "exists"
            }
            
            # First webhook should fail
            webhook_response = client.post(
                "/api/v1/webhooks/google-calendar",
                json=webhook_payload,
                headers=webhook_headers
            )
            
            # Should handle error gracefully
            assert webhook_response.status_code in [200, 500]
            
            # Check that execution was recorded as failed
            executions_response = client.get(
                f"/api/v1/triggers/{trigger_id}/executions",
                headers=auth_headers
            )
            
            if executions_response.status_code == 200:
                executions = executions_response.json()
                if executions:
                    # Should have a failed execution
                    assert any(exec["status"] == "failed" for exec in executions)
            
            # Clean up
            client.delete(f"/api/v1/triggers/{trigger_id}", headers=auth_headers)
    
    def test_multiple_triggers_e2e(self, client, auth_headers, mock_external_services):
        """Test multiple triggers processing events simultaneously."""
        
        with patch("src.adapters.google_calendar.build", return_value=mock_external_services["google_calendar_service"]), \
             patch("httpx.AsyncClient") as mock_http_client:
            
            mock_client = AsyncMock()
            mock_client.get = AsyncMock(return_value=mock_external_services["auth_response"])
            mock_client.post = AsyncMock(return_value=mock_external_services["workflow_response"])
            mock_http_client.return_value.__aenter__.return_value = mock_client
            
            # Create multiple triggers
            trigger_ids = []
            for i in range(3):
                trigger_data = {
                    "user_id": f"e2e-multi-user-{i}",
                    "workflow_id": f"e2e-multi-workflow-{i}",
                    "trigger_type": "google_calendar",
                    "trigger_name": f"E2E Multi Trigger {i}",
                    "trigger_config": {
                        "calendar_id": f"multi-test-{i}@example.com",
                        "event_types": ["created"]
                    },
                    "event_types": ["created"],
                    "is_active": True
                }
                
                response = client.post(
                    "/api/v1/triggers",
                    json=trigger_data,
                    headers=auth_headers
                )
                
                assert response.status_code == 201
                trigger_ids.append(response.json()["id"])
            
            # Send webhook events for each trigger
            for i, trigger_id in enumerate(trigger_ids):
                webhook_payload = {
                    "kind": "api#channel",
                    "id": f"multi-test-channel-{i}",
                    "resourceId": f"multi-test-resource-{i}",
                    "event_data": {
                        "id": f"multi-test-event-{i}",
                        "summary": f"Multi Test Event {i}",
                        "start": {"dateTime": "2024-01-15T10:00:00Z"}
                    }
                }
                
                webhook_headers = {
                    "X-Goog-Channel-ID": f"multi-test-channel-{i}",
                    "X-Goog-Channel-Token": f"multi-test-token-{i}",
                    "X-Goog-Resource-ID": f"multi-test-resource-{i}",
                    "X-Goog-Resource-State": "exists"
                }
                
                response = client.post(
                    "/api/v1/webhooks/google-calendar",
                    json=webhook_payload,
                    headers=webhook_headers
                )
                
                assert response.status_code in [200, 500]
            
            # Verify all triggers are still active
            for trigger_id in trigger_ids:
                response = client.get(
                    f"/api/v1/triggers/{trigger_id}",
                    headers=auth_headers
                )
                assert response.status_code == 200
                assert response.json()["is_active"] is True
            
            # Clean up
            for trigger_id in trigger_ids:
                client.delete(f"/api/v1/triggers/{trigger_id}", headers=auth_headers)
    
    def test_health_monitoring_e2e(self, client, auth_headers):
        """Test health monitoring throughout the system."""
        # Test basic health
        health_response = client.get("/api/v1/health")
        assert health_response.status_code == 200
        assert health_response.json()["status"] == "healthy"
        
        # Test detailed health
        detailed_response = client.get(
            "/api/v1/health/detailed",
            headers=auth_headers
        )
        assert detailed_response.status_code == 200
        detailed_health = detailed_response.json()
        assert "dependencies" in detailed_health
        
        # Test adapter health
        adapter_response = client.get(
            "/api/v1/health/adapters",
            headers=auth_headers
        )
        assert adapter_response.status_code == 200
        
        # Test metrics
        metrics_response = client.get(
            "/api/v1/health/metrics",
            headers=auth_headers
        )
        assert metrics_response.status_code == 200
        metrics = metrics_response.json()
        assert "user_metrics" in metrics
        assert "system_metrics" in metrics
