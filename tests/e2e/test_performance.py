"""
Performance tests for the trigger service.

This module tests the performance characteristics of the trigger service,
including response times, throughput, and resource usage.
"""

import pytest
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock, AsyncMock

from src.main import app
from tests.fixtures.http import auth_headers


class TestPerformance:
    """Performance tests for the trigger service."""
    
    @pytest.fixture
    def client(self):
        """Create a test client."""
        return TestClient(app)
    
    @pytest.fixture
    def mock_external_services(self):
        """Mock external services for performance tests."""
        mocks = {}
        
        # Fast mock responses
        mock_auth_response = MagicMock()
        mock_auth_response.status_code = 200
        mock_auth_response.json.return_value = {
            "credentials": {"google_calendar": {"access_token": "perf-token"}}
        }
        
        mock_workflow_response = MagicMock()
        mock_workflow_response.status_code = 200
        mock_workflow_response.json.return_value = {
            "correlationId": "perf-correlation",
            "status": "initiated"
        }
        
        mocks["auth_response"] = mock_auth_response
        mocks["workflow_response"] = mock_workflow_response
        
        return mocks
    
    def test_health_endpoint_response_time(self, client):
        """Test health endpoint response time."""
        response_times = []
        
        for _ in range(10):
            start_time = time.time()
            response = client.get("/api/v1/health")
            end_time = time.time()
            
            assert response.status_code == 200
            response_times.append(end_time - start_time)
        
        # Health endpoint should respond quickly
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)
        
        assert avg_response_time < 0.1  # Average under 100ms
        assert max_response_time < 0.5   # Max under 500ms
        
        print(f"Health endpoint - Avg: {avg_response_time:.3f}s, Max: {max_response_time:.3f}s")
    
    def test_trigger_creation_performance(self, client, auth_headers, mock_external_services):
        """Test trigger creation performance."""
        
        with patch("httpx.AsyncClient") as mock_http_client:
            mock_client = AsyncMock()
            mock_client.get = AsyncMock(return_value=mock_external_services["auth_response"])
            mock_http_client.return_value.__aenter__.return_value = mock_client
            
            response_times = []
            created_triggers = []
            
            for i in range(5):  # Create 5 triggers
                trigger_data = {
                    "user_id": f"perf-user-{i}",
                    "workflow_id": f"perf-workflow-{i}",
                    "trigger_type": "google_calendar",
                    "trigger_name": f"Performance Test Trigger {i}",
                    "trigger_config": {
                        "calendar_id": f"perf-test-{i}@example.com",
                        "event_types": ["created"]
                    },
                    "event_types": ["created"],
                    "is_active": True
                }
                
                start_time = time.time()
                response = client.post(
                    "/api/v1/triggers",
                    json=trigger_data,
                    headers=auth_headers
                )
                end_time = time.time()
                
                if response.status_code == 201:
                    created_triggers.append(response.json()["id"])
                    response_times.append(end_time - start_time)
            
            if response_times:
                avg_response_time = sum(response_times) / len(response_times)
                max_response_time = max(response_times)
                
                # Trigger creation should be reasonably fast
                assert avg_response_time < 2.0  # Average under 2 seconds
                assert max_response_time < 5.0  # Max under 5 seconds
                
                print(f"Trigger creation - Avg: {avg_response_time:.3f}s, Max: {max_response_time:.3f}s")
            
            # Cleanup
            for trigger_id in created_triggers:
                client.delete(f"/api/v1/triggers/{trigger_id}", headers=auth_headers)
    
    def test_webhook_processing_performance(self, client, mock_external_services):
        """Test webhook processing performance."""
        
        with patch("httpx.AsyncClient") as mock_http_client:
            mock_client = AsyncMock()
            mock_client.post = AsyncMock(return_value=mock_external_services["workflow_response"])
            mock_http_client.return_value.__aenter__.return_value = mock_client
            
            response_times = []
            
            for i in range(10):  # Process 10 webhooks
                webhook_payload = {
                    "kind": "api#channel",
                    "id": f"perf-channel-{i}",
                    "resourceId": f"perf-resource-{i}",
                    "event_data": {
                        "id": f"perf-event-{i}",
                        "summary": f"Performance Test Event {i}",
                        "start": {"dateTime": "2024-01-15T10:00:00Z"}
                    }
                }
                
                webhook_headers = {
                    "X-Goog-Channel-ID": f"perf-channel-{i}",
                    "X-Goog-Channel-Token": f"perf-token-{i}",
                    "X-Goog-Resource-ID": f"perf-resource-{i}",
                    "X-Goog-Resource-State": "exists"
                }
                
                start_time = time.time()
                response = client.post(
                    "/api/v1/webhooks/google-calendar",
                    json=webhook_payload,
                    headers=webhook_headers
                )
                end_time = time.time()
                
                # Accept both success and "no triggers" responses
                assert response.status_code in [200, 500]
                response_times.append(end_time - start_time)
            
            avg_response_time = sum(response_times) / len(response_times)
            max_response_time = max(response_times)
            
            # Webhook processing should be fast
            assert avg_response_time < 1.0  # Average under 1 second
            assert max_response_time < 3.0  # Max under 3 seconds
            
            print(f"Webhook processing - Avg: {avg_response_time:.3f}s, Max: {max_response_time:.3f}s")
    
    def test_concurrent_requests_performance(self, client, auth_headers):
        """Test performance under concurrent load."""
        
        def make_health_request():
            """Make a health check request."""
            start_time = time.time()
            response = client.get("/api/v1/health")
            end_time = time.time()
            return response.status_code, end_time - start_time
        
        def make_trigger_list_request():
            """Make a trigger list request."""
            start_time = time.time()
            response = client.get("/api/v1/triggers", headers=auth_headers)
            end_time = time.time()
            return response.status_code, end_time - start_time
        
        # Test concurrent health checks
        with ThreadPoolExecutor(max_workers=10) as executor:
            health_futures = [executor.submit(make_health_request) for _ in range(20)]
            trigger_futures = [executor.submit(make_trigger_list_request) for _ in range(10)]
            
            # Collect results
            health_results = [future.result() for future in health_futures]
            trigger_results = [future.result() for future in trigger_futures]
        
        # Verify all requests succeeded
        health_success_count = sum(1 for status, _ in health_results if status == 200)
        trigger_success_count = sum(1 for status, _ in trigger_results if status == 200)
        
        assert health_success_count >= 18  # At least 90% success rate
        assert trigger_success_count >= 8   # At least 80% success rate
        
        # Check response times under load
        health_times = [time for _, time in health_results if _ == 200]
        trigger_times = [time for _, time in trigger_results if _ == 200]
        
        if health_times:
            avg_health_time = sum(health_times) / len(health_times)
            assert avg_health_time < 0.5  # Should still be fast under load
            print(f"Concurrent health checks - Avg: {avg_health_time:.3f}s")
        
        if trigger_times:
            avg_trigger_time = sum(trigger_times) / len(trigger_times)
            assert avg_trigger_time < 2.0  # Should be reasonable under load
            print(f"Concurrent trigger lists - Avg: {avg_trigger_time:.3f}s")
    
    def test_memory_usage_stability(self, client, auth_headers):
        """Test memory usage stability over multiple requests."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Make many requests
        for i in range(50):
            # Mix of different endpoints
            client.get("/api/v1/health")
            client.get("/api/v1/triggers", headers=auth_headers)
            
            if i % 10 == 0:  # Check memory every 10 requests
                current_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_increase = current_memory - initial_memory
                
                # Memory shouldn't grow excessively
                assert memory_increase < 100  # Less than 100MB increase
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        total_increase = final_memory - initial_memory
        
        print(f"Memory usage - Initial: {initial_memory:.1f}MB, Final: {final_memory:.1f}MB, Increase: {total_increase:.1f}MB")
        
        # Total memory increase should be reasonable
        assert total_increase < 50  # Less than 50MB total increase
    
    def test_database_query_performance(self, client, auth_headers, mock_external_services):
        """Test database query performance."""
        
        with patch("httpx.AsyncClient") as mock_http_client:
            mock_client = AsyncMock()
            mock_client.get = AsyncMock(return_value=mock_external_services["auth_response"])
            mock_http_client.return_value.__aenter__.return_value = mock_client
            
            # Create some test data first
            created_triggers = []
            for i in range(10):
                trigger_data = {
                    "user_id": f"db-perf-user-{i % 3}",  # 3 different users
                    "workflow_id": f"db-perf-workflow-{i}",
                    "trigger_type": "google_calendar",
                    "trigger_name": f"DB Performance Test Trigger {i}",
                    "trigger_config": {"calendar_id": f"db-perf-{i}@example.com"},
                    "event_types": ["created"],
                    "is_active": i % 2 == 0  # Mix of active/inactive
                }
                
                response = client.post(
                    "/api/v1/triggers",
                    json=trigger_data,
                    headers=auth_headers
                )
                
                if response.status_code == 201:
                    created_triggers.append(response.json()["id"])
            
            # Test query performance
            query_times = []
            
            for _ in range(5):
                start_time = time.time()
                response = client.get(
                    "/api/v1/triggers",
                    headers=auth_headers,
                    params={"user_id": "db-perf-user-0", "page_size": 50}
                )
                end_time = time.time()
                
                assert response.status_code == 200
                query_times.append(end_time - start_time)
            
            avg_query_time = sum(query_times) / len(query_times)
            max_query_time = max(query_times)
            
            # Database queries should be fast
            assert avg_query_time < 0.5  # Average under 500ms
            assert max_query_time < 1.0  # Max under 1 second
            
            print(f"Database queries - Avg: {avg_query_time:.3f}s, Max: {max_query_time:.3f}s")
            
            # Cleanup
            for trigger_id in created_triggers:
                client.delete(f"/api/v1/triggers/{trigger_id}", headers=auth_headers)
    
    def test_api_throughput(self, client):
        """Test API throughput (requests per second)."""
        
        def make_request():
            return client.get("/api/v1/health")
        
        # Measure throughput over 10 seconds
        start_time = time.time()
        request_count = 0
        
        while time.time() - start_time < 5:  # Run for 5 seconds
            response = make_request()
            if response.status_code == 200:
                request_count += 1
        
        duration = time.time() - start_time
        throughput = request_count / duration
        
        print(f"API Throughput: {throughput:.1f} requests/second")
        
        # Should handle at least 10 requests per second
        assert throughput >= 10
    
    @pytest.mark.asyncio
    async def test_async_performance(self):
        """Test async operation performance."""
        from httpx import AsyncClient
        
        async def make_async_request(client):
            start_time = time.time()
            response = await client.get("http://test/api/v1/health")
            end_time = time.time()
            return response.status_code, end_time - start_time
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Test concurrent async requests
            tasks = [make_async_request(client) for _ in range(20)]
            results = await asyncio.gather(*tasks)
        
        # Verify results
        success_count = sum(1 for status, _ in results if status == 200)
        response_times = [time for status, time in results if status == 200]
        
        assert success_count >= 18  # At least 90% success rate
        
        if response_times:
            avg_time = sum(response_times) / len(response_times)
            max_time = max(response_times)
            
            print(f"Async requests - Avg: {avg_time:.3f}s, Max: {max_time:.3f}s")
            
            # Async requests should be fast
            assert avg_time < 0.2  # Average under 200ms
            assert max_time < 1.0  # Max under 1 second
