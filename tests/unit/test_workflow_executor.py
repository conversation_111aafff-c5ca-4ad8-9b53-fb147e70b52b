"""
Unit tests for the WorkflowExecutor class.

This module tests the workflow execution functionality,
including request formatting, HTTP client operations, and error handling.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any

from src.core.workflow_executor import WorkflowExecutor
from tests.fixtures.http import (
    mock_successful_response,
    mock_error_response,
    mock_timeout_response,
    workflow_execution_request,
    workflow_execution_response
)
from tests.fixtures.mocks import mock_settings, mock_logger


class TestWorkflowExecutor:
    """Test cases for WorkflowExecutor class."""
    
    @pytest.fixture
    def workflow_executor(self, mock_settings, mock_logger):
        """Create a WorkflowExecutor instance for testing."""
        with patch("src.core.workflow_executor.get_settings", return_value=mock_settings), \
             patch("src.core.workflow_executor.get_logger", return_value=mock_logger):
            return WorkflowExecutor()
    
    @pytest.fixture
    def sample_event_data(self) -> Dict[str, Any]:
        """Sample event data for testing."""
        return {
            "event_type": "created",
            "event_id": "test-event-123",
            "summary": "Test Meeting",
            "start_time": "2024-01-15T10:00:00Z",
            "end_time": "2024-01-15T11:00:00Z",
            "attendees": ["<EMAIL>", "<EMAIL>"]
        }
    
    @pytest.fixture
    def sample_workflow_data(self) -> Dict[str, Any]:
        """Sample workflow data for testing."""
        return {
            "calendar_id": "<EMAIL>",
            "event_types": ["created", "updated"],
            "filters": {
                "summary_contains": "meeting"
            }
        }
    
    @pytest.mark.asyncio
    async def test_execute_workflow_success(
        self,
        workflow_executor,
        sample_event_data,
        sample_workflow_data,
        mock_successful_response
    ):
        """Test successful workflow execution."""
        # Setup
        user_id = "test-user-123"
        workflow_id = "test-workflow-456"
        
        # Mock HTTP client
        mock_client = AsyncMock()
        mock_client.post = AsyncMock(return_value=mock_successful_response)
        workflow_executor.client = mock_client
        
        # Execute
        result = await workflow_executor.execute_workflow(
            user_id=user_id,
            workflow_id=workflow_id,
            workflow_data=sample_workflow_data,
            event_data=sample_event_data
        )
        
        # Assertions
        assert result == "test-correlation-123"
        mock_client.post.assert_called_once()
        
        # Verify the request payload
        call_args = mock_client.post.call_args
        assert call_args[1]["json"]["user_id"] == user_id
        assert call_args[1]["json"]["workflow_id"] == workflow_id
        assert call_args[1]["json"]["approval"] is True
        assert "payload" in call_args[1]["json"]
    
    @pytest.mark.asyncio
    async def test_execute_workflow_http_error(
        self,
        workflow_executor,
        sample_event_data,
        sample_workflow_data,
        mock_error_response
    ):
        """Test workflow execution with HTTP error."""
        # Setup
        user_id = "test-user-123"
        workflow_id = "test-workflow-456"
        
        # Mock HTTP client with error response
        mock_client = AsyncMock()
        mock_client.post = AsyncMock(return_value=mock_error_response)
        workflow_executor.client = mock_client
        
        # Execute
        result = await workflow_executor.execute_workflow(
            user_id=user_id,
            workflow_id=workflow_id,
            workflow_data=sample_workflow_data,
            event_data=sample_event_data
        )
        
        # Assertions
        assert result is None
        mock_client.post.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_execute_workflow_network_exception(
        self,
        workflow_executor,
        sample_event_data,
        sample_workflow_data
    ):
        """Test workflow execution with network exception."""
        # Setup
        user_id = "test-user-123"
        workflow_id = "test-workflow-456"
        
        # Mock HTTP client with exception
        mock_client = AsyncMock()
        mock_client.post = AsyncMock(side_effect=Exception("Network error"))
        workflow_executor.client = mock_client
        
        # Execute
        result = await workflow_executor.execute_workflow(
            user_id=user_id,
            workflow_id=workflow_id,
            workflow_data=sample_workflow_data,
            event_data=sample_event_data
        )
        
        # Assertions
        assert result is None
        mock_client.post.assert_called_once()
    
    def test_transform_event_to_payload_google_calendar(
        self,
        workflow_executor,
        sample_event_data,
        sample_workflow_data
    ):
        """Test event data transformation for Google Calendar."""
        # Execute
        result = workflow_executor._transform_event_to_payload(
            sample_event_data,
            sample_workflow_data
        )
        
        # Assertions
        assert result["trigger_type"] == "google_calendar"
        assert result["event_type"] == "created"
        assert result["event_data"] == sample_event_data
        assert result["trigger_config"] == sample_workflow_data
    
    def test_transform_event_to_payload_minimal_data(
        self,
        workflow_executor
    ):
        """Test event data transformation with minimal data."""
        # Setup
        minimal_event = {"event_type": "test"}
        minimal_workflow = {"type": "test"}
        
        # Execute
        result = workflow_executor._transform_event_to_payload(
            minimal_event,
            minimal_workflow
        )
        
        # Assertions
        assert result["trigger_type"] == "unknown"
        assert result["event_type"] == "test"
        assert result["event_data"] == minimal_event
        assert result["trigger_config"] == minimal_workflow
    
    @pytest.mark.asyncio
    async def test_execute_workflow_missing_correlation_id(
        self,
        workflow_executor,
        sample_event_data,
        sample_workflow_data
    ):
        """Test workflow execution when response is missing correlation ID."""
        # Setup
        user_id = "test-user-123"
        workflow_id = "test-workflow-456"
        
        # Mock response without correlation ID
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"status": "success"}  # No correlationId
        
        mock_client = AsyncMock()
        mock_client.post = AsyncMock(return_value=mock_response)
        workflow_executor.client = mock_client
        
        # Execute
        result = await workflow_executor.execute_workflow(
            user_id=user_id,
            workflow_id=workflow_id,
            workflow_data=sample_workflow_data,
            event_data=sample_event_data
        )
        
        # Assertions
        assert result is None
        mock_client.post.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_execute_workflow_invalid_json_response(
        self,
        workflow_executor,
        sample_event_data,
        sample_workflow_data
    ):
        """Test workflow execution with invalid JSON response."""
        # Setup
        user_id = "test-user-123"
        workflow_id = "test-workflow-456"
        
        # Mock response with invalid JSON
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.side_effect = ValueError("Invalid JSON")
        
        mock_client = AsyncMock()
        mock_client.post = AsyncMock(return_value=mock_response)
        workflow_executor.client = mock_client
        
        # Execute
        result = await workflow_executor.execute_workflow(
            user_id=user_id,
            workflow_id=workflow_id,
            workflow_data=sample_workflow_data,
            event_data=sample_event_data
        )
        
        # Assertions
        assert result is None
        mock_client.post.assert_called_once()
    
    def test_format_request_headers(self, workflow_executor, mock_settings):
        """Test request headers formatting."""
        # Setup
        workflow_executor.settings = mock_settings
        
        # Execute - this is tested indirectly through execute_workflow
        # but we can verify the settings are used correctly
        assert workflow_executor.settings.workflow_service_api_key == "test-workflow-key"
        assert workflow_executor.settings.workflow_service_url == "http://localhost:8001"
    
    @pytest.mark.asyncio
    async def test_execute_workflow_timeout(
        self,
        workflow_executor,
        sample_event_data,
        sample_workflow_data
    ):
        """Test workflow execution with timeout."""
        # Setup
        user_id = "test-user-123"
        workflow_id = "test-workflow-456"
        
        # Mock HTTP client with timeout
        import httpx
        mock_client = AsyncMock()
        mock_client.post = AsyncMock(side_effect=httpx.TimeoutException("Request timed out"))
        workflow_executor.client = mock_client
        
        # Execute
        result = await workflow_executor.execute_workflow(
            user_id=user_id,
            workflow_id=workflow_id,
            workflow_data=sample_workflow_data,
            event_data=sample_event_data
        )
        
        # Assertions
        assert result is None
        mock_client.post.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_execute_workflow_connection_error(
        self,
        workflow_executor,
        sample_event_data,
        sample_workflow_data
    ):
        """Test workflow execution with connection error."""
        # Setup
        user_id = "test-user-123"
        workflow_id = "test-workflow-456"
        
        # Mock HTTP client with connection error
        import httpx
        mock_client = AsyncMock()
        mock_client.post = AsyncMock(side_effect=httpx.ConnectError("Connection failed"))
        workflow_executor.client = mock_client
        
        # Execute
        result = await workflow_executor.execute_workflow(
            user_id=user_id,
            workflow_id=workflow_id,
            workflow_data=sample_workflow_data,
            event_data=sample_event_data
        )
        
        # Assertions
        assert result is None
        mock_client.post.assert_called_once()
    
    def test_workflow_executor_initialization(self, mock_settings, mock_logger):
        """Test WorkflowExecutor initialization."""
        with patch("src.core.workflow_executor.get_settings", return_value=mock_settings), \
             patch("src.core.workflow_executor.get_logger", return_value=mock_logger):
            
            executor = WorkflowExecutor()
            
            # Assertions
            assert executor.settings == mock_settings
            assert executor.client is not None
