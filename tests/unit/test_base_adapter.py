"""
Unit tests for the BaseTriggerAdapter class.

This module tests the base adapter interface and common functionality
that all trigger adapters should implement.
"""

import pytest
from abc import ABC
from unittest.mock import MagicMock, patch
from uuid import uuid4
from datetime import datetime

from src.adapters.base import BaseTriggerAdapter, TriggerEvent, EventType, TriggerStatus
from src.database.models import Trigger
from tests.fixtures.database import sample_trigger_data
from tests.fixtures.mocks import mock_logger


class ConcreteTriggerAdapter(BaseTriggerAdapter):
    """Concrete implementation of BaseTriggerAdapter for testing."""
    
    def __init__(self):
        super().__init__()
        self.setup_calls = []
        self.remove_calls = []
        self.process_calls = []
    
    async def setup_trigger(self, trigger: Trigger) -> bool:
        """Mock implementation of setup_trigger."""
        self.setup_calls.append(trigger)
        return True
    
    async def remove_trigger(self, trigger: Trigger) -> bool:
        """Mock implementation of remove_trigger."""
        self.remove_calls.append(trigger)
        return True
    
    async def process_event(self, event_data: dict, trigger: Trigger) -> bool:
        """Mock implementation of process_event."""
        self.process_calls.append((event_data, trigger))
        return True
    
    async def get_health(self) -> dict:
        """Mock implementation of get_health."""
        return {
            "status": "healthy",
            "external_service_status": "available"
        }


class TestBaseTriggerAdapter:
    """Test cases for BaseTriggerAdapter class."""
    
    @pytest.fixture
    def adapter(self, mock_logger):
        """Create a concrete adapter instance for testing."""
        with patch("src.adapters.base.get_logger", return_value=mock_logger):
            return ConcreteTriggerAdapter()
    
    @pytest.fixture
    def sample_trigger(self, sample_trigger_data):
        """Create a sample trigger for testing."""
        trigger = Trigger(**sample_trigger_data)
        trigger.id = uuid4()
        return trigger
    
    def test_adapter_is_abstract(self):
        """Test that BaseTriggerAdapter cannot be instantiated directly."""
        with pytest.raises(TypeError):
            BaseTriggerAdapter()
    
    def test_adapter_inheritance(self, adapter):
        """Test that concrete adapter properly inherits from base."""
        assert isinstance(adapter, BaseTriggerAdapter)
        assert hasattr(adapter, 'setup_trigger')
        assert hasattr(adapter, 'remove_trigger')
        assert hasattr(adapter, 'process_event')
        assert hasattr(adapter, 'get_health')
    
    @pytest.mark.asyncio
    async def test_setup_trigger_interface(self, adapter, sample_trigger):
        """Test setup_trigger interface."""
        result = await adapter.setup_trigger(sample_trigger)
        
        assert result is True
        assert len(adapter.setup_calls) == 1
        assert adapter.setup_calls[0] == sample_trigger
    
    @pytest.mark.asyncio
    async def test_remove_trigger_interface(self, adapter, sample_trigger):
        """Test remove_trigger interface."""
        result = await adapter.remove_trigger(sample_trigger)
        
        assert result is True
        assert len(adapter.remove_calls) == 1
        assert adapter.remove_calls[0] == sample_trigger
    
    @pytest.mark.asyncio
    async def test_process_event_interface(self, adapter, sample_trigger):
        """Test process_event interface."""
        event_data = {
            "event_type": "created",
            "event_id": "test-123",
            "summary": "Test Event"
        }
        
        result = await adapter.process_event(event_data, sample_trigger)
        
        assert result is True
        assert len(adapter.process_calls) == 1
        assert adapter.process_calls[0] == (event_data, sample_trigger)
    
    @pytest.mark.asyncio
    async def test_get_health_interface(self, adapter):
        """Test get_health interface."""
        result = await adapter.get_health()
        
        assert isinstance(result, dict)
        assert "status" in result
        assert result["status"] == "healthy"
    
    @pytest.mark.asyncio
    async def test_pause_trigger_default_implementation(self, adapter, sample_trigger):
        """Test default pause_trigger implementation."""
        # The base class should have a default implementation
        result = await adapter.pause_trigger(sample_trigger)
        
        # Default implementation should return True
        assert result is True
    
    @pytest.mark.asyncio
    async def test_resume_trigger_default_implementation(self, adapter, sample_trigger):
        """Test default resume_trigger implementation."""
        # The base class should have a default implementation
        result = await adapter.resume_trigger(sample_trigger)
        
        # Default implementation should return True
        assert result is True
    
    @pytest.mark.asyncio
    async def test_get_trigger_info_default_implementation(self, adapter, sample_trigger):
        """Test default get_trigger_info implementation."""
        # The base class should have a default implementation
        result = await adapter.get_trigger_info(sample_trigger)
        
        # Default implementation should return empty dict
        assert isinstance(result, dict)


class TestTriggerEvent:
    """Test cases for TriggerEvent model."""
    
    def test_trigger_event_creation(self):
        """Test TriggerEvent creation with valid data."""
        event = TriggerEvent(
            event_type=EventType.CREATED,
            event_id="test-123",
            timestamp=datetime.utcnow(),
            data={"summary": "Test Event"}
        )
        
        assert event.event_type == EventType.CREATED
        assert event.event_id == "test-123"
        assert isinstance(event.timestamp, datetime)
        assert event.data["summary"] == "Test Event"
    
    def test_trigger_event_with_optional_fields(self):
        """Test TriggerEvent creation with optional fields."""
        event = TriggerEvent(
            event_type=EventType.UPDATED,
            event_id="test-456",
            timestamp=datetime.utcnow(),
            data={"summary": "Updated Event"},
            source="google_calendar",
            correlation_id="corr-123"
        )
        
        assert event.source == "google_calendar"
        assert event.correlation_id == "corr-123"
    
    def test_trigger_event_validation(self):
        """Test TriggerEvent validation."""
        # Test with invalid event_type
        with pytest.raises(ValueError):
            TriggerEvent(
                event_type="invalid_type",  # Should be EventType enum
                event_id="test-123",
                timestamp=datetime.utcnow(),
                data={}
            )
    
    def test_trigger_event_serialization(self):
        """Test TriggerEvent serialization."""
        event = TriggerEvent(
            event_type=EventType.CREATED,
            event_id="test-123",
            timestamp=datetime.utcnow(),
            data={"summary": "Test Event"}
        )
        
        # Test dict conversion
        event_dict = event.model_dump()
        assert event_dict["event_type"] == "created"
        assert event_dict["event_id"] == "test-123"
        assert "timestamp" in event_dict
        assert event_dict["data"]["summary"] == "Test Event"


class TestEventType:
    """Test cases for EventType enum."""
    
    def test_event_type_values(self):
        """Test EventType enum values."""
        assert EventType.CREATED.value == "created"
        assert EventType.UPDATED.value == "updated"
        assert EventType.DELETED.value == "deleted"
        assert EventType.REMINDER.value == "reminder"
    
    def test_event_type_from_string(self):
        """Test creating EventType from string."""
        assert EventType("created") == EventType.CREATED
        assert EventType("updated") == EventType.UPDATED
        assert EventType("deleted") == EventType.DELETED
        assert EventType("reminder") == EventType.REMINDER
    
    def test_event_type_invalid_value(self):
        """Test EventType with invalid value."""
        with pytest.raises(ValueError):
            EventType("invalid")


class TestTriggerStatus:
    """Test cases for TriggerStatus enum."""
    
    def test_trigger_status_values(self):
        """Test TriggerStatus enum values."""
        assert TriggerStatus.ACTIVE.value == "active"
        assert TriggerStatus.PAUSED.value == "paused"
        assert TriggerStatus.ERROR.value == "error"
        assert TriggerStatus.SETUP.value == "setup"
    
    def test_trigger_status_from_string(self):
        """Test creating TriggerStatus from string."""
        assert TriggerStatus("active") == TriggerStatus.ACTIVE
        assert TriggerStatus("paused") == TriggerStatus.PAUSED
        assert TriggerStatus("error") == TriggerStatus.ERROR
        assert TriggerStatus("setup") == TriggerStatus.SETUP
    
    def test_trigger_status_invalid_value(self):
        """Test TriggerStatus with invalid value."""
        with pytest.raises(ValueError):
            TriggerStatus("invalid")


class TestAdapterErrorHandling:
    """Test cases for adapter error handling."""
    
    class FailingAdapter(BaseTriggerAdapter):
        """Adapter that fails for testing error handling."""
        
        async def setup_trigger(self, trigger: Trigger) -> bool:
            raise Exception("Setup failed")
        
        async def remove_trigger(self, trigger: Trigger) -> bool:
            raise Exception("Remove failed")
        
        async def process_event(self, event_data: dict, trigger: Trigger) -> bool:
            raise Exception("Process failed")
        
        async def get_health(self) -> dict:
            raise Exception("Health check failed")
    
    @pytest.fixture
    def failing_adapter(self, mock_logger):
        """Create a failing adapter for testing."""
        with patch("src.adapters.base.get_logger", return_value=mock_logger):
            return self.FailingAdapter()
    
    @pytest.mark.asyncio
    async def test_setup_trigger_exception_handling(self, failing_adapter, sample_trigger):
        """Test exception handling in setup_trigger."""
        with pytest.raises(Exception, match="Setup failed"):
            await failing_adapter.setup_trigger(sample_trigger)
    
    @pytest.mark.asyncio
    async def test_remove_trigger_exception_handling(self, failing_adapter, sample_trigger):
        """Test exception handling in remove_trigger."""
        with pytest.raises(Exception, match="Remove failed"):
            await failing_adapter.remove_trigger(sample_trigger)
    
    @pytest.mark.asyncio
    async def test_process_event_exception_handling(self, failing_adapter, sample_trigger):
        """Test exception handling in process_event."""
        event_data = {"event_type": "created"}
        
        with pytest.raises(Exception, match="Process failed"):
            await failing_adapter.process_event(event_data, sample_trigger)
    
    @pytest.mark.asyncio
    async def test_get_health_exception_handling(self, failing_adapter):
        """Test exception handling in get_health."""
        with pytest.raises(Exception, match="Health check failed"):
            await failing_adapter.get_health()
