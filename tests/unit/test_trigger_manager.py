"""
Unit tests for the TriggerManager class.

This module tests the core functionality of the TriggerManager,
including CRUD operations, event processing, and adapter management.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4
from datetime import datetime

from src.core.trigger_manager import Trigger<PERSON>anager
from src.database.models import Trigger, TriggerExecution
from src.schemas.trigger import TriggerCreate, TriggerUpdate
from tests.fixtures.database import sample_trigger_data, sample_execution_data
from tests.fixtures.mocks import (
    mock_workflow_executor,
    mock_google_calendar_adapter,
    mock_settings,
    mock_logger
)


class TestTriggerManager:
    """Test cases for TriggerManager class."""
    
    @pytest.fixture
    def trigger_manager(self, mock_settings, mock_logger):
        """Create a TriggerManager instance for testing."""
        with patch("src.core.trigger_manager.get_settings", return_value=mock_settings), \
             patch("src.core.trigger_manager.get_logger", return_value=mock_logger):
            return TriggerManager()
    
    @pytest.mark.asyncio
    async def test_create_trigger_success(
        self,
        trigger_manager,
        sample_trigger_data,
        mock_async_session,
        mock_google_calendar_adapter
    ):
        """Test successful trigger creation."""
        # Setup
        trigger_create = TriggerCreate(**sample_trigger_data)
        mock_trigger = Trigger(**sample_trigger_data)
        mock_trigger.id = uuid4()
        
        # Mock database operations
        mock_async_session.add = MagicMock()
        mock_async_session.commit = AsyncMock()
        mock_async_session.refresh = AsyncMock()
        
        # Mock adapter
        trigger_manager.adapters = {"google_calendar": mock_google_calendar_adapter}
        mock_google_calendar_adapter.setup_trigger = AsyncMock(return_value=True)
        
        with patch("src.core.trigger_manager.get_async_session") as mock_get_session:
            mock_get_session.return_value.__aenter__.return_value = mock_async_session
            
            # Mock the Trigger constructor to return our mock
            with patch("src.core.trigger_manager.Trigger", return_value=mock_trigger):
                result = await trigger_manager.create_trigger(trigger_create)
        
        # Assertions
        assert result == mock_trigger
        mock_async_session.add.assert_called_once()
        mock_async_session.commit.assert_called_once()
        mock_google_calendar_adapter.setup_trigger.assert_called_once_with(mock_trigger)
    
    @pytest.mark.asyncio
    async def test_create_trigger_adapter_failure(
        self,
        trigger_manager,
        sample_trigger_data,
        mock_async_session,
        mock_google_calendar_adapter
    ):
        """Test trigger creation when adapter setup fails."""
        # Setup
        trigger_create = TriggerCreate(**sample_trigger_data)
        mock_trigger = Trigger(**sample_trigger_data)
        mock_trigger.id = uuid4()
        
        # Mock database operations
        mock_async_session.add = MagicMock()
        mock_async_session.commit = AsyncMock()
        mock_async_session.rollback = AsyncMock()
        mock_async_session.refresh = AsyncMock()
        
        # Mock adapter failure
        trigger_manager.adapters = {"google_calendar": mock_google_calendar_adapter}
        mock_google_calendar_adapter.setup_trigger = AsyncMock(return_value=False)
        
        with patch("src.core.trigger_manager.get_async_session") as mock_get_session:
            mock_get_session.return_value.__aenter__.return_value = mock_async_session
            
            with patch("src.core.trigger_manager.Trigger", return_value=mock_trigger):
                result = await trigger_manager.create_trigger(trigger_create)
        
        # Assertions
        assert result is None
        mock_async_session.rollback.assert_called_once()
        mock_google_calendar_adapter.setup_trigger.assert_called_once_with(mock_trigger)
    
    @pytest.mark.asyncio
    async def test_get_trigger_success(
        self,
        trigger_manager,
        sample_trigger,
        mock_async_session
    ):
        """Test successful trigger retrieval."""
        # Setup
        trigger_id = sample_trigger.id
        
        # Mock database query
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_trigger
        mock_async_session.execute = AsyncMock(return_value=mock_result)
        
        with patch("src.core.trigger_manager.get_async_session") as mock_get_session:
            mock_get_session.return_value.__aenter__.return_value = mock_async_session
            
            result = await trigger_manager.get_trigger(trigger_id)
        
        # Assertions
        assert result == sample_trigger
        mock_async_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_trigger_not_found(
        self,
        trigger_manager,
        mock_async_session
    ):
        """Test trigger retrieval when trigger doesn't exist."""
        # Setup
        trigger_id = uuid4()
        
        # Mock database query returning None
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_async_session.execute = AsyncMock(return_value=mock_result)
        
        with patch("src.core.trigger_manager.get_async_session") as mock_get_session:
            mock_get_session.return_value.__aenter__.return_value = mock_async_session
            
            result = await trigger_manager.get_trigger(trigger_id)
        
        # Assertions
        assert result is None
        mock_async_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_update_trigger_success(
        self,
        trigger_manager,
        sample_trigger,
        mock_async_session,
        mock_google_calendar_adapter
    ):
        """Test successful trigger update."""
        # Setup
        trigger_id = sample_trigger.id
        update_data = TriggerUpdate(
            trigger_name="Updated Trigger Name",
            is_active=False
        )
        
        # Mock database operations
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_trigger
        mock_async_session.execute = AsyncMock(return_value=mock_result)
        mock_async_session.commit = AsyncMock()
        mock_async_session.refresh = AsyncMock()
        
        # Mock adapter
        trigger_manager.adapters = {"google_calendar": mock_google_calendar_adapter}
        mock_google_calendar_adapter.pause_trigger = AsyncMock(return_value=True)
        
        with patch("src.core.trigger_manager.get_async_session") as mock_get_session:
            mock_get_session.return_value.__aenter__.return_value = mock_async_session
            
            result = await trigger_manager.update_trigger(trigger_id, update_data)
        
        # Assertions
        assert result == sample_trigger
        assert sample_trigger.trigger_name == "Updated Trigger Name"
        assert sample_trigger.is_active is False
        mock_async_session.commit.assert_called_once()
        mock_google_calendar_adapter.pause_trigger.assert_called_once_with(sample_trigger)
    
    @pytest.mark.asyncio
    async def test_delete_trigger_success(
        self,
        trigger_manager,
        sample_trigger,
        mock_async_session,
        mock_google_calendar_adapter
    ):
        """Test successful trigger deletion."""
        # Setup
        trigger_id = sample_trigger.id
        
        # Mock database operations
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_trigger
        mock_async_session.execute = AsyncMock(return_value=mock_result)
        mock_async_session.delete = MagicMock()
        mock_async_session.commit = AsyncMock()
        
        # Mock adapter
        trigger_manager.adapters = {"google_calendar": mock_google_calendar_adapter}
        mock_google_calendar_adapter.remove_trigger = AsyncMock(return_value=True)
        
        with patch("src.core.trigger_manager.get_async_session") as mock_get_session:
            mock_get_session.return_value.__aenter__.return_value = mock_async_session
            
            result = await trigger_manager.delete_trigger(trigger_id)
        
        # Assertions
        assert result is True
        mock_async_session.delete.assert_called_once_with(sample_trigger)
        mock_async_session.commit.assert_called_once()
        mock_google_calendar_adapter.remove_trigger.assert_called_once_with(sample_trigger)
    
    @pytest.mark.asyncio
    async def test_list_triggers_with_filters(
        self,
        trigger_manager,
        multiple_triggers,
        mock_async_session
    ):
        """Test listing triggers with filters."""
        # Setup
        user_id = "test-user-123"
        
        # Mock database query
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = multiple_triggers
        mock_async_session.execute = AsyncMock(return_value=mock_result)
        
        with patch("src.core.trigger_manager.get_async_session") as mock_get_session:
            mock_get_session.return_value.__aenter__.return_value = mock_async_session
            
            result = await trigger_manager.list_triggers(
                user_id=user_id,
                trigger_type="google_calendar",
                is_active=True,
                limit=10,
                offset=0
            )
        
        # Assertions
        assert result == multiple_triggers
        mock_async_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_process_event_success(
        self,
        trigger_manager,
        sample_trigger,
        mock_async_session,
        mock_workflow_executor,
        mock_google_calendar_adapter
    ):
        """Test successful event processing."""
        # Setup
        event_data = {
            "event_type": "created",
            "event_id": "test-event-123",
            "summary": "Test Meeting"
        }
        
        # Mock database operations
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [sample_trigger]
        mock_async_session.execute = AsyncMock(return_value=mock_result)
        mock_async_session.add = MagicMock()
        mock_async_session.commit = AsyncMock()
        
        # Mock workflow executor
        trigger_manager.workflow_executor = mock_workflow_executor
        mock_workflow_executor.execute_workflow = AsyncMock(return_value="correlation-123")
        
        # Mock adapter
        trigger_manager.adapters = {"google_calendar": mock_google_calendar_adapter}
        
        with patch("src.core.trigger_manager.get_async_session") as mock_get_session:
            mock_get_session.return_value.__aenter__.return_value = mock_async_session
            
            with patch("src.core.trigger_manager.TriggerExecution") as mock_execution_class:
                mock_execution = MagicMock()
                mock_execution_class.return_value = mock_execution
                
                result = await trigger_manager.process_event(
                    "google_calendar",
                    event_data
                )
        
        # Assertions
        assert result is True
        mock_workflow_executor.execute_workflow.assert_called_once()
        mock_async_session.add.assert_called()
        mock_async_session.commit.assert_called()
    
    def test_register_adapter(self, trigger_manager, mock_google_calendar_adapter):
        """Test adapter registration."""
        # Execute
        trigger_manager.register_adapter("google_calendar", mock_google_calendar_adapter)
        
        # Assertions
        assert "google_calendar" in trigger_manager.adapters
        assert trigger_manager.adapters["google_calendar"] == mock_google_calendar_adapter
    
    def test_get_adapter_existing(self, trigger_manager, mock_google_calendar_adapter):
        """Test getting existing adapter."""
        # Setup
        trigger_manager.adapters = {"google_calendar": mock_google_calendar_adapter}
        
        # Execute
        result = trigger_manager.get_adapter("google_calendar")
        
        # Assertions
        assert result == mock_google_calendar_adapter
    
    def test_get_adapter_non_existing(self, trigger_manager):
        """Test getting non-existing adapter."""
        # Execute
        result = trigger_manager.get_adapter("non_existing")
        
        # Assertions
        assert result is None
