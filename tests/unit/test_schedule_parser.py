import pytest
from datetime import datetime, timedelta, timezone
from unittest.mock import patch
import pytz

from src.utils.schedule_parser import (
    ScheduleParser,
    InvalidScheduleConfigError,
)
from src.schemas.scheduler import (
    SimplifiedSchedulerResponse,
    ScheduleFrequency,
)


def create_simplified_scheduler(frequency, **kwargs):
    """Helper function to create SimplifiedSchedulerResponse objects for testing."""
    base_data = {
        "id": "test_scheduler_123",
        "name": "Test Scheduler",
        "frequency": frequency,
        "timezone": "UTC",
        "is_active": True,
        "workflow_id": "test_workflow_456",
        "user_id": "test_user_789",
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc),
        "last_run_at": None,
        "next_run_at": None,
        "scheduler_metadata": None,
    }
    base_data.update(kwargs)
    return SimplifiedSchedulerResponse(**base_data)


class TestEveryMinuteFrequency:
    """Tests for every_minute frequency."""

    def test_every_minute_first_run(self):
        """Test every_minute frequency for first run."""
        scheduler = create_simplified_scheduler(ScheduleFrequency.EVERY_MINUTE)

        with patch("src.utils.schedule_parser.datetime") as mock_datetime:
            mock_now = datetime(2025, 1, 17, 10, 30, 0, tzinfo=timezone.utc)
            mock_datetime.now.return_value = mock_now

            next_run = ScheduleParser.get_next_run_time(scheduler)

            assert next_run is not None
            expected = mock_now + timedelta(minutes=1)
            assert next_run == expected

    def test_every_minute_with_last_run_future(self):
        """Test every_minute frequency with last run time in future."""
        scheduler = create_simplified_scheduler(ScheduleFrequency.EVERY_MINUTE)

        with patch("src.utils.schedule_parser.datetime") as mock_datetime:
            mock_now = datetime(2025, 1, 17, 10, 30, 0, tzinfo=timezone.utc)
            mock_datetime.now.return_value = mock_now

            # Last run was 30 seconds ago
            last_run = mock_now - timedelta(seconds=30)
            next_run = ScheduleParser.get_next_run_time(scheduler, last_run)

            assert next_run is not None
            # Should be 1 minute after last run (30 seconds from now)
            expected = last_run + timedelta(minutes=1)
            assert next_run == expected


class TestHourlyFrequency:
    """Tests for hourly frequency."""

    def test_hourly_first_run(self):
        """Test hourly frequency for first run."""
        scheduler = create_simplified_scheduler(ScheduleFrequency.HOURLY)

        with patch("src.utils.schedule_parser.datetime") as mock_datetime:
            mock_now = datetime(2025, 1, 17, 10, 30, 0, tzinfo=timezone.utc)
            mock_datetime.now.return_value = mock_now

            next_run = ScheduleParser.get_next_run_time(scheduler)

            assert next_run is not None
            expected = mock_now + timedelta(hours=1)
            assert next_run == expected

    def test_hourly_with_last_run_future(self):
        """Test hourly frequency with last run time in future."""
        scheduler = create_simplified_scheduler(ScheduleFrequency.HOURLY)

        with patch("src.utils.schedule_parser.datetime") as mock_datetime:
            mock_now = datetime(2025, 1, 17, 10, 30, 0, tzinfo=timezone.utc)
            mock_datetime.now.return_value = mock_now

            # Last run was 30 minutes ago
            last_run = mock_now - timedelta(minutes=30)
            next_run = ScheduleParser.get_next_run_time(scheduler, last_run)

            assert next_run is not None
            # Should be 1 hour after last run (30 minutes from now)
            expected = last_run + timedelta(hours=1)
            assert next_run == expected


class TestDailyFrequency:
    """Tests for daily frequency."""

    def test_daily_same_day_future_time(self):
        """Test daily frequency when target time is later today."""
        scheduler = create_simplified_scheduler(
            ScheduleFrequency.DAILY, time="15:30", timezone="UTC"
        )

        with patch("src.utils.schedule_parser.datetime") as mock_datetime:
            mock_now = datetime(2025, 1, 17, 10, 0, 0, tzinfo=timezone.utc)
            mock_datetime.now.return_value = mock_now

            next_run = ScheduleParser.get_next_run_time(scheduler)

            assert next_run is not None
            expected = datetime(2025, 1, 17, 15, 30, 0, tzinfo=timezone.utc)
            assert next_run == expected

    def test_daily_next_day(self):
        """Test daily frequency when target time has passed today."""
        scheduler = create_simplified_scheduler(
            ScheduleFrequency.DAILY, time="09:30", timezone="UTC"
        )

        with patch("src.utils.schedule_parser.datetime") as mock_datetime:
            mock_now = datetime(2025, 1, 17, 10, 0, 0, tzinfo=timezone.utc)
            mock_datetime.now.return_value = mock_now

            next_run = ScheduleParser.get_next_run_time(scheduler)

            assert next_run is not None
            expected = datetime(2025, 1, 18, 9, 30, 0, tzinfo=timezone.utc)
            assert next_run == expected

    def test_daily_with_timezone(self):
        """Test daily frequency with non-UTC timezone."""
        scheduler = create_simplified_scheduler(
            ScheduleFrequency.DAILY, time="10:30", timezone="Asia/Kolkata"
        )

        next_run = ScheduleParser.get_next_run_time(scheduler)

        assert next_run is not None
        assert next_run.tzinfo == pytz.utc


class TestWeeklyFrequency:
    """Tests for weekly frequency."""

    def test_weekly_same_day_future_time(self):
        """Test weekly frequency when target day is today and time is in future."""
        scheduler = create_simplified_scheduler(
            ScheduleFrequency.WEEKLY,
            time="15:30",
            days_of_week=["Friday"],
            timezone="UTC",
        )

        with patch("src.utils.schedule_parser.datetime") as mock_datetime:
            # Mock Friday 10:00 AM
            mock_now = datetime(2025, 1, 17, 10, 0, 0, tzinfo=timezone.utc)  # Friday
            mock_datetime.now.return_value = mock_now

            next_run = ScheduleParser.get_next_run_time(scheduler)

            assert next_run is not None
            expected = datetime(
                2025, 1, 17, 15, 30, 0, tzinfo=timezone.utc
            )  # Same Friday
            assert next_run == expected

    def test_weekly_next_occurrence(self):
        """Test weekly frequency when target day is in the future."""
        scheduler = create_simplified_scheduler(
            ScheduleFrequency.WEEKLY,
            time="10:30",
            days_of_week=["Monday"],
            timezone="UTC",
        )

        with patch("src.utils.schedule_parser.datetime") as mock_datetime:
            # Mock Friday
            mock_now = datetime(2025, 1, 17, 15, 0, 0, tzinfo=timezone.utc)  # Friday
            mock_datetime.now.return_value = mock_now

            next_run = ScheduleParser.get_next_run_time(scheduler)

            assert next_run is not None
            expected = datetime(
                2025, 1, 20, 10, 30, 0, tzinfo=timezone.utc
            )  # Next Monday
            assert next_run == expected

    def test_weekly_multiple_days(self):
        """Test weekly frequency with multiple days."""
        scheduler = create_simplified_scheduler(
            ScheduleFrequency.WEEKLY,
            time="10:30",
            days_of_week=["Monday", "Wednesday", "Friday"],
            timezone="UTC",
        )

        with patch("src.utils.schedule_parser.datetime") as mock_datetime:
            # Mock Tuesday
            mock_now = datetime(2025, 1, 14, 15, 0, 0, tzinfo=timezone.utc)  # Tuesday
            mock_datetime.now.return_value = mock_now

            next_run = ScheduleParser.get_next_run_time(scheduler)

            assert next_run is not None
            expected = datetime(
                2025, 1, 15, 10, 30, 0, tzinfo=timezone.utc
            )  # Next Wednesday
            assert next_run == expected


class TestMonthlyFrequency:
    """Tests for monthly frequency."""

    def test_monthly_same_month_future_day(self):
        """Test monthly frequency when target day is later this month."""
        scheduler = create_simplified_scheduler(
            ScheduleFrequency.MONTHLY, time="10:30", days_of_month=[25], timezone="UTC"
        )

        with patch("src.utils.schedule_parser.datetime") as mock_datetime:
            # Mock January 15th
            mock_now = datetime(2025, 1, 15, 9, 0, 0, tzinfo=timezone.utc)
            mock_datetime.now.return_value = mock_now

            next_run = ScheduleParser.get_next_run_time(scheduler)

            assert next_run is not None
            expected = datetime(2025, 1, 25, 10, 30, 0, tzinfo=timezone.utc)
            assert next_run == expected

    def test_monthly_next_month(self):
        """Test monthly frequency when target day has passed this month."""
        scheduler = create_simplified_scheduler(
            ScheduleFrequency.MONTHLY, time="10:30", days_of_month=[15], timezone="UTC"
        )

        with patch("src.utils.schedule_parser.datetime") as mock_datetime:
            # Mock January 20th
            mock_now = datetime(2025, 1, 20, 15, 0, 0, tzinfo=timezone.utc)
            mock_datetime.now.return_value = mock_now

            next_run = ScheduleParser.get_next_run_time(scheduler)

            assert next_run is not None
            expected = datetime(2025, 2, 15, 10, 30, 0, tzinfo=timezone.utc)
            assert next_run == expected

    def test_monthly_multiple_days(self):
        """Test monthly frequency with multiple days."""
        scheduler = create_simplified_scheduler(
            ScheduleFrequency.MONTHLY,
            time="10:30",
            days_of_month=[1, 15, 30],
            timezone="UTC",
        )

        with patch("src.utils.schedule_parser.datetime") as mock_datetime:
            # Mock January 10th
            mock_now = datetime(2025, 1, 10, 15, 0, 0, tzinfo=timezone.utc)
            mock_datetime.now.return_value = mock_now

            next_run = ScheduleParser.get_next_run_time(scheduler)

            assert next_run is not None
            expected = datetime(2025, 1, 15, 10, 30, 0, tzinfo=timezone.utc)
            assert next_run == expected

    def test_monthly_invalid_day(self):
        """Test monthly frequency handles invalid days (e.g., Feb 30)."""
        scheduler = create_simplified_scheduler(
            ScheduleFrequency.MONTHLY,
            time="10:30",
            days_of_month=[30, 31],
            timezone="UTC",
        )

        with patch("src.utils.schedule_parser.datetime") as mock_datetime:
            # Mock February 25th (February doesn't have 30/31)
            mock_now = datetime(2025, 2, 25, 15, 0, 0, tzinfo=timezone.utc)
            mock_datetime.now.return_value = mock_now

            next_run = ScheduleParser.get_next_run_time(scheduler)

            assert next_run is not None
            # Should schedule for March 30th (first valid day)
            expected = datetime(2025, 3, 30, 10, 30, 0, tzinfo=timezone.utc)
            assert next_run == expected


class TestCustomFrequency:
    """Tests for custom frequency with cron expressions."""

    def test_custom_valid_cron(self):
        """Test custom frequency with valid cron expression."""
        scheduler = create_simplified_scheduler(
            ScheduleFrequency.CUSTOM,
            cron_expression="0 10 * * 1",  # Every Monday at 10:00 AM
            timezone="UTC",
        )

        next_run = ScheduleParser.get_next_run_time(scheduler)

        assert next_run is not None
        assert next_run.tzinfo == pytz.utc
        # Should be a Monday at 10:00 AM
        assert next_run.weekday() == 0  # Monday
        assert next_run.hour == 10
        assert next_run.minute == 0

    def test_custom_with_timezone(self):
        """Test custom frequency with non-UTC timezone."""
        scheduler = create_simplified_scheduler(
            ScheduleFrequency.CUSTOM,
            cron_expression="0 10 * * *",  # Every day at 10:00 AM
            timezone="Asia/Kolkata",
        )

        next_run = ScheduleParser.get_next_run_time(scheduler)

        assert next_run is not None
        assert next_run.tzinfo == pytz.utc


class TestDictCompatibility:
    """Tests for dictionary-based schedule configurations."""

    def test_dict_frequency_based(self):
        """Test dictionary with frequency-based configuration."""
        config = {"frequency": "daily", "time": "10:30", "timezone": "UTC"}

        next_run = ScheduleParser.get_next_run_time(config)

        assert next_run is not None
        assert next_run.tzinfo == pytz.utc

    def test_dict_every_minute(self):
        """Test dictionary with every_minute frequency."""
        config = {"frequency": "every_minute", "timezone": "UTC"}

        next_run = ScheduleParser.get_next_run_time(config)

        assert next_run is not None
        assert next_run.tzinfo == pytz.utc

    def test_dict_weekly(self):
        """Test dictionary with weekly frequency."""
        config = {
            "frequency": "weekly",
            "time": "10:30",
            "days_of_week": ["Monday", "Friday"],
            "timezone": "UTC",
        }

        next_run = ScheduleParser.get_next_run_time(config)

        assert next_run is not None
        assert next_run.tzinfo == pytz.utc

    def test_dict_monthly(self):
        """Test dictionary with monthly frequency."""
        config = {
            "frequency": "monthly",
            "time": "10:30",
            "days_of_month": [1, 15],
            "timezone": "UTC",
        }

        next_run = ScheduleParser.get_next_run_time(config)

        assert next_run is not None
        assert next_run.tzinfo == pytz.utc

    def test_dict_custom(self):
        """Test dictionary with custom frequency."""
        config = {
            "frequency": "custom",
            "cron_expression": "0 10 * * *",
            "timezone": "UTC",
        }

        next_run = ScheduleParser.get_next_run_time(config)

        assert next_run is not None
        assert next_run.tzinfo == pytz.utc

    def test_dict_invalid_config(self):
        """Test dictionary with invalid configuration."""
        config = {"invalid_field": "value"}

        with pytest.raises(
            InvalidScheduleConfigError, match="missing 'frequency' field"
        ):
            ScheduleParser.get_next_run_time(config)

    def test_dict_unsupported_frequency(self):
        """Test dictionary with unsupported frequency."""
        config = {"frequency": "unsupported_frequency", "timezone": "UTC"}

        with pytest.raises(InvalidScheduleConfigError, match="Unsupported frequency"):
            ScheduleParser.get_next_run_time(config)


class TestUtilityMethods:
    """Tests for utility methods."""

    def test_convert_to_utc(self):
        """Test timezone conversion to UTC."""
        dt = datetime(2025, 1, 17, 10, 30, 0)
        utc_dt = ScheduleParser.convert_to_utc(dt, "Asia/Kolkata")

        assert utc_dt.tzinfo == pytz.utc
        # Kolkata is UTC+5:30, so 10:30 IST = 05:00 UTC
        assert utc_dt.hour == 5
        assert utc_dt.minute == 0

    def test_convert_from_utc(self):
        """Test timezone conversion from UTC."""
        utc_dt = datetime(2025, 1, 17, 5, 0, 0, tzinfo=timezone.utc)
        local_dt = ScheduleParser.convert_from_utc(utc_dt, "Asia/Kolkata")

        # UTC 05:00 = IST 10:30
        assert local_dt.hour == 10
        assert local_dt.minute == 30

    def test_convert_invalid_timezone(self):
        """Test timezone conversion with invalid timezone."""
        dt = datetime(2025, 1, 17, 10, 30, 0)

        with pytest.raises(InvalidScheduleConfigError, match="Error converting to UTC"):
            ScheduleParser.convert_to_utc(dt, "Invalid/Timezone")


class TestEdgeCases:
    """Tests for edge cases and error conditions."""

    def test_unsupported_schedule_type(self):
        """Test unsupported schedule configuration type."""
        with pytest.raises(
            InvalidScheduleConfigError, match="Unsupported schedule configuration type"
        ):
            ScheduleParser.get_next_run_time("invalid_type")

    def test_dst_transition(self):
        """Test DST transition handling."""
        # Test around DST transition in US Eastern timezone
        scheduler = create_simplified_scheduler(
            ScheduleFrequency.DAILY,
            time="02:30",  # Time that might not exist during DST transition
            timezone="America/New_York",
        )

        # This should not raise an exception
        next_run = ScheduleParser.get_next_run_time(scheduler)
        assert next_run is not None

    def test_leap_year_february_29(self):
        """Test handling of February 29 in leap years."""
        scheduler = create_simplified_scheduler(
            ScheduleFrequency.MONTHLY, time="10:30", days_of_month=[29], timezone="UTC"
        )

        with patch("src.utils.schedule_parser.datetime") as mock_datetime:
            # Mock February 28, 2025 (non-leap year)
            mock_now = datetime(2025, 2, 28, 15, 0, 0, tzinfo=timezone.utc)
            mock_datetime.now.return_value = mock_now

            next_run = ScheduleParser.get_next_run_time(scheduler)

            assert next_run is not None
            # Should skip to March 29 since Feb 29 doesn't exist in 2025
            assert next_run.month == 3
            assert next_run.day == 29


class TestValidationErrors:
    """Test that Pydantic validation works correctly."""

    def test_daily_requires_time(self):
        """Test that daily frequency requires time field."""
        with pytest.raises(Exception):  # Pydantic validation error
            create_simplified_scheduler(ScheduleFrequency.DAILY)

    def test_weekly_requires_time_and_days(self):
        """Test that weekly frequency requires time and days_of_week."""
        with pytest.raises(Exception):  # Pydantic validation error
            create_simplified_scheduler(ScheduleFrequency.WEEKLY, time="10:30")

        with pytest.raises(Exception):  # Pydantic validation error
            create_simplified_scheduler(
                ScheduleFrequency.WEEKLY, days_of_week=["Monday"]
            )

    def test_monthly_requires_time_and_days(self):
        """Test that monthly frequency requires time and days_of_month."""
        with pytest.raises(Exception):  # Pydantic validation error
            create_simplified_scheduler(ScheduleFrequency.MONTHLY, time="10:30")

        with pytest.raises(Exception):  # Pydantic validation error
            create_simplified_scheduler(ScheduleFrequency.MONTHLY, days_of_month=[15])

    def test_custom_requires_cron_expression(self):
        """Test that custom frequency requires cron_expression."""
        with pytest.raises(Exception):  # Pydantic validation error
            create_simplified_scheduler(ScheduleFrequency.CUSTOM)

    def test_invalid_timezone_validation(self):
        """Test that invalid timezone is caught by Pydantic validation."""
        with pytest.raises(Exception):  # Pydantic validation error
            create_simplified_scheduler(
                ScheduleFrequency.DAILY, time="10:30", timezone="Invalid/Timezone"
            )
