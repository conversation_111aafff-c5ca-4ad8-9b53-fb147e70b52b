"""
Unit tests for webhook API routes.

This module tests the webhook processing endpoints,
including Google Calendar webhooks and generic webhook handling.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi.testclient import TestClient

from src.main import app
from tests.fixtures.http import (
    google_calendar_webhook_payload,
    google_calendar_event_data,
    auth_headers
)
from tests.fixtures.mocks import mock_trigger_manager


class TestWebhookRoutes:
    """Test cases for webhook API routes."""
    
    @pytest.fixture
    def client(self):
        """Create a test client."""
        return TestClient(app)
    
    @pytest.fixture
    def mock_dependency_override(self, mock_trigger_manager):
        """Override dependencies for testing."""
        from src.api.routes.webhooks import get_trigger_manager
        
        app.dependency_overrides[get_trigger_manager] = lambda: mock_trigger_manager
        yield mock_trigger_manager
        app.dependency_overrides.clear()
    
    def test_google_calendar_webhook_success(
        self,
        client,
        mock_dependency_override,
        google_calendar_webhook_payload
    ):
        """Test successful Google Calendar webhook processing."""
        # Setup
        mock_dependency_override.process_event = AsyncMock(return_value=True)
        
        # Execute
        response = client.post(
            "/api/v1/webhooks/google-calendar",
            json=google_calendar_webhook_payload,
            headers={
                "X-Goog-Channel-ID": "test-channel-123",
                "X-Goog-Channel-Token": "test-token",
                "X-Goog-Resource-ID": "test-resource-456",
                "X-Goog-Resource-State": "exists"
            }
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "processed"
        mock_dependency_override.process_event.assert_called_once()
    
    def test_google_calendar_webhook_verification(self, client):
        """Test Google Calendar webhook verification."""
        # Execute
        response = client.get(
            "/api/v1/webhooks/google-calendar/verify",
            params={"challenge": "test-challenge-123"}
        )
        
        # Assertions
        assert response.status_code == 200
        assert response.text == "test-challenge-123"
    
    def test_google_calendar_webhook_missing_headers(
        self,
        client,
        google_calendar_webhook_payload
    ):
        """Test Google Calendar webhook with missing required headers."""
        # Execute
        response = client.post(
            "/api/v1/webhooks/google-calendar",
            json=google_calendar_webhook_payload
            # Missing required headers
        )
        
        # Assertions
        assert response.status_code == 400
        data = response.json()
        assert "Missing required headers" in data["detail"]
    
    def test_google_calendar_webhook_invalid_payload(self, client):
        """Test Google Calendar webhook with invalid payload."""
        # Execute
        response = client.post(
            "/api/v1/webhooks/google-calendar",
            json={"invalid": "payload"},
            headers={
                "X-Goog-Channel-ID": "test-channel-123",
                "X-Goog-Channel-Token": "test-token",
                "X-Goog-Resource-ID": "test-resource-456",
                "X-Goog-Resource-State": "exists"
            }
        )
        
        # Assertions
        assert response.status_code == 400
        data = response.json()
        assert "Invalid webhook payload" in data["detail"]
    
    def test_google_calendar_webhook_processing_failure(
        self,
        client,
        mock_dependency_override,
        google_calendar_webhook_payload
    ):
        """Test Google Calendar webhook when processing fails."""
        # Setup
        mock_dependency_override.process_event = AsyncMock(return_value=False)
        
        # Execute
        response = client.post(
            "/api/v1/webhooks/google-calendar",
            json=google_calendar_webhook_payload,
            headers={
                "X-Goog-Channel-ID": "test-channel-123",
                "X-Goog-Channel-Token": "test-token",
                "X-Goog-Resource-ID": "test-resource-456",
                "X-Goog-Resource-State": "exists"
            }
        )
        
        # Assertions
        assert response.status_code == 500
        data = response.json()
        assert "Failed to process webhook" in data["detail"]
    
    def test_google_calendar_webhook_sync_state(
        self,
        client,
        google_calendar_webhook_payload
    ):
        """Test Google Calendar webhook with sync state (should be ignored)."""
        # Execute
        response = client.post(
            "/api/v1/webhooks/google-calendar",
            json=google_calendar_webhook_payload,
            headers={
                "X-Goog-Channel-ID": "test-channel-123",
                "X-Goog-Channel-Token": "test-token",
                "X-Goog-Resource-ID": "test-resource-456",
                "X-Goog-Resource-State": "sync"  # Sync state should be ignored
            }
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "ignored"
        assert "sync" in data["message"]
    
    def test_generic_webhook_success(
        self,
        client,
        mock_dependency_override
    ):
        """Test successful generic webhook processing."""
        # Setup
        adapter_name = "test_adapter"
        webhook_payload = {
            "event_type": "created",
            "event_id": "test-123",
            "data": {"key": "value"}
        }
        
        mock_dependency_override.process_event = AsyncMock(return_value=True)
        
        # Execute
        response = client.post(
            f"/api/v1/webhooks/generic/{adapter_name}",
            json=webhook_payload,
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "processed"
        mock_dependency_override.process_event.assert_called_once_with(
            adapter_name,
            webhook_payload
        )
    
    def test_generic_webhook_unauthorized(self, client):
        """Test generic webhook without authentication."""
        # Execute
        response = client.post(
            "/api/v1/webhooks/generic/test_adapter",
            json={"event_type": "test"}
            # No auth headers
        )
        
        # Assertions
        assert response.status_code == 401
    
    def test_generic_webhook_invalid_adapter(
        self,
        client,
        mock_dependency_override,
        auth_headers
    ):
        """Test generic webhook with invalid adapter name."""
        # Setup
        mock_dependency_override.process_event = AsyncMock(return_value=False)
        
        # Execute
        response = client.post(
            "/api/v1/webhooks/generic/invalid_adapter",
            json={"event_type": "test"},
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 400
        data = response.json()
        assert "Failed to process webhook" in data["detail"]
    
    def test_generic_webhook_processing_exception(
        self,
        client,
        mock_dependency_override,
        auth_headers
    ):
        """Test generic webhook when processing raises exception."""
        # Setup
        mock_dependency_override.process_event = AsyncMock(
            side_effect=Exception("Processing error")
        )
        
        # Execute
        response = client.post(
            "/api/v1/webhooks/generic/test_adapter",
            json={"event_type": "test"},
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 500
        data = response.json()
        assert "Internal server error" in data["detail"]
    
    def test_webhook_status_success(
        self,
        client,
        mock_dependency_override,
        auth_headers
    ):
        """Test webhook status endpoint."""
        # Setup
        mock_status = {
            "google_calendar": {
                "active_webhooks": 5,
                "last_received": "2024-01-15T10:00:00Z",
                "status": "healthy"
            },
            "total_webhooks_processed": 150,
            "webhook_processing_rate": 2.5
        }
        
        mock_dependency_override.get_webhook_status = AsyncMock(return_value=mock_status)
        
        # Execute
        response = client.get(
            "/api/v1/webhooks/status",
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["google_calendar"]["active_webhooks"] == 5
        assert data["total_webhooks_processed"] == 150
        mock_dependency_override.get_webhook_status.assert_called_once()
    
    def test_webhook_status_unauthorized(self, client):
        """Test webhook status without authentication."""
        # Execute
        response = client.get("/api/v1/webhooks/status")
        
        # Assertions
        assert response.status_code == 401
    
    def test_google_calendar_webhook_with_event_data(
        self,
        client,
        mock_dependency_override,
        google_calendar_event_data
    ):
        """Test Google Calendar webhook with actual event data."""
        # Setup
        webhook_payload = {
            "kind": "api#channel",
            "id": "test-channel-123",
            "resourceId": "test-resource-456",
            "event_data": google_calendar_event_data
        }
        
        mock_dependency_override.process_event = AsyncMock(return_value=True)
        
        # Execute
        response = client.post(
            "/api/v1/webhooks/google-calendar",
            json=webhook_payload,
            headers={
                "X-Goog-Channel-ID": "test-channel-123",
                "X-Goog-Channel-Token": "test-token",
                "X-Goog-Resource-ID": "test-resource-456",
                "X-Goog-Resource-State": "exists"
            }
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "processed"
        
        # Verify the event data was passed correctly
        call_args = mock_dependency_override.process_event.call_args
        assert call_args[0][0] == "google_calendar"  # adapter name
        assert "event_data" in call_args[0][1]  # event payload
    
    def test_google_calendar_webhook_idempotency(
        self,
        client,
        mock_dependency_override,
        google_calendar_webhook_payload
    ):
        """Test Google Calendar webhook idempotency handling."""
        # Setup
        mock_dependency_override.process_event = AsyncMock(return_value=True)
        
        headers = {
            "X-Goog-Channel-ID": "test-channel-123",
            "X-Goog-Channel-Token": "test-token",
            "X-Goog-Resource-ID": "test-resource-456",
            "X-Goog-Resource-State": "exists",
            "X-Goog-Message-Number": "1"  # Message number for idempotency
        }
        
        # Execute the same webhook twice
        response1 = client.post(
            "/api/v1/webhooks/google-calendar",
            json=google_calendar_webhook_payload,
            headers=headers
        )
        
        response2 = client.post(
            "/api/v1/webhooks/google-calendar",
            json=google_calendar_webhook_payload,
            headers=headers
        )
        
        # Assertions
        assert response1.status_code == 200
        assert response2.status_code == 200
        
        # Both should be processed (idempotency handling depends on implementation)
        assert mock_dependency_override.process_event.call_count >= 1
    
    def test_webhook_error_handling(
        self,
        client,
        mock_dependency_override,
        google_calendar_webhook_payload
    ):
        """Test webhook error handling and logging."""
        # Setup
        mock_dependency_override.process_event = AsyncMock(
            side_effect=ValueError("Invalid event data")
        )
        
        # Execute
        response = client.post(
            "/api/v1/webhooks/google-calendar",
            json=google_calendar_webhook_payload,
            headers={
                "X-Goog-Channel-ID": "test-channel-123",
                "X-Goog-Channel-Token": "test-token",
                "X-Goog-Resource-ID": "test-resource-456",
                "X-Goog-Resource-State": "exists"
            }
        )
        
        # Assertions
        assert response.status_code == 500
        data = response.json()
        assert "error" in data or "detail" in data
