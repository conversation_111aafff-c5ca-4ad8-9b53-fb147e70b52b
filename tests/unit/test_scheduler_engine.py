import pytest
from unittest.mock import MagicMock, AsyncMock, patch
from datetime import datetime, timedelta, timezone
from sqlalchemy.ext.asyncio import AsyncSession
from src.core.scheduler_engine import SchedulerEngine
from src.core.workflow_executor import WorkflowExecutor
from src.database.models import Scheduler
from src.schemas.scheduler import ScheduleType, ScheduleFrequency


@pytest.fixture
def mock_session():
    session = AsyncMock(spec=AsyncSession)
    session.add = AsyncMock()
    session.commit = AsyncMock()
    session.refresh = AsyncMock()
    session.delete = AsyncMock()
    session.execute = AsyncMock()
    session.rollback = AsyncMock()
    return session


@pytest.fixture
def mock_workflow_executor():
    executor = AsyncMock(spec=WorkflowExecutor)
    executor.execute_workflow_with_fetch = AsyncMock()
    return executor


@pytest.fixture
def scheduler_engine(mock_session, mock_workflow_executor):
    return SchedulerEngine(
        db_session=mock_session, workflow_executor=mock_workflow_executor
    )


def create_test_scheduler(
    scheduler_id: str = "test_id",
    workflow_id: str = "test_workflow",
    frequency: str = "daily",
    is_active: bool = True,
    next_run_at: datetime = None,
    **kwargs,
) -> Scheduler:
    """Helper function to create test scheduler objects."""
    now = datetime.now(timezone.utc)

    # Set appropriate fields based on frequency
    time = None
    days_of_week = None
    days_of_month = None
    cron_expression = None

    if frequency == "daily":
        time = "10:00"
    elif frequency == "weekly":
        time = "10:00"
        days_of_week = ["Monday"]
    elif frequency == "monthly":
        time = "10:00"
        days_of_month = [1]
    elif frequency == "custom":
        cron_expression = "0 10 * * *"
    # hourly and every_minute don't need additional fields

    scheduler = Scheduler(
        id=scheduler_id,
        user_id="test_user",
        name="Test Scheduler",
        workflow_id=workflow_id,
        schedule_type="simplified",
        schedule_config={},
        workflow_config={},
        timezone="UTC",
        is_active=is_active,
        frequency=frequency,
        time=time,
        days_of_week=days_of_week,
        days_of_month=days_of_month,
        cron_expression=cron_expression,
        created_at=now,
        updated_at=now,
        next_run_at=next_run_at or now - timedelta(minutes=5),
        **kwargs,
    )
    return scheduler


@pytest.mark.asyncio
@patch("src.core.scheduler_engine.SchedulerManager")
async def test_process_due_schedulers_no_due_schedulers(
    mock_scheduler_manager_class, scheduler_engine, mock_session
):
    """Test process_due_schedulers when no schedulers are due."""
    mock_scheduler_manager = AsyncMock()
    mock_scheduler_manager.get_due_schedulers.return_value = []
    mock_scheduler_manager_class.return_value = mock_scheduler_manager

    await scheduler_engine.process_due_schedulers()

    mock_scheduler_manager_class.assert_called_once_with(mock_session)
    mock_scheduler_manager.get_due_schedulers.assert_called_once()
    scheduler_engine.workflow_executor.execute_workflow_with_fetch.assert_not_called()


@pytest.mark.asyncio
@patch("src.core.scheduler_engine.SchedulerManager")
@patch("src.utils.schedule_parser.ScheduleParser.get_next_run_time")
async def test_process_due_schedulers_single_due_scheduler(
    mock_get_next_run_time, mock_scheduler_manager_class, scheduler_engine, mock_session
):
    """Test process_due_schedulers with a single due scheduler."""
    now = datetime.now(timezone.utc)
    due_scheduler = create_test_scheduler(
        scheduler_id="s1",
        workflow_id="w1",
        frequency="daily",
        next_run_at=now - timedelta(minutes=5),
    )

    mock_scheduler_manager = AsyncMock()
    mock_scheduler_manager.get_due_schedulers.return_value = [due_scheduler]
    mock_scheduler_manager_class.return_value = mock_scheduler_manager

    mock_get_next_run_time.return_value = now + timedelta(hours=24)

    await scheduler_engine.process_due_schedulers()

    mock_scheduler_manager.get_due_schedulers.assert_called_once()
    scheduler_engine.workflow_executor.execute_workflow_with_fetch.assert_called_once_with(
        workflow_id="w1",
        payload={"scheduler_id": "s1", "scheduler_name": "Test Scheduler"},
    )
    assert due_scheduler.last_run_at is not None
    assert due_scheduler.next_run_at == now + timedelta(hours=24)
    mock_session.add.assert_called_once_with(due_scheduler)
    mock_session.commit.assert_called_once()
    mock_session.refresh.assert_called_once_with(due_scheduler)


@pytest.mark.asyncio
@patch("src.core.scheduler_engine.SchedulerManager")
async def test_process_due_schedulers_inactive_scheduler_skipped(
    mock_scheduler_manager_class, scheduler_engine, mock_session
):
    """Test that inactive schedulers are not processed."""
    now = datetime.now(timezone.utc)
    inactive_scheduler = create_test_scheduler(
        scheduler_id="s2",
        workflow_id="w2",
        frequency="daily",
        is_active=False,
        next_run_at=now - timedelta(minutes=5),
    )

    mock_scheduler_manager = AsyncMock()
    # Assuming SchedulerManager.get_due_schedulers() already filters out inactive schedulers
    mock_scheduler_manager.get_due_schedulers.return_value = []
    mock_scheduler_manager_class.return_value = mock_scheduler_manager

    await scheduler_engine.process_due_schedulers()

    scheduler_engine.workflow_executor.execute_workflow_with_fetch.assert_not_called()
    mock_session.commit.assert_not_called()


@pytest.mark.asyncio
@patch("src.core.scheduler_engine.SchedulerManager")
@patch("src.utils.schedule_parser.ScheduleParser.get_next_run_time")
async def test_process_due_schedulers_workflow_execution_failure(
    mock_get_next_run_time, mock_scheduler_manager_class, scheduler_engine, mock_session
):
    """Test process_due_schedulers when workflow execution fails."""
    now = datetime.now(timezone.utc)
    failing_scheduler = create_test_scheduler(
        scheduler_id="s3",
        workflow_id="w3",
        frequency="daily",
        next_run_at=now - timedelta(minutes=5),
    )

    mock_scheduler_manager = AsyncMock()
    mock_scheduler_manager.get_due_schedulers.return_value = [failing_scheduler]
    mock_scheduler_manager_class.return_value = mock_scheduler_manager

    # Mock workflow execution to fail
    scheduler_engine.workflow_executor.execute_workflow_with_fetch.side_effect = (
        Exception("Workflow execution failed")
    )
    mock_get_next_run_time.return_value = now + timedelta(hours=24)

    await scheduler_engine.process_due_schedulers()

    scheduler_engine.workflow_executor.execute_workflow_with_fetch.assert_called_once()
    # Should rollback on failure
    mock_session.rollback.assert_called()


@pytest.mark.asyncio
@patch("src.core.scheduler_engine.SchedulerManager")
@patch("src.utils.schedule_parser.ScheduleParser.get_next_run_time")
async def test_process_due_schedulers_missing_frequency_field(
    mock_get_next_run_time, mock_scheduler_manager_class, scheduler_engine, mock_session
):
    """Test process_due_schedulers with scheduler missing frequency field."""
    now = datetime.now(timezone.utc)
    scheduler_without_frequency = create_test_scheduler(
        scheduler_id="s4", workflow_id="w4", next_run_at=now - timedelta(minutes=5)
    )
    # Remove frequency field to simulate old scheduler
    scheduler_without_frequency.frequency = None

    mock_scheduler_manager = AsyncMock()
    mock_scheduler_manager.get_due_schedulers.return_value = [
        scheduler_without_frequency
    ]
    mock_scheduler_manager_class.return_value = mock_scheduler_manager

    await scheduler_engine.process_due_schedulers()

    scheduler_engine.workflow_executor.execute_workflow_with_fetch.assert_called_once()
    # Should rollback due to missing frequency field
    mock_session.rollback.assert_called()


@pytest.mark.asyncio
@patch("src.core.scheduler_engine.SchedulerManager")
@patch("src.utils.schedule_parser.ScheduleParser.get_next_run_time")
async def test_process_due_schedulers_multiple_schedulers(
    mock_get_next_run_time, mock_scheduler_manager_class, scheduler_engine, mock_session
):
    """Test process_due_schedulers with multiple due schedulers."""
    now = datetime.now(timezone.utc)
    scheduler1 = create_test_scheduler(
        scheduler_id="s1",
        workflow_id="w1",
        frequency="daily",
        next_run_at=now - timedelta(minutes=5),
    )
    scheduler2 = create_test_scheduler(
        scheduler_id="s2",
        workflow_id="w2",
        frequency="hourly",
        next_run_at=now - timedelta(minutes=10),
    )

    mock_scheduler_manager = AsyncMock()
    mock_scheduler_manager.get_due_schedulers.return_value = [scheduler1, scheduler2]
    mock_scheduler_manager_class.return_value = mock_scheduler_manager

    mock_get_next_run_time.side_effect = [
        now + timedelta(hours=24),  # For scheduler1
        now + timedelta(hours=1),  # For scheduler2
    ]

    await scheduler_engine.process_due_schedulers()

    # Both schedulers should succeed now that we have proper validation
    assert (
        scheduler_engine.workflow_executor.execute_workflow_with_fetch.call_count == 2
    )
    assert mock_session.add.call_count == 2
    assert mock_session.commit.call_count == 2
    assert mock_session.refresh.call_count == 2


@pytest.mark.asyncio
@patch("src.core.scheduler_engine.SchedulerManager")
async def test_run_pending_schedules_alias(
    mock_scheduler_manager_class, scheduler_engine, mock_session
):
    """Test that run_pending_schedules is an alias for process_due_schedulers."""
    mock_scheduler_manager = AsyncMock()
    mock_scheduler_manager.get_due_schedulers.return_value = []
    mock_scheduler_manager_class.return_value = mock_scheduler_manager

    await scheduler_engine.run_pending_schedules()

    mock_scheduler_manager_class.assert_called_once_with(mock_session)
    mock_scheduler_manager.get_due_schedulers.assert_called_once()
