"""
Unit tests for the AuthClient class.

This module tests the authentication client functionality,
including credential retrieval, token refresh, and error handling.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any

from src.core.auth_client import AuthClient
from tests.fixtures.http import (
    mock_successful_response,
    mock_error_response,
    mock_auth_error_response,
    mock_auth_service_response
)
from tests.fixtures.mocks import mock_settings, mock_logger


class TestAuthClient:
    """Test cases for AuthClient class."""
    
    @pytest.fixture
    def auth_client(self, mock_settings, mock_logger):
        """Create an AuthClient instance for testing."""
        with patch("src.core.auth_client.get_settings", return_value=mock_settings), \
             patch("src.core.auth_client.get_logger", return_value=mock_logger):
            return AuthClient()
    
    @pytest.fixture
    def sample_credentials(self) -> Dict[str, Any]:
        """Sample credentials for testing."""
        return {
            "access_token": "test-access-token",
            "refresh_token": "test-refresh-token",
            "expires_at": "2024-12-31T23:59:59Z",
            "token_type": "Bearer",
            "scope": "https://www.googleapis.com/auth/calendar.readonly"
        }
    
    @pytest.mark.asyncio
    async def test_get_credentials_success(
        self,
        auth_client,
        sample_credentials,
        mock_auth_service_response
    ):
        """Test successful credential retrieval."""
        # Setup
        user_id = "test-user-123"
        service_name = "google_calendar"
        
        # Mock HTTP response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = mock_auth_service_response
        
        mock_client = AsyncMock()
        mock_client.get = AsyncMock(return_value=mock_response)
        auth_client.client = mock_client
        
        # Execute
        result = await auth_client.get_credentials(user_id, service_name)
        
        # Assertions
        assert result is not None
        assert "access_token" in result
        assert result["access_token"] == "mock-access-token"
        mock_client.get.assert_called_once()
        
        # Verify the request URL and headers
        call_args = mock_client.get.call_args
        assert f"/api/v1/credentials/{user_id}/{service_name}" in call_args[0][0]
        assert "Authorization" in call_args[1]["headers"]
    
    @pytest.mark.asyncio
    async def test_get_credentials_user_not_found(
        self,
        auth_client
    ):
        """Test credential retrieval when user is not found."""
        # Setup
        user_id = "non-existent-user"
        service_name = "google_calendar"
        
        # Mock HTTP response with 404
        mock_response = MagicMock()
        mock_response.status_code = 404
        mock_response.text = "User not found"
        
        mock_client = AsyncMock()
        mock_client.get = AsyncMock(return_value=mock_response)
        auth_client.client = mock_client
        
        # Execute
        result = await auth_client.get_credentials(user_id, service_name)
        
        # Assertions
        assert result is None
        mock_client.get.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_credentials_service_error(
        self,
        auth_client
    ):
        """Test credential retrieval with service error."""
        # Setup
        user_id = "test-user-123"
        service_name = "google_calendar"
        
        # Mock HTTP response with 500
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_response.text = "Internal server error"
        
        mock_client = AsyncMock()
        mock_client.get = AsyncMock(return_value=mock_response)
        auth_client.client = mock_client
        
        # Execute
        result = await auth_client.get_credentials(user_id, service_name)
        
        # Assertions
        assert result is None
        mock_client.get.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_credentials_network_error(
        self,
        auth_client
    ):
        """Test credential retrieval with network error."""
        # Setup
        user_id = "test-user-123"
        service_name = "google_calendar"
        
        # Mock HTTP client with exception
        import httpx
        mock_client = AsyncMock()
        mock_client.get = AsyncMock(side_effect=httpx.ConnectError("Connection failed"))
        auth_client.client = mock_client
        
        # Execute
        result = await auth_client.get_credentials(user_id, service_name)
        
        # Assertions
        assert result is None
        mock_client.get.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_refresh_credentials_success(
        self,
        auth_client,
        sample_credentials
    ):
        """Test successful credential refresh."""
        # Setup
        user_id = "test-user-123"
        service_name = "google_calendar"
        refresh_token = "test-refresh-token"
        
        # Mock HTTP response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "credentials": {
                "access_token": "new-access-token",
                "refresh_token": "test-refresh-token",
                "expires_at": "2024-12-31T23:59:59Z"
            }
        }
        
        mock_client = AsyncMock()
        mock_client.post = AsyncMock(return_value=mock_response)
        auth_client.client = mock_client
        
        # Execute
        result = await auth_client.refresh_credentials(user_id, service_name, refresh_token)
        
        # Assertions
        assert result is not None
        assert result["access_token"] == "new-access-token"
        mock_client.post.assert_called_once()
        
        # Verify the request payload
        call_args = mock_client.post.call_args
        assert "refresh_token" in call_args[1]["json"]
        assert call_args[1]["json"]["refresh_token"] == refresh_token
    
    @pytest.mark.asyncio
    async def test_refresh_credentials_invalid_token(
        self,
        auth_client
    ):
        """Test credential refresh with invalid token."""
        # Setup
        user_id = "test-user-123"
        service_name = "google_calendar"
        refresh_token = "invalid-refresh-token"
        
        # Mock HTTP response with 401
        mock_response = MagicMock()
        mock_response.status_code = 401
        mock_response.text = "Invalid refresh token"
        
        mock_client = AsyncMock()
        mock_client.post = AsyncMock(return_value=mock_response)
        auth_client.client = mock_client
        
        # Execute
        result = await auth_client.refresh_credentials(user_id, service_name, refresh_token)
        
        # Assertions
        assert result is None
        mock_client.post.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_refresh_credentials_service_error(
        self,
        auth_client
    ):
        """Test credential refresh with service error."""
        # Setup
        user_id = "test-user-123"
        service_name = "google_calendar"
        refresh_token = "test-refresh-token"
        
        # Mock HTTP response with 500
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_response.text = "Internal server error"
        
        mock_client = AsyncMock()
        mock_client.post = AsyncMock(return_value=mock_response)
        auth_client.client = mock_client
        
        # Execute
        result = await auth_client.refresh_credentials(user_id, service_name, refresh_token)
        
        # Assertions
        assert result is None
        mock_client.post.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_credentials_invalid_json(
        self,
        auth_client
    ):
        """Test credential retrieval with invalid JSON response."""
        # Setup
        user_id = "test-user-123"
        service_name = "google_calendar"
        
        # Mock HTTP response with invalid JSON
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.side_effect = ValueError("Invalid JSON")
        
        mock_client = AsyncMock()
        mock_client.get = AsyncMock(return_value=mock_response)
        auth_client.client = mock_client
        
        # Execute
        result = await auth_client.get_credentials(user_id, service_name)
        
        # Assertions
        assert result is None
        mock_client.get.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_credentials_timeout(
        self,
        auth_client
    ):
        """Test credential retrieval with timeout."""
        # Setup
        user_id = "test-user-123"
        service_name = "google_calendar"
        
        # Mock HTTP client with timeout
        import httpx
        mock_client = AsyncMock()
        mock_client.get = AsyncMock(side_effect=httpx.TimeoutException("Request timed out"))
        auth_client.client = mock_client
        
        # Execute
        result = await auth_client.get_credentials(user_id, service_name)
        
        # Assertions
        assert result is None
        mock_client.get.assert_called_once()
    
    def test_auth_client_initialization(self, mock_settings, mock_logger):
        """Test AuthClient initialization."""
        with patch("src.core.auth_client.get_settings", return_value=mock_settings), \
             patch("src.core.auth_client.get_logger", return_value=mock_logger):
            
            client = AuthClient()
            
            # Assertions
            assert client.settings == mock_settings
            assert client.client is not None
    
    @pytest.mark.asyncio
    async def test_get_credentials_missing_service_data(
        self,
        auth_client
    ):
        """Test credential retrieval when service data is missing."""
        # Setup
        user_id = "test-user-123"
        service_name = "google_calendar"
        
        # Mock HTTP response with missing service data
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "credentials": {
                "other_service": {"access_token": "token"}
            }
        }
        
        mock_client = AsyncMock()
        mock_client.get = AsyncMock(return_value=mock_response)
        auth_client.client = mock_client
        
        # Execute
        result = await auth_client.get_credentials(user_id, service_name)
        
        # Assertions
        assert result is None
        mock_client.get.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_refresh_credentials_missing_response_data(
        self,
        auth_client
    ):
        """Test credential refresh when response data is missing."""
        # Setup
        user_id = "test-user-123"
        service_name = "google_calendar"
        refresh_token = "test-refresh-token"
        
        # Mock HTTP response with missing credentials
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"status": "success"}  # No credentials
        
        mock_client = AsyncMock()
        mock_client.post = AsyncMock(return_value=mock_response)
        auth_client.client = mock_client
        
        # Execute
        result = await auth_client.refresh_credentials(user_id, service_name, refresh_token)
        
        # Assertions
        assert result is None
        mock_client.post.assert_called_once()
