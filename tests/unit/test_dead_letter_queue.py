"""
Unit tests for the DeadLetterQueue class.

This module tests the dead letter queue functionality,
including failed execution management, retry operations, and cleanup.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4
from datetime import datetime, timedelta

from src.core.dead_letter_queue import DeadLetterQueue
from src.database.models import TriggerExecution
from tests.fixtures.database import sample_trigger, failed_executions
from tests.fixtures.mocks import (
    mock_workflow_executor,
    mock_settings,
    mock_logger,
    mock_async_session
)


class TestDeadLetterQueue:
    """Test cases for DeadLetterQueue class."""
    
    @pytest.fixture
    def dlq(self, mock_settings, mock_logger, mock_workflow_executor):
        """Create a DeadLetterQueue instance for testing."""
        with patch("src.core.dead_letter_queue.get_settings", return_value=mock_settings), \
             patch("src.core.dead_letter_queue.get_logger", return_value=mock_logger):
            
            dlq = DeadLetterQueue()
            dlq.workflow_executor = mock_workflow_executor
            return dlq
    
    @pytest.mark.asyncio
    async def test_add_failed_execution_success(self, dlq, mock_async_session):
        """Test successfully adding a failed execution to DLQ."""
        # Setup
        execution_id = uuid4()
        final_error = "Permanent failure after 5 retries"
        context = {"retry_count": 5, "last_error": "Connection timeout"}
        
        # Mock database operations
        mock_result = MagicMock()
        mock_result.rowcount = 1
        mock_async_session.execute = AsyncMock(return_value=mock_result)
        mock_async_session.commit = AsyncMock()
        
        with patch("src.core.dead_letter_queue.get_async_session") as mock_get_session:
            mock_get_session.return_value.__aenter__.return_value = mock_async_session
            
            result = await dlq.add_failed_execution(execution_id, final_error, context)
        
        # Assertions
        assert result is True
        mock_async_session.execute.assert_called_once()
        mock_async_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_add_failed_execution_not_found(self, dlq, mock_async_session):
        """Test adding failed execution when execution doesn't exist."""
        # Setup
        execution_id = uuid4()
        final_error = "Permanent failure"
        
        # Mock database operations - no rows affected
        mock_result = MagicMock()
        mock_result.rowcount = 0
        mock_async_session.execute = AsyncMock(return_value=mock_result)
        
        with patch("src.core.dead_letter_queue.get_async_session") as mock_get_session:
            mock_get_session.return_value.__aenter__.return_value = mock_async_session
            
            result = await dlq.add_failed_execution(execution_id, final_error)
        
        # Assertions
        assert result is False
        mock_async_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_add_failed_execution_database_error(self, dlq, mock_async_session):
        """Test adding failed execution with database error."""
        # Setup
        execution_id = uuid4()
        final_error = "Permanent failure"
        
        # Mock database operations with exception
        mock_async_session.execute = AsyncMock(side_effect=Exception("Database error"))
        
        with patch("src.core.dead_letter_queue.get_async_session") as mock_get_session:
            mock_get_session.return_value.__aenter__.return_value = mock_async_session
            
            result = await dlq.add_failed_execution(execution_id, final_error)
        
        # Assertions
        assert result is False
        mock_async_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_failed_executions_success(self, dlq, failed_executions, mock_async_session):
        """Test retrieving failed executions from DLQ."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = failed_executions
        mock_async_session.execute = AsyncMock(return_value=mock_result)
        
        with patch("src.core.dead_letter_queue.get_async_session") as mock_get_session:
            mock_get_session.return_value.__aenter__.return_value = mock_async_session
            
            result = await dlq.get_failed_executions(limit=10, offset=0)
        
        # Assertions
        assert result == failed_executions
        mock_async_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_failed_executions_with_filters(self, dlq, mock_async_session):
        """Test retrieving failed executions with filters."""
        # Setup
        trigger_id = uuid4()
        user_id = "test-user-123"
        since = datetime.utcnow() - timedelta(days=1)
        
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = []
        mock_async_session.execute = AsyncMock(return_value=mock_result)
        
        with patch("src.core.dead_letter_queue.get_async_session") as mock_get_session:
            mock_get_session.return_value.__aenter__.return_value = mock_async_session
            
            result = await dlq.get_failed_executions(
                limit=50,
                offset=10,
                trigger_id=trigger_id,
                user_id=user_id,
                since=since
            )
        
        # Assertions
        assert result == []
        mock_async_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_failed_executions_database_error(self, dlq, mock_async_session):
        """Test retrieving failed executions with database error."""
        # Setup
        mock_async_session.execute = AsyncMock(side_effect=Exception("Database error"))
        
        with patch("src.core.dead_letter_queue.get_async_session") as mock_get_session:
            mock_get_session.return_value.__aenter__.return_value = mock_async_session
            
            result = await dlq.get_failed_executions()
        
        # Assertions
        assert result == []
        mock_async_session.execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_retry_execution_success(self, dlq, sample_trigger, mock_async_session):
        """Test successful manual retry of failed execution."""
        # Setup
        execution_id = uuid4()
        
        # Create mock execution and trigger
        mock_execution = MagicMock()
        mock_execution.id = execution_id
        mock_execution.status = "failed"
        mock_execution.retry_count = 3
        mock_execution.is_retryable = True
        mock_execution.event_data = {"event_type": "created", "event_id": "test-123"}
        
        mock_result = MagicMock()
        mock_result.first.return_value = (mock_execution, sample_trigger)
        mock_async_session.execute = AsyncMock(return_value=mock_result)
        mock_async_session.commit = AsyncMock()
        
        # Mock successful workflow execution
        dlq.workflow_executor.execute_workflow = AsyncMock(return_value="correlation-123")
        
        with patch("src.core.dead_letter_queue.get_async_session") as mock_get_session:
            mock_get_session.return_value.__aenter__.return_value = mock_async_session
            
            success, result = await dlq.retry_execution(execution_id)
        
        # Assertions
        assert success is True
        assert result == "correlation-123"
        assert mock_execution.status == "success"
        assert mock_execution.retry_count == 4
        dlq.workflow_executor.execute_workflow.assert_called_once()
        mock_async_session.commit.assert_called()
    
    @pytest.mark.asyncio
    async def test_retry_execution_not_found(self, dlq, mock_async_session):
        """Test retry when execution is not found."""
        # Setup
        execution_id = uuid4()
        
        mock_result = MagicMock()
        mock_result.first.return_value = None
        mock_async_session.execute = AsyncMock(return_value=mock_result)
        
        with patch("src.core.dead_letter_queue.get_async_session") as mock_get_session:
            mock_get_session.return_value.__aenter__.return_value = mock_async_session
            
            success, result = await dlq.retry_execution(execution_id)
        
        # Assertions
        assert success is False
        assert result == "Execution not found"
    
    @pytest.mark.asyncio
    async def test_retry_execution_not_retryable(self, dlq, sample_trigger, mock_async_session):
        """Test retry when execution is not retryable."""
        # Setup
        execution_id = uuid4()
        
        # Create mock execution that's not retryable
        mock_execution = MagicMock()
        mock_execution.is_retryable = False
        
        mock_result = MagicMock()
        mock_result.first.return_value = (mock_execution, sample_trigger)
        mock_async_session.execute = AsyncMock(return_value=mock_result)
        
        with patch("src.core.dead_letter_queue.get_async_session") as mock_get_session:
            mock_get_session.return_value.__aenter__.return_value = mock_async_session
            
            success, result = await dlq.retry_execution(execution_id, force=False)
        
        # Assertions
        assert success is False
        assert result == "Execution is not retryable"
    
    @pytest.mark.asyncio
    async def test_retry_execution_force_retry(self, dlq, sample_trigger, mock_async_session):
        """Test force retry of non-retryable execution."""
        # Setup
        execution_id = uuid4()
        
        # Create mock execution that's not retryable
        mock_execution = MagicMock()
        mock_execution.is_retryable = False
        mock_execution.retry_count = 5
        mock_execution.event_data = {"event_type": "created"}
        
        mock_result = MagicMock()
        mock_result.first.return_value = (mock_execution, sample_trigger)
        mock_async_session.execute = AsyncMock(return_value=mock_result)
        mock_async_session.commit = AsyncMock()
        
        # Mock successful workflow execution
        dlq.workflow_executor.execute_workflow = AsyncMock(return_value="correlation-456")
        
        with patch("src.core.dead_letter_queue.get_async_session") as mock_get_session:
            mock_get_session.return_value.__aenter__.return_value = mock_async_session
            
            success, result = await dlq.retry_execution(execution_id, force=True)
        
        # Assertions
        assert success is True
        assert result == "correlation-456"
        dlq.workflow_executor.execute_workflow.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_retry_execution_workflow_failure(self, dlq, sample_trigger, mock_async_session):
        """Test retry when workflow execution fails."""
        # Setup
        execution_id = uuid4()
        
        mock_execution = MagicMock()
        mock_execution.is_retryable = True
        mock_execution.retry_count = 2
        mock_execution.event_data = {"event_type": "created"}
        
        mock_result = MagicMock()
        mock_result.first.return_value = (mock_execution, sample_trigger)
        mock_async_session.execute = AsyncMock(return_value=mock_result)
        mock_async_session.commit = AsyncMock()
        
        # Mock failed workflow execution
        dlq.workflow_executor.execute_workflow = AsyncMock(return_value=None)
        
        with patch("src.core.dead_letter_queue.get_async_session") as mock_get_session:
            mock_get_session.return_value.__aenter__.return_value = mock_async_session
            
            success, result = await dlq.retry_execution(execution_id)
        
        # Assertions
        assert success is False
        assert "Manual retry failed" in result
        assert mock_execution.status == "failed"
        dlq.workflow_executor.execute_workflow.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_retry_batch_success(self, dlq):
        """Test successful batch retry operation."""
        # Setup
        execution_ids = [uuid4() for _ in range(3)]
        
        # Mock individual retry results
        dlq.retry_execution = AsyncMock()
        dlq.retry_execution.side_effect = [
            (True, "correlation-1"),
            (True, "correlation-2"),
            (False, "Retry failed")
        ]
        
        result = await dlq.retry_batch(execution_ids)
        
        # Assertions
        assert result["total"] == 3
        assert result["successful"] == 2
        assert result["failed"] == 1
        assert len(result["details"]) == 3
        assert dlq.retry_execution.call_count == 3
    
    @pytest.mark.asyncio
    async def test_retry_batch_with_exceptions(self, dlq):
        """Test batch retry with exceptions."""
        # Setup
        execution_ids = [uuid4() for _ in range(2)]
        
        # Mock individual retry with exception
        dlq.retry_execution = AsyncMock()
        dlq.retry_execution.side_effect = [
            (True, "correlation-1"),
            Exception("Unexpected error")
        ]
        
        result = await dlq.retry_batch(execution_ids)
        
        # Assertions
        assert result["total"] == 2
        assert result["successful"] == 1
        assert result["failed"] == 1
        assert "Exception: Unexpected error" in str(result["details"])
    
    @pytest.mark.asyncio
    async def test_get_failure_statistics_success(self, dlq, mock_async_session):
        """Test getting failure statistics."""
        # Setup mock database results
        mock_async_session.execute = AsyncMock()
        
        # Mock total failed count
        total_result = MagicMock()
        total_result.scalar.return_value = 25
        
        # Mock failures by type
        type_result = MagicMock()
        type_result.__iter__ = lambda self: iter([
            MagicMock(trigger_type="google_calendar", count=20),
            MagicMock(trigger_type="slack", count=5)
        ])
        
        # Mock recent failures
        recent_result = MagicMock()
        recent_result.scalar.return_value = 3
        
        # Mock common errors
        error_result = MagicMock()
        error_result.__iter__ = lambda self: iter([
            MagicMock(error_message="Connection timeout", count=10),
            MagicMock(error_message="Authentication failed", count=8)
        ])
        
        mock_async_session.execute.side_effect = [
            total_result,
            type_result,
            recent_result,
            error_result
        ]
        
        with patch("src.core.dead_letter_queue.get_async_session") as mock_get_session:
            mock_get_session.return_value.__aenter__.return_value = mock_async_session
            
            result = await dlq.get_failure_statistics()
        
        # Assertions
        assert result["total_failed_executions"] == 25
        assert result["recent_failures_24h"] == 3
        assert "google_calendar" in result["failures_by_trigger_type"]
        assert len(result["common_error_patterns"]) == 2
        assert "generated_at" in result
