"""
Unit tests for trigger API routes.

This module tests the trigger management endpoints,
including CRUD operations, validation, and error handling.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4
from fastapi.testclient import TestClient

from src.main import app
from src.schemas.trigger import Tri<PERSON><PERSON><PERSON>, TriggerUpdate, TriggerResponse
from tests.fixtures.database import sample_trigger_data, sample_trigger
from tests.fixtures.http import auth_headers, invalid_auth_headers
from tests.fixtures.mocks import mock_trigger_manager


class TestTriggerRoutes:
    """Test cases for trigger API routes."""
    
    @pytest.fixture
    def client(self):
        """Create a test client."""
        return TestClient(app)
    
    @pytest.fixture
    def mock_dependency_override(self, mock_trigger_manager):
        """Override dependencies for testing."""
        from src.api.routes.triggers import get_trigger_manager
        
        app.dependency_overrides[get_trigger_manager] = lambda: mock_trigger_manager
        yield mock_trigger_manager
        app.dependency_overrides.clear()
    
    def test_create_trigger_success(
        self,
        client,
        mock_dependency_override,
        sample_trigger_data,
        auth_headers
    ):
        """Test successful trigger creation."""
        # Setup
        trigger_create_data = sample_trigger_data.copy()
        mock_trigger = MagicMock()
        mock_trigger.id = uuid4()
        mock_trigger.user_id = trigger_create_data["user_id"]
        mock_trigger.workflow_id = trigger_create_data["workflow_id"]
        mock_trigger.trigger_type = trigger_create_data["trigger_type"]
        mock_trigger.trigger_name = trigger_create_data["trigger_name"]
        mock_trigger.is_active = trigger_create_data["is_active"]
        
        mock_dependency_override.create_trigger = AsyncMock(return_value=mock_trigger)
        
        # Execute
        response = client.post(
            "/api/v1/triggers",
            json=trigger_create_data,
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 201
        data = response.json()
        assert data["user_id"] == trigger_create_data["user_id"]
        assert data["workflow_id"] == trigger_create_data["workflow_id"]
        assert data["trigger_type"] == trigger_create_data["trigger_type"]
        mock_dependency_override.create_trigger.assert_called_once()
    
    def test_create_trigger_validation_error(self, client, auth_headers):
        """Test trigger creation with validation error."""
        # Setup invalid data (missing required fields)
        invalid_data = {
            "trigger_name": "Test Trigger"
            # Missing required fields
        }
        
        # Execute
        response = client.post(
            "/api/v1/triggers",
            json=invalid_data,
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 422
        data = response.json()
        assert "detail" in data
    
    def test_create_trigger_unauthorized(self, client, sample_trigger_data):
        """Test trigger creation without authentication."""
        # Execute
        response = client.post(
            "/api/v1/triggers",
            json=sample_trigger_data
            # No auth headers
        )
        
        # Assertions
        assert response.status_code == 401
    
    def test_create_trigger_adapter_failure(
        self,
        client,
        mock_dependency_override,
        sample_trigger_data,
        auth_headers
    ):
        """Test trigger creation when adapter setup fails."""
        # Setup
        mock_dependency_override.create_trigger = AsyncMock(return_value=None)
        
        # Execute
        response = client.post(
            "/api/v1/triggers",
            json=sample_trigger_data,
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 400
        data = response.json()
        assert "Failed to create trigger" in data["detail"]
    
    def test_get_trigger_success(
        self,
        client,
        mock_dependency_override,
        sample_trigger,
        auth_headers
    ):
        """Test successful trigger retrieval."""
        # Setup
        trigger_id = str(sample_trigger.id)
        mock_dependency_override.get_trigger = AsyncMock(return_value=sample_trigger)
        
        # Execute
        response = client.get(
            f"/api/v1/triggers/{trigger_id}",
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == trigger_id
        mock_dependency_override.get_trigger.assert_called_once()
    
    def test_get_trigger_not_found(
        self,
        client,
        mock_dependency_override,
        auth_headers
    ):
        """Test trigger retrieval when trigger doesn't exist."""
        # Setup
        trigger_id = str(uuid4())
        mock_dependency_override.get_trigger = AsyncMock(return_value=None)
        
        # Execute
        response = client.get(
            f"/api/v1/triggers/{trigger_id}",
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 404
        data = response.json()
        assert "Trigger not found" in data["detail"]
    
    def test_get_trigger_invalid_uuid(self, client, auth_headers):
        """Test trigger retrieval with invalid UUID."""
        # Execute
        response = client.get(
            "/api/v1/triggers/invalid-uuid",
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 422
    
    def test_list_triggers_success(
        self,
        client,
        mock_dependency_override,
        auth_headers
    ):
        """Test successful trigger listing."""
        # Setup
        mock_triggers = [MagicMock() for _ in range(3)]
        for i, trigger in enumerate(mock_triggers):
            trigger.id = uuid4()
            trigger.trigger_name = f"Trigger {i+1}"
        
        mock_dependency_override.list_triggers = AsyncMock(return_value=mock_triggers)
        
        # Execute
        response = client.get(
            "/api/v1/triggers",
            headers=auth_headers,
            params={
                "user_id": "test-user-123",
                "trigger_type": "google_calendar",
                "is_active": True,
                "limit": 10,
                "offset": 0
            }
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 3
        mock_dependency_override.list_triggers.assert_called_once()
    
    def test_list_triggers_empty_result(
        self,
        client,
        mock_dependency_override,
        auth_headers
    ):
        """Test trigger listing with empty result."""
        # Setup
        mock_dependency_override.list_triggers = AsyncMock(return_value=[])
        
        # Execute
        response = client.get(
            "/api/v1/triggers",
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data == []
    
    def test_update_trigger_success(
        self,
        client,
        mock_dependency_override,
        sample_trigger,
        auth_headers
    ):
        """Test successful trigger update."""
        # Setup
        trigger_id = str(sample_trigger.id)
        update_data = {
            "trigger_name": "Updated Trigger Name",
            "is_active": False
        }
        
        # Update the mock trigger
        updated_trigger = sample_trigger
        updated_trigger.trigger_name = update_data["trigger_name"]
        updated_trigger.is_active = update_data["is_active"]
        
        mock_dependency_override.update_trigger = AsyncMock(return_value=updated_trigger)
        
        # Execute
        response = client.put(
            f"/api/v1/triggers/{trigger_id}",
            json=update_data,
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["trigger_name"] == update_data["trigger_name"]
        assert data["is_active"] == update_data["is_active"]
        mock_dependency_override.update_trigger.assert_called_once()
    
    def test_update_trigger_not_found(
        self,
        client,
        mock_dependency_override,
        auth_headers
    ):
        """Test trigger update when trigger doesn't exist."""
        # Setup
        trigger_id = str(uuid4())
        update_data = {"trigger_name": "Updated Name"}
        
        mock_dependency_override.update_trigger = AsyncMock(return_value=None)
        
        # Execute
        response = client.put(
            f"/api/v1/triggers/{trigger_id}",
            json=update_data,
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 404
        data = response.json()
        assert "Trigger not found" in data["detail"]
    
    def test_delete_trigger_success(
        self,
        client,
        mock_dependency_override,
        auth_headers
    ):
        """Test successful trigger deletion."""
        # Setup
        trigger_id = str(uuid4())
        mock_dependency_override.delete_trigger = AsyncMock(return_value=True)
        
        # Execute
        response = client.delete(
            f"/api/v1/triggers/{trigger_id}",
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 204
        mock_dependency_override.delete_trigger.assert_called_once()
    
    def test_delete_trigger_not_found(
        self,
        client,
        mock_dependency_override,
        auth_headers
    ):
        """Test trigger deletion when trigger doesn't exist."""
        # Setup
        trigger_id = str(uuid4())
        mock_dependency_override.delete_trigger = AsyncMock(return_value=False)
        
        # Execute
        response = client.delete(
            f"/api/v1/triggers/{trigger_id}",
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 404
        data = response.json()
        assert "Trigger not found" in data["detail"]
    
    def test_toggle_trigger_success(
        self,
        client,
        mock_dependency_override,
        sample_trigger,
        auth_headers
    ):
        """Test successful trigger toggle."""
        # Setup
        trigger_id = str(sample_trigger.id)
        
        # Mock the trigger with toggled state
        toggled_trigger = sample_trigger
        toggled_trigger.is_active = not sample_trigger.is_active
        
        mock_dependency_override.get_trigger = AsyncMock(return_value=sample_trigger)
        mock_dependency_override.update_trigger = AsyncMock(return_value=toggled_trigger)
        
        # Execute
        response = client.post(
            f"/api/v1/triggers/{trigger_id}/toggle",
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["is_active"] == toggled_trigger.is_active
        mock_dependency_override.get_trigger.assert_called_once()
        mock_dependency_override.update_trigger.assert_called_once()
    
    def test_get_trigger_executions_success(
        self,
        client,
        mock_dependency_override,
        auth_headers
    ):
        """Test getting trigger execution history."""
        # Setup
        trigger_id = str(uuid4())
        mock_executions = [MagicMock() for _ in range(5)]
        
        mock_dependency_override.get_trigger_executions = AsyncMock(return_value=mock_executions)
        
        # Execute
        response = client.get(
            f"/api/v1/triggers/{trigger_id}/executions",
            headers=auth_headers,
            params={"limit": 10, "offset": 0}
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 5
        mock_dependency_override.get_trigger_executions.assert_called_once()
    
    def test_get_trigger_statistics_success(
        self,
        client,
        mock_dependency_override,
        auth_headers
    ):
        """Test getting trigger statistics."""
        # Setup
        mock_stats = {
            "total_triggers": 10,
            "active_triggers": 8,
            "total_executions": 150,
            "successful_executions": 140,
            "failed_executions": 10
        }
        
        mock_dependency_override.get_statistics = AsyncMock(return_value=mock_stats)
        
        # Execute
        response = client.get(
            "/api/v1/triggers/stats",
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["total_triggers"] == 10
        assert data["active_triggers"] == 8
        mock_dependency_override.get_statistics.assert_called_once()
