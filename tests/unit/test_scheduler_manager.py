import pytest
from unittest.mock import MagicMock, AsyncMock
from datetime import datetime, timedelta, timezone
from sqlalchemy.ext.asyncio import AsyncSession
from src.core.scheduler_manager import SchedulerManager
from src.database.models import Scheduler
from src.schemas.scheduler import (
    SimplifiedSchedulerCreate,
    SimplifiedSchedulerUpdate,
    SimplifiedSchedulerResponse,
    ScheduleFrequency,
)


@pytest.fixture
def mock_session():
    session = AsyncMock(spec=AsyncSession)
    session.add = AsyncMock()
    session.commit = AsyncMock()
    session.refresh = AsyncMock()
    session.delete = AsyncMock()
    session.execute = AsyncMock()
    session.rollback = AsyncMock()
    return session


@pytest.fixture
def scheduler_manager(mock_session):
    return SchedulerManager(db=mock_session)


@pytest.mark.asyncio
async def test_create_scheduler(scheduler_manager, mock_session):
    scheduler_data = SimplifiedSchedulerCreate(
        name="Test Daily Scheduler",
        workflow_id="workflow123",
        frequency=ScheduleFrequency.DAILY,
        time="10:30",
        timezone="UTC",
        is_active=True,
    )

    # Mock the database scheduler object that would be created
    mock_db_scheduler = Scheduler(
        id="test-id-123",
        user_id="user123",
        name="Test Daily Scheduler",
        workflow_id="workflow123",
        frequency="daily",
        time="10:30",
        timezone="UTC",
        is_active=True,
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc),
        next_run_at=datetime.now(timezone.utc) + timedelta(days=1),
    )

    # Mock the refresh to populate the created scheduler
    async def mock_refresh(obj):
        for attr, value in mock_db_scheduler.__dict__.items():
            if not attr.startswith("_"):
                setattr(obj, attr, value)

    mock_session.refresh.side_effect = mock_refresh

    created_scheduler = await scheduler_manager.create(scheduler_data, "user123")

    assert created_scheduler.workflow_id == "workflow123"
    assert created_scheduler.frequency == ScheduleFrequency.DAILY
    assert created_scheduler.time == "10:30"
    assert created_scheduler.name == "Test Daily Scheduler"
    mock_session.add.assert_called_once()
    mock_session.commit.assert_called_once()
    mock_session.refresh.assert_called_once()


@pytest.mark.asyncio
async def test_get_scheduler(scheduler_manager, mock_session):
    mock_scheduler = Scheduler(
        id="scheduler123",
        user_id="user123",
        name="Test Scheduler",
        workflow_id="workflow123",
        frequency="daily",
        time="10:30",
        timezone="UTC",
        is_active=True,
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc),
    )
    # Mock the execute result properly
    mock_result = MagicMock()
    mock_result.scalars.return_value.first.return_value = mock_scheduler
    mock_session.execute.return_value = mock_result

    retrieved_scheduler = await scheduler_manager.get("scheduler123", "user123")

    assert retrieved_scheduler.id == "scheduler123"
    assert retrieved_scheduler.workflow_id == "workflow123"
    assert retrieved_scheduler.frequency == ScheduleFrequency.DAILY
    mock_session.execute.assert_called_once()


@pytest.mark.asyncio
async def test_get_scheduler_not_found(scheduler_manager, mock_session):
    # Mock the execute result properly
    mock_result = MagicMock()
    mock_result.scalars.return_value.first.return_value = None
    mock_session.execute.return_value = mock_result

    retrieved_scheduler = await scheduler_manager.get("nonexistent", "user123")

    assert retrieved_scheduler is None


@pytest.mark.asyncio
async def test_update_scheduler(scheduler_manager, mock_session):
    existing_scheduler = Scheduler(
        id="scheduler123",
        user_id="user123",
        name="Test Scheduler",
        workflow_id="workflow123",
        frequency="daily",
        time="10:30",
        timezone="UTC",
        is_active=True,
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc),
    )
    # Mock the execute result properly
    mock_result = MagicMock()
    mock_result.scalars.return_value.first.return_value = existing_scheduler
    mock_session.execute.return_value = mock_result

    update_data = SimplifiedSchedulerUpdate(is_active=False)

    updated_scheduler = await scheduler_manager.update(
        "scheduler123", update_data, "user123"
    )

    assert updated_scheduler.is_active == False
    mock_session.commit.assert_called_once()
    mock_session.refresh.assert_called_once_with(existing_scheduler)


@pytest.mark.asyncio
async def test_update_scheduler_not_found(scheduler_manager, mock_session):
    # Mock the execute result properly
    mock_result = MagicMock()
    mock_result.scalars.return_value.first.return_value = None
    mock_session.execute.return_value = mock_result

    update_data = SimplifiedSchedulerUpdate(is_active=False)

    updated_scheduler = await scheduler_manager.update(
        "nonexistent", update_data, "user123"
    )

    assert updated_scheduler is None
    mock_session.commit.assert_not_called()


@pytest.mark.asyncio
async def test_delete_scheduler(scheduler_manager, mock_session):
    existing_scheduler = Scheduler(
        id="scheduler123",
        user_id="user123",
        name="Test Scheduler",
        workflow_id="workflow123",
        frequency="daily",
        time="10:30",
        timezone="UTC",
        is_active=True,
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc),
    )
    # Mock the execute result properly
    mock_result = MagicMock()
    mock_result.scalars.return_value.first.return_value = existing_scheduler
    mock_session.execute.return_value = mock_result

    result = await scheduler_manager.delete("scheduler123", "user123")

    assert result == True
    mock_session.delete.assert_called_once_with(existing_scheduler)
    mock_session.commit.assert_called_once()


@pytest.mark.asyncio
async def test_delete_scheduler_not_found(scheduler_manager, mock_session):
    # Mock the execute result properly
    mock_result = MagicMock()
    mock_result.scalars.return_value.first.return_value = None
    mock_session.execute.return_value = mock_result

    result = await scheduler_manager.delete("nonexistent", "user123")

    assert result == False
    mock_session.delete.assert_not_called()
    mock_session.commit.assert_not_called()


@pytest.mark.asyncio
async def test_list_schedulers(scheduler_manager, mock_session):
    mock_schedulers = [
        Scheduler(
            id="s1",
            user_id="user123",
            name="Scheduler 1",
            workflow_id="w1",
            frequency="daily",
            time="10:30",
            timezone="UTC",
            is_active=True,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
        ),
        Scheduler(
            id="s2",
            user_id="user123",
            name="Scheduler 2",
            workflow_id="w2",
            frequency="weekly",
            time="14:00",
            days_of_week=["Monday"],
            timezone="UTC",
            is_active=True,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
        ),
    ]
    # Mock the execute result properly
    mock_result = MagicMock()
    mock_result.scalars.return_value.all.return_value = mock_schedulers
    mock_session.execute.return_value = mock_result

    schedulers = await scheduler_manager.list("user123")

    assert len(schedulers) == 2
    assert schedulers[0].id == "s1"
    assert schedulers[1].id == "s2"
    assert schedulers[0].frequency == ScheduleFrequency.DAILY
    assert schedulers[1].frequency == ScheduleFrequency.WEEKLY
    mock_session.execute.assert_called_once()


@pytest.mark.asyncio
async def test_get_due_schedulers(scheduler_manager, mock_session):
    now = datetime.now(timezone.utc)
    mock_schedulers = [
        Scheduler(
            id="s1",
            user_id="user123",
            name="Due Scheduler",
            workflow_id="w1",
            frequency="daily",
            time="10:30",
            timezone="UTC",
            is_active=True,
            next_run_at=now - timedelta(minutes=5),  # Due 5 minutes ago
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
        )
    ]
    # Mock the execute result properly
    mock_result = MagicMock()
    mock_result.scalars.return_value.all.return_value = mock_schedulers
    mock_session.execute.return_value = mock_result

    due_schedulers = await scheduler_manager.get_due_schedulers()

    assert len(due_schedulers) == 1
    assert due_schedulers[0].id == "s1"
    assert due_schedulers[0].frequency == ScheduleFrequency.DAILY
    mock_session.execute.assert_called_once()
