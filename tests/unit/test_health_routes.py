"""
Unit tests for health API routes.

This module tests the health monitoring endpoints,
including basic health checks, detailed health, and metrics.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi.testclient import TestClient

from src.main import app
from tests.fixtures.http import auth_headers
from tests.fixtures.mocks import mock_trigger_manager, mock_dead_letter_queue


class TestHealthRoutes:
    """Test cases for health API routes."""
    
    @pytest.fixture
    def client(self):
        """Create a test client."""
        return TestClient(app)
    
    @pytest.fixture
    def mock_dependency_override(self, mock_trigger_manager, mock_dead_letter_queue):
        """Override dependencies for testing."""
        from src.api.routes.health import get_trigger_manager, get_dead_letter_queue
        
        app.dependency_overrides[get_trigger_manager] = lambda: mock_trigger_manager
        app.dependency_overrides[get_dead_letter_queue] = lambda: mock_dead_letter_queue
        yield mock_trigger_manager, mock_dead_letter_queue
        app.dependency_overrides.clear()
    
    def test_basic_health_check_success(self, client):
        """Test basic health check endpoint."""
        # Execute
        response = client.get("/api/v1/health")
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "version" in data
    
    def test_detailed_health_check_success(
        self,
        client,
        mock_dependency_override,
        auth_headers
    ):
        """Test detailed health check with all dependencies."""
        # Setup
        mock_trigger_manager, mock_dead_letter_queue = mock_dependency_override
        
        # Mock adapter health
        mock_trigger_manager.get_adapter_health = AsyncMock(return_value={
            "google_calendar": {
                "status": "healthy",
                "external_service_status": "available",
                "last_check": "2024-01-15T10:00:00Z"
            }
        })
        
        # Mock DLQ health
        mock_dead_letter_queue.get_dlq_health = AsyncMock(return_value={
            "status": "healthy",
            "current_failed_executions": 0,
            "recent_failures_1h": 0
        })
        
        # Mock database health
        with patch("src.api.routes.health.check_database_health") as mock_db_health:
            mock_db_health.return_value = {
                "status": "healthy",
                "connection_pool": "available",
                "response_time_ms": 5
            }
            
            # Execute
            response = client.get(
                "/api/v1/health/detailed",
                headers=auth_headers
            )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "database" in data["dependencies"]
        assert "adapters" in data["dependencies"]
        assert "dead_letter_queue" in data["dependencies"]
        assert data["dependencies"]["database"]["status"] == "healthy"
        assert data["dependencies"]["adapters"]["google_calendar"]["status"] == "healthy"
        assert data["dependencies"]["dead_letter_queue"]["status"] == "healthy"
    
    def test_detailed_health_check_with_failures(
        self,
        client,
        mock_dependency_override,
        auth_headers
    ):
        """Test detailed health check with some failures."""
        # Setup
        mock_trigger_manager, mock_dead_letter_queue = mock_dependency_override
        
        # Mock adapter health with failure
        mock_trigger_manager.get_adapter_health = AsyncMock(return_value={
            "google_calendar": {
                "status": "error",
                "external_service_status": "unavailable",
                "error": "API quota exceeded",
                "last_check": "2024-01-15T10:00:00Z"
            }
        })
        
        # Mock DLQ health with warnings
        mock_dead_letter_queue.get_dlq_health = AsyncMock(return_value={
            "status": "warning",
            "current_failed_executions": 50,
            "recent_failures_1h": 5
        })
        
        # Mock database health as healthy
        with patch("src.api.routes.health.check_database_health") as mock_db_health:
            mock_db_health.return_value = {
                "status": "healthy",
                "connection_pool": "available",
                "response_time_ms": 8
            }
            
            # Execute
            response = client.get(
                "/api/v1/health/detailed",
                headers=auth_headers
            )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "degraded"  # Overall status should be degraded
        assert data["dependencies"]["adapters"]["google_calendar"]["status"] == "error"
        assert data["dependencies"]["dead_letter_queue"]["status"] == "warning"
        assert data["dependencies"]["database"]["status"] == "healthy"
    
    def test_detailed_health_check_unauthorized(self, client):
        """Test detailed health check without authentication."""
        # Execute
        response = client.get("/api/v1/health/detailed")
        
        # Assertions
        assert response.status_code == 401
    
    def test_adapter_health_check_success(
        self,
        client,
        mock_dependency_override,
        auth_headers
    ):
        """Test adapter-specific health check."""
        # Setup
        mock_trigger_manager, _ = mock_dependency_override
        
        mock_trigger_manager.get_adapter_health = AsyncMock(return_value={
            "google_calendar": {
                "status": "healthy",
                "external_service_status": "available",
                "active_triggers": 15,
                "webhook_subscriptions": 12,
                "last_event_processed": "2024-01-15T09:45:00Z",
                "api_quota_remaining": 8500
            }
        })
        
        # Execute
        response = client.get(
            "/api/v1/health/adapters",
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert "google_calendar" in data
        assert data["google_calendar"]["status"] == "healthy"
        assert data["google_calendar"]["active_triggers"] == 15
        assert data["google_calendar"]["webhook_subscriptions"] == 12
    
    def test_adapter_health_check_with_errors(
        self,
        client,
        mock_dependency_override,
        auth_headers
    ):
        """Test adapter health check with errors."""
        # Setup
        mock_trigger_manager, _ = mock_dependency_override
        
        mock_trigger_manager.get_adapter_health = AsyncMock(return_value={
            "google_calendar": {
                "status": "error",
                "external_service_status": "unavailable",
                "error": "Authentication failed",
                "last_successful_check": "2024-01-15T08:00:00Z",
                "consecutive_failures": 3
            }
        })
        
        # Execute
        response = client.get(
            "/api/v1/health/adapters",
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["google_calendar"]["status"] == "error"
        assert "Authentication failed" in data["google_calendar"]["error"]
        assert data["google_calendar"]["consecutive_failures"] == 3
    
    def test_metrics_endpoint_success(
        self,
        client,
        mock_dependency_override,
        auth_headers
    ):
        """Test metrics endpoint."""
        # Setup
        mock_trigger_manager, mock_dead_letter_queue = mock_dependency_override
        
        # Mock trigger statistics
        mock_trigger_manager.get_statistics = AsyncMock(return_value={
            "total_triggers": 25,
            "active_triggers": 20,
            "paused_triggers": 5,
            "total_executions": 1500,
            "successful_executions": 1450,
            "failed_executions": 50,
            "executions_last_24h": 120,
            "average_execution_time_ms": 250
        })
        
        # Mock DLQ statistics
        mock_dead_letter_queue.get_failure_statistics = AsyncMock(return_value={
            "total_failed_executions": 50,
            "recent_failures_24h": 5,
            "failures_by_trigger_type": {
                "google_calendar": 45,
                "slack": 5
            },
            "common_error_patterns": [
                {"error": "Connection timeout", "count": 20},
                {"error": "Authentication failed", "count": 15}
            ]
        })
        
        # Execute
        response = client.get(
            "/api/v1/health/metrics",
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert "triggers" in data
        assert "executions" in data
        assert "failures" in data
        assert data["triggers"]["total"] == 25
        assert data["triggers"]["active"] == 20
        assert data["executions"]["total"] == 1500
        assert data["executions"]["successful"] == 1450
        assert data["failures"]["total"] == 50
        assert data["failures"]["recent_24h"] == 5
    
    def test_metrics_endpoint_unauthorized(self, client):
        """Test metrics endpoint without authentication."""
        # Execute
        response = client.get("/api/v1/health/metrics")
        
        # Assertions
        assert response.status_code == 401
    
    def test_health_check_with_database_error(self, client):
        """Test health check when database is unavailable."""
        # Mock database health check failure
        with patch("src.api.routes.health.check_database_health") as mock_db_health:
            mock_db_health.side_effect = Exception("Database connection failed")
            
            # Execute
            response = client.get("/api/v1/health")
        
        # Assertions
        assert response.status_code == 503  # Service Unavailable
        data = response.json()
        assert data["status"] == "unhealthy"
        assert "database" in data.get("error", "").lower()
    
    def test_health_check_response_time(self, client):
        """Test that health check responds quickly."""
        import time
        
        # Execute
        start_time = time.time()
        response = client.get("/api/v1/health")
        end_time = time.time()
        
        # Assertions
        assert response.status_code == 200
        assert (end_time - start_time) < 1.0  # Should respond within 1 second
    
    def test_health_check_includes_version_info(self, client):
        """Test that health check includes version information."""
        # Execute
        response = client.get("/api/v1/health")
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert "version" in data
        assert "build_time" in data or "timestamp" in data
    
    def test_adapter_health_empty_adapters(
        self,
        client,
        mock_dependency_override,
        auth_headers
    ):
        """Test adapter health when no adapters are registered."""
        # Setup
        mock_trigger_manager, _ = mock_dependency_override
        mock_trigger_manager.get_adapter_health = AsyncMock(return_value={})
        
        # Execute
        response = client.get(
            "/api/v1/health/adapters",
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data == {}
    
    def test_metrics_calculation_accuracy(
        self,
        client,
        mock_dependency_override,
        auth_headers
    ):
        """Test that metrics calculations are accurate."""
        # Setup
        mock_trigger_manager, mock_dead_letter_queue = mock_dependency_override
        
        # Mock statistics with specific numbers for calculation testing
        mock_trigger_manager.get_statistics = AsyncMock(return_value={
            "total_triggers": 100,
            "active_triggers": 80,
            "paused_triggers": 20,
            "total_executions": 1000,
            "successful_executions": 950,
            "failed_executions": 50
        })
        
        mock_dead_letter_queue.get_failure_statistics = AsyncMock(return_value={
            "total_failed_executions": 50,
            "recent_failures_24h": 10
        })
        
        # Execute
        response = client.get(
            "/api/v1/health/metrics",
            headers=auth_headers
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        
        # Verify calculations
        total_triggers = data["triggers"]["total"]
        active_triggers = data["triggers"]["active"]
        paused_triggers = data["triggers"]["paused"]
        
        assert total_triggers == active_triggers + paused_triggers
        
        total_executions = data["executions"]["total"]
        successful_executions = data["executions"]["successful"]
        failed_executions = data["executions"]["failed"]
        
        assert total_executions == successful_executions + failed_executions
        
        # Success rate should be calculated correctly
        expected_success_rate = (successful_executions / total_executions) * 100
        if "success_rate" in data["executions"]:
            assert abs(data["executions"]["success_rate"] - expected_success_rate) < 0.01
