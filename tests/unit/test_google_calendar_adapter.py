"""
Unit tests for the GoogleCalendarAdapter class.

This module tests the Google Calendar adapter functionality,
including event processing, webhook management, and OAuth2 integration.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4
from datetime import datetime

from src.adapters.google_calendar import GoogleCalendarAdapter
from src.database.models import Trigger
from tests.fixtures.database import sample_trigger_data
from tests.fixtures.http import google_calendar_event_data, google_calendar_webhook_payload
from tests.fixtures.mocks import (
    mock_google_calendar_service,
    mock_google_credentials,
    mock_auth_client,
    mock_settings,
    mock_logger,
    google_api_error,
    oauth_error
)


class TestGoogleCalendarAdapter:
    """Test cases for GoogleCalendarAdapter class."""
    
    @pytest.fixture
    def adapter(self, mock_settings, mock_logger, mock_auth_client):
        """Create a GoogleCalendarAdapter instance for testing."""
        with patch("src.adapters.google_calendar.get_settings", return_value=mock_settings), \
             patch("src.adapters.google_calendar.get_logger", return_value=mock_logger):
            
            adapter = GoogleCalendarAdapter()
            adapter.auth_client = mock_auth_client
            return adapter
    
    @pytest.fixture
    def sample_trigger(self, sample_trigger_data):
        """Create a sample Google Calendar trigger."""
        trigger_data = sample_trigger_data.copy()
        trigger_data["trigger_config"] = {
            "calendar_id": "<EMAIL>",
            "event_types": ["created", "updated"],
            "filters": {
                "summary_contains": "meeting"
            }
        }
        trigger = Trigger(**trigger_data)
        trigger.id = uuid4()
        return trigger
    
    @pytest.mark.asyncio
    async def test_setup_trigger_success(
        self,
        adapter,
        sample_trigger,
        mock_google_calendar_service,
        mock_google_credentials
    ):
        """Test successful trigger setup."""
        # Mock credentials retrieval
        adapter.auth_client.get_credentials = AsyncMock(return_value={
            "access_token": "test-token",
            "refresh_token": "refresh-token"
        })
        
        # Mock Google Calendar service
        with patch("src.adapters.google_calendar.build", return_value=mock_google_calendar_service), \
             patch("src.adapters.google_calendar.Credentials", return_value=mock_google_credentials):
            
            result = await adapter.setup_trigger(sample_trigger)
        
        # Assertions
        assert result is True
        adapter.auth_client.get_credentials.assert_called_once_with(
            sample_trigger.user_id,
            "google_calendar"
        )
        mock_google_calendar_service.events().watch.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_setup_trigger_no_credentials(self, adapter, sample_trigger):
        """Test trigger setup when credentials are not available."""
        # Mock credentials retrieval returning None
        adapter.auth_client.get_credentials = AsyncMock(return_value=None)
        
        result = await adapter.setup_trigger(sample_trigger)
        
        # Assertions
        assert result is False
        adapter.auth_client.get_credentials.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_setup_trigger_api_error(
        self,
        adapter,
        sample_trigger,
        mock_google_calendar_service,
        mock_google_credentials,
        google_api_error
    ):
        """Test trigger setup with Google API error."""
        # Mock credentials retrieval
        adapter.auth_client.get_credentials = AsyncMock(return_value={
            "access_token": "test-token",
            "refresh_token": "refresh-token"
        })
        
        # Mock Google Calendar service with error
        mock_google_calendar_service.events().watch().execute.side_effect = google_api_error
        
        with patch("src.adapters.google_calendar.build", return_value=mock_google_calendar_service), \
             patch("src.adapters.google_calendar.Credentials", return_value=mock_google_credentials):
            
            result = await adapter.setup_trigger(sample_trigger)
        
        # Assertions
        assert result is False
    
    @pytest.mark.asyncio
    async def test_remove_trigger_success(
        self,
        adapter,
        sample_trigger,
        mock_google_calendar_service,
        mock_google_credentials
    ):
        """Test successful trigger removal."""
        # Setup trigger with subscription info
        sample_trigger.trigger_config["subscription_id"] = "test-subscription-123"
        
        # Mock credentials retrieval
        adapter.auth_client.get_credentials = AsyncMock(return_value={
            "access_token": "test-token",
            "refresh_token": "refresh-token"
        })
        
        # Mock Google Calendar service
        with patch("src.adapters.google_calendar.build", return_value=mock_google_calendar_service), \
             patch("src.adapters.google_calendar.Credentials", return_value=mock_google_credentials):
            
            result = await adapter.remove_trigger(sample_trigger)
        
        # Assertions
        assert result is True
        # Note: Google Calendar doesn't have a direct unsubscribe API,
        # so we just return True for successful credential validation
    
    @pytest.mark.asyncio
    async def test_remove_trigger_no_credentials(self, adapter, sample_trigger):
        """Test trigger removal when credentials are not available."""
        # Mock credentials retrieval returning None
        adapter.auth_client.get_credentials = AsyncMock(return_value=None)
        
        result = await adapter.remove_trigger(sample_trigger)
        
        # Assertions
        assert result is False
        adapter.auth_client.get_credentials.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_process_event_created(
        self,
        adapter,
        sample_trigger,
        google_calendar_event_data
    ):
        """Test processing a 'created' event."""
        # Setup event data
        event_data = {
            "event_type": "created",
            "event_data": google_calendar_event_data
        }
        
        result = await adapter.process_event(event_data, sample_trigger)
        
        # Assertions
        assert result is True
    
    @pytest.mark.asyncio
    async def test_process_event_updated(
        self,
        adapter,
        sample_trigger,
        google_calendar_event_data
    ):
        """Test processing an 'updated' event."""
        # Setup event data
        event_data = {
            "event_type": "updated",
            "event_data": google_calendar_event_data
        }
        
        result = await adapter.process_event(event_data, sample_trigger)
        
        # Assertions
        assert result is True
    
    @pytest.mark.asyncio
    async def test_process_event_deleted(self, adapter, sample_trigger):
        """Test processing a 'deleted' event."""
        # Setup event data for deleted event
        event_data = {
            "event_type": "deleted",
            "event_data": {
                "id": "deleted-event-123",
                "status": "cancelled"
            }
        }
        
        result = await adapter.process_event(event_data, sample_trigger)
        
        # Assertions
        assert result is True
    
    @pytest.mark.asyncio
    async def test_process_event_filtered_out(self, adapter, sample_trigger):
        """Test processing an event that should be filtered out."""
        # Setup trigger with specific filter
        sample_trigger.trigger_config["filters"] = {
            "summary_contains": "important"
        }
        
        # Setup event data that doesn't match filter
        event_data = {
            "event_type": "created",
            "event_data": {
                "id": "test-event-123",
                "summary": "Regular meeting",  # Doesn't contain "important"
                "start": {"dateTime": "2024-01-15T10:00:00Z"}
            }
        }
        
        result = await adapter.process_event(event_data, sample_trigger)
        
        # Event should be processed but might be filtered
        # The actual filtering logic depends on implementation
        assert isinstance(result, bool)
    
    @pytest.mark.asyncio
    async def test_process_event_invalid_data(self, adapter, sample_trigger):
        """Test processing an event with invalid data."""
        # Setup invalid event data
        event_data = {
            "event_type": "invalid_type",
            "event_data": {}
        }
        
        result = await adapter.process_event(event_data, sample_trigger)
        
        # Should handle invalid data gracefully
        assert isinstance(result, bool)
    
    @pytest.mark.asyncio
    async def test_get_health_success(
        self,
        adapter,
        mock_google_calendar_service,
        mock_google_credentials
    ):
        """Test health check with successful API access."""
        # Mock credentials for health check
        adapter.auth_client.get_credentials = AsyncMock(return_value={
            "access_token": "test-token",
            "refresh_token": "refresh-token"
        })
        
        # Mock Google Calendar service
        with patch("src.adapters.google_calendar.build", return_value=mock_google_calendar_service), \
             patch("src.adapters.google_calendar.Credentials", return_value=mock_google_credentials):
            
            result = await adapter.get_health()
        
        # Assertions
        assert result["status"] == "healthy"
        assert result["external_service_status"] == "available"
    
    @pytest.mark.asyncio
    async def test_get_health_api_error(
        self,
        adapter,
        mock_google_calendar_service,
        mock_google_credentials,
        google_api_error
    ):
        """Test health check with API error."""
        # Mock credentials
        adapter.auth_client.get_credentials = AsyncMock(return_value={
            "access_token": "test-token",
            "refresh_token": "refresh-token"
        })
        
        # Mock Google Calendar service with error
        mock_google_calendar_service.events().list().execute.side_effect = google_api_error
        
        with patch("src.adapters.google_calendar.build", return_value=mock_google_calendar_service), \
             patch("src.adapters.google_calendar.Credentials", return_value=mock_google_credentials):
            
            result = await adapter.get_health()
        
        # Assertions
        assert result["status"] == "error"
        assert result["external_service_status"] == "unavailable"
    
    @pytest.mark.asyncio
    async def test_get_health_no_credentials(self, adapter):
        """Test health check when no credentials are available."""
        # Mock credentials retrieval returning None
        adapter.auth_client.get_credentials = AsyncMock(return_value=None)
        
        result = await adapter.get_health()
        
        # Assertions
        assert result["status"] == "error"
        assert "No credentials available" in result.get("error", "")
    
    @pytest.mark.asyncio
    async def test_pause_trigger_success(self, adapter, sample_trigger):
        """Test pausing a trigger."""
        result = await adapter.pause_trigger(sample_trigger)
        
        # Pausing should succeed (implementation may vary)
        assert result is True
    
    @pytest.mark.asyncio
    async def test_resume_trigger_success(self, adapter, sample_trigger):
        """Test resuming a trigger."""
        result = await adapter.resume_trigger(sample_trigger)
        
        # Resuming should succeed (implementation may vary)
        assert result is True
    
    @pytest.mark.asyncio
    async def test_get_trigger_info_success(self, adapter, sample_trigger):
        """Test getting trigger information."""
        # Setup trigger with subscription info
        sample_trigger.trigger_config["subscription_id"] = "test-subscription-123"
        
        result = await adapter.get_trigger_info(sample_trigger)
        
        # Assertions
        assert isinstance(result, dict)
        # The exact content depends on implementation
    
    def test_transform_event_data_created(self, adapter, google_calendar_event_data):
        """Test event data transformation for created events."""
        result = adapter._transform_event_data(google_calendar_event_data, "created")
        
        # Assertions
        assert result["event_type"] == "created"
        assert result["event_id"] == google_calendar_event_data["id"]
        assert result["summary"] == google_calendar_event_data["summary"]
        assert "start_time" in result
        assert "end_time" in result
    
    def test_transform_event_data_updated(self, adapter, google_calendar_event_data):
        """Test event data transformation for updated events."""
        result = adapter._transform_event_data(google_calendar_event_data, "updated")
        
        # Assertions
        assert result["event_type"] == "updated"
        assert result["event_id"] == google_calendar_event_data["id"]
    
    def test_transform_event_data_deleted(self, adapter):
        """Test event data transformation for deleted events."""
        deleted_event_data = {
            "id": "deleted-event-123",
            "status": "cancelled"
        }
        
        result = adapter._transform_event_data(deleted_event_data, "deleted")
        
        # Assertions
        assert result["event_type"] == "deleted"
        assert result["event_id"] == "deleted-event-123"
        assert result["status"] == "cancelled"
    
    def test_should_process_event_matching_filter(self, adapter, sample_trigger):
        """Test event filtering with matching criteria."""
        # Setup trigger with filter
        sample_trigger.trigger_config["filters"] = {
            "summary_contains": "meeting"
        }
        
        event_data = {
            "summary": "Important meeting with team",
            "start": {"dateTime": "2024-01-15T10:00:00Z"}
        }
        
        result = adapter._should_process_event(event_data, sample_trigger)
        
        # Should process because summary contains "meeting"
        assert result is True
    
    def test_should_process_event_non_matching_filter(self, adapter, sample_trigger):
        """Test event filtering with non-matching criteria."""
        # Setup trigger with filter
        sample_trigger.trigger_config["filters"] = {
            "summary_contains": "important"
        }
        
        event_data = {
            "summary": "Regular team sync",  # Doesn't contain "important"
            "start": {"dateTime": "2024-01-15T10:00:00Z"}
        }
        
        result = adapter._should_process_event(event_data, sample_trigger)
        
        # Should not process because summary doesn't contain "important"
        assert result is False
    
    def test_should_process_event_no_filters(self, adapter, sample_trigger):
        """Test event filtering when no filters are configured."""
        # Setup trigger without filters
        sample_trigger.trigger_config.pop("filters", None)
        
        event_data = {
            "summary": "Any event",
            "start": {"dateTime": "2024-01-15T10:00:00Z"}
        }
        
        result = adapter._should_process_event(event_data, sample_trigger)
        
        # Should process all events when no filters
        assert result is True
