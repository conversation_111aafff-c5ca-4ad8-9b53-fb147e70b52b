"""
Unit tests for the Retry<PERSON><PERSON>ler class.

This module tests the retry mechanism functionality,
including exponential backoff, error classification, and retry logic.
"""

import asyncio
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Any

from src.utils.retry import (
    Re<PERSON><PERSON><PERSON><PERSON>,
    RetryableError,
    NonRetryableError,
    calculate_backoff_delay,
    retry_async,
    retry_sync
)
from tests.fixtures.mocks import mock_settings, mock_logger


class TestRetryHandler:
    """Test cases for RetryHandler class."""
    
    @pytest.fixture
    def retry_handler(self, mock_settings, mock_logger):
        """Create a RetryHandler instance for testing."""
        with patch("src.utils.retry.get_settings", return_value=mock_settings), \
             patch("src.utils.retry.get_logger", return_value=mock_logger):
            return RetryHandler(max_attempts=3, base_delay=0.1)  # Fast tests
    
    def test_is_retryable_with_retryable_error(self, retry_handler):
        """Test error classification for retryable errors."""
        # Test built-in retryable errors
        assert retry_handler.is_retryable(RetryableError("Test error"))
        assert retry_handler.is_retryable(ConnectionError("Connection failed"))
        assert retry_handler.is_retryable(TimeoutError("Request timed out"))
        assert retry_handler.is_retryable(OSError("OS error"))
    
    def test_is_retryable_with_non_retryable_error(self, retry_handler):
        """Test error classification for non-retryable errors."""
        # Test built-in non-retryable errors
        assert not retry_handler.is_retryable(NonRetryableError("Test error"))
        assert not retry_handler.is_retryable(ValueError("Invalid value"))
        assert not retry_handler.is_retryable(TypeError("Type error"))
        assert not retry_handler.is_retryable(KeyError("Key not found"))
        assert not retry_handler.is_retryable(AttributeError("Attribute error"))
    
    def test_is_retryable_with_unknown_error(self, retry_handler):
        """Test error classification for unknown errors."""
        # Unknown errors should not be retryable by default
        assert not retry_handler.is_retryable(RuntimeError("Unknown error"))
        assert not retry_handler.is_retryable(Exception("Generic exception"))
    
    def test_is_retryable_with_custom_exceptions(self, mock_settings, mock_logger):
        """Test error classification with custom exception lists."""
        # Custom retryable exceptions
        custom_retryable = [RuntimeError, ValueError]
        custom_non_retryable = [ConnectionError]
        
        with patch("src.utils.retry.get_settings", return_value=mock_settings), \
             patch("src.utils.retry.get_logger", return_value=mock_logger):
            
            handler = RetryHandler(
                retryable_exceptions=custom_retryable,
                non_retryable_exceptions=custom_non_retryable
            )
        
        # Test custom configuration
        assert handler.is_retryable(RuntimeError("Test"))
        assert handler.is_retryable(ValueError("Test"))
        assert not handler.is_retryable(ConnectionError("Test"))
        assert not handler.is_retryable(TypeError("Test"))  # Unknown, defaults to non-retryable
    
    @pytest.mark.asyncio
    async def test_execute_async_success_first_attempt(self, retry_handler):
        """Test successful execution on first attempt."""
        # Setup
        mock_func = AsyncMock(return_value="success")
        
        # Execute
        result = await retry_handler.execute_async(mock_func, "arg1", kwarg1="value1")
        
        # Assertions
        assert result == "success"
        mock_func.assert_called_once_with("arg1", kwarg1="value1")
    
    @pytest.mark.asyncio
    async def test_execute_async_success_after_retries(self, retry_handler):
        """Test successful execution after retries."""
        # Setup
        mock_func = AsyncMock()
        mock_func.side_effect = [
            RetryableError("First failure"),
            RetryableError("Second failure"),
            "success"
        ]
        
        # Execute
        result = await retry_handler.execute_async(mock_func)
        
        # Assertions
        assert result == "success"
        assert mock_func.call_count == 3
    
    @pytest.mark.asyncio
    async def test_execute_async_max_retries_exceeded(self, retry_handler):
        """Test execution when max retries are exceeded."""
        # Setup
        mock_func = AsyncMock()
        mock_func.side_effect = RetryableError("Persistent failure")
        
        # Execute and expect exception
        with pytest.raises(RetryableError, match="Persistent failure"):
            await retry_handler.execute_async(mock_func)
        
        # Assertions
        assert mock_func.call_count == 3  # max_attempts
    
    @pytest.mark.asyncio
    async def test_execute_async_non_retryable_error(self, retry_handler):
        """Test execution with non-retryable error."""
        # Setup
        mock_func = AsyncMock()
        mock_func.side_effect = NonRetryableError("Non-retryable failure")
        
        # Execute and expect immediate exception
        with pytest.raises(NonRetryableError, match="Non-retryable failure"):
            await retry_handler.execute_async(mock_func)
        
        # Assertions
        assert mock_func.call_count == 1  # No retries
    
    def test_execute_sync_success_first_attempt(self, retry_handler):
        """Test successful sync execution on first attempt."""
        # Setup
        mock_func = MagicMock(return_value="success")
        
        # Execute
        result = retry_handler.execute_sync(mock_func, "arg1", kwarg1="value1")
        
        # Assertions
        assert result == "success"
        mock_func.assert_called_once_with("arg1", kwarg1="value1")
    
    def test_execute_sync_success_after_retries(self, retry_handler):
        """Test successful sync execution after retries."""
        # Setup
        mock_func = MagicMock()
        mock_func.side_effect = [
            RetryableError("First failure"),
            RetryableError("Second failure"),
            "success"
        ]
        
        # Execute
        result = retry_handler.execute_sync(mock_func)
        
        # Assertions
        assert result == "success"
        assert mock_func.call_count == 3
    
    def test_execute_sync_max_retries_exceeded(self, retry_handler):
        """Test sync execution when max retries are exceeded."""
        # Setup
        mock_func = MagicMock()
        mock_func.side_effect = RetryableError("Persistent failure")
        
        # Execute and expect exception
        with pytest.raises(RetryableError, match="Persistent failure"):
            retry_handler.execute_sync(mock_func)
        
        # Assertions
        assert mock_func.call_count == 3  # max_attempts
    
    def test_execute_sync_non_retryable_error(self, retry_handler):
        """Test sync execution with non-retryable error."""
        # Setup
        mock_func = MagicMock()
        mock_func.side_effect = NonRetryableError("Non-retryable failure")
        
        # Execute and expect immediate exception
        with pytest.raises(NonRetryableError, match="Non-retryable failure"):
            retry_handler.execute_sync(mock_func)
        
        # Assertions
        assert mock_func.call_count == 1  # No retries


class TestBackoffCalculation:
    """Test cases for backoff delay calculation."""
    
    def test_calculate_backoff_delay_basic(self):
        """Test basic backoff delay calculation."""
        # Test exponential backoff
        delay0 = calculate_backoff_delay(0, base_delay=1.0, backoff_factor=2.0, jitter=False)
        delay1 = calculate_backoff_delay(1, base_delay=1.0, backoff_factor=2.0, jitter=False)
        delay2 = calculate_backoff_delay(2, base_delay=1.0, backoff_factor=2.0, jitter=False)
        
        assert delay0 == 1.0
        assert delay1 == 2.0
        assert delay2 == 4.0
    
    def test_calculate_backoff_delay_max_delay(self):
        """Test backoff delay with maximum limit."""
        # Test max delay enforcement
        delay = calculate_backoff_delay(10, base_delay=1.0, backoff_factor=2.0, max_delay=5.0, jitter=False)
        assert delay == 5.0
    
    def test_calculate_backoff_delay_with_jitter(self):
        """Test backoff delay with jitter."""
        # Test that jitter produces different values
        delays = []
        for _ in range(10):
            delay = calculate_backoff_delay(2, base_delay=1.0, backoff_factor=2.0, jitter=True)
            delays.append(delay)
        
        # Should have some variation due to jitter
        assert len(set(delays)) > 1
        
        # All delays should be positive and around the expected value
        for delay in delays:
            assert delay > 0
            assert delay <= 5.0  # 4.0 + 25% jitter
    
    def test_calculate_backoff_delay_zero_attempt(self):
        """Test backoff delay for zero attempt."""
        delay = calculate_backoff_delay(0, jitter=False)
        assert delay == 1.0  # base_delay default


class TestRetryDecorators:
    """Test cases for retry decorators."""
    
    @pytest.mark.asyncio
    async def test_retry_async_decorator_success(self, mock_settings, mock_logger):
        """Test async retry decorator with successful execution."""
        with patch("src.utils.retry.get_settings", return_value=mock_settings), \
             patch("src.utils.retry.get_logger", return_value=mock_logger):
            
            @retry_async(max_attempts=3, base_delay=0.1)
            async def test_func(value):
                return f"success: {value}"
            
            result = await test_func("test")
            assert result == "success: test"
    
    @pytest.mark.asyncio
    async def test_retry_async_decorator_with_retries(self, mock_settings, mock_logger):
        """Test async retry decorator with retries."""
        with patch("src.utils.retry.get_settings", return_value=mock_settings), \
             patch("src.utils.retry.get_logger", return_value=mock_logger):
            
            call_count = 0
            
            @retry_async(max_attempts=3, base_delay=0.1)
            async def test_func():
                nonlocal call_count
                call_count += 1
                if call_count < 3:
                    raise RetryableError("Temporary failure")
                return "success"
            
            result = await test_func()
            assert result == "success"
            assert call_count == 3
    
    def test_retry_sync_decorator_success(self, mock_settings, mock_logger):
        """Test sync retry decorator with successful execution."""
        with patch("src.utils.retry.get_settings", return_value=mock_settings), \
             patch("src.utils.retry.get_logger", return_value=mock_logger):
            
            @retry_sync(max_attempts=3, base_delay=0.1)
            def test_func(value):
                return f"success: {value}"
            
            result = test_func("test")
            assert result == "success: test"
    
    def test_retry_sync_decorator_with_retries(self, mock_settings, mock_logger):
        """Test sync retry decorator with retries."""
        with patch("src.utils.retry.get_settings", return_value=mock_settings), \
             patch("src.utils.retry.get_logger", return_value=mock_logger):
            
            call_count = 0
            
            @retry_sync(max_attempts=3, base_delay=0.1)
            def test_func():
                nonlocal call_count
                call_count += 1
                if call_count < 3:
                    raise RetryableError("Temporary failure")
                return "success"
            
            result = test_func()
            assert result == "success"
            assert call_count == 3
