"""
Test runner script for the Trigger Service.

This script provides a convenient way to run different test suites
with proper configuration and reporting.
"""

import sys
import subprocess
import argparse
from pathlib import Path


def run_command(command, description):
    """Run a command and handle the result."""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {command}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        return True
    except subprocess.CalledProcessError as e:
        print(f"ERROR: Command failed with exit code {e.returncode}")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)
        return False


def run_unit_tests(verbose=False, coverage=False):
    """Run unit tests."""
    cmd = "python -m pytest tests/unit/"
    
    if verbose:
        cmd += " -v"
    
    if coverage:
        cmd += " --cov=src --cov-report=html --cov-report=term"
    
    cmd += " --tb=short"
    
    return run_command(cmd, "Unit Tests")


def run_integration_tests(verbose=False):
    """Run integration tests."""
    cmd = "python -m pytest tests/integration/"
    
    if verbose:
        cmd += " -v"
    
    cmd += " --tb=short"
    
    return run_command(cmd, "Integration Tests")


def run_e2e_tests(verbose=False):
    """Run end-to-end tests."""
    cmd = "python -m pytest tests/e2e/"
    
    if verbose:
        cmd += " -v"
    
    cmd += " --tb=short"
    
    return run_command(cmd, "End-to-End Tests")


def run_performance_tests(verbose=False):
    """Run performance tests."""
    cmd = "python -m pytest tests/e2e/test_performance.py"
    
    if verbose:
        cmd += " -v"
    
    cmd += " --tb=short -s"  # -s to see print statements
    
    return run_command(cmd, "Performance Tests")


def run_all_tests(verbose=False, coverage=False):
    """Run all test suites."""
    print("Running complete test suite...")
    
    results = []
    
    # Unit tests with coverage
    results.append(("Unit Tests", run_unit_tests(verbose, coverage)))
    
    # Integration tests
    results.append(("Integration Tests", run_integration_tests(verbose)))
    
    # End-to-end tests
    results.append(("E2E Tests", run_e2e_tests(verbose)))
    
    # Performance tests
    results.append(("Performance Tests", run_performance_tests(verbose)))
    
    # Summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print(f"{'='*60}")
    
    all_passed = True
    for test_name, passed in results:
        status = "PASSED" if passed else "FAILED"
        print(f"{test_name:<20} {status}")
        if not passed:
            all_passed = False
    
    print(f"{'='*60}")
    overall_status = "ALL TESTS PASSED" if all_passed else "SOME TESTS FAILED"
    print(f"Overall Result: {overall_status}")
    print(f"{'='*60}")
    
    return all_passed


def run_specific_test(test_path, verbose=False):
    """Run a specific test file or test function."""
    cmd = f"python -m pytest {test_path}"
    
    if verbose:
        cmd += " -v"
    
    cmd += " --tb=short"
    
    return run_command(cmd, f"Specific Test: {test_path}")


def check_test_environment():
    """Check if the test environment is properly set up."""
    print("Checking test environment...")
    
    # Check if pytest is installed
    try:
        import pytest
        print(f"✓ pytest version: {pytest.__version__}")
    except ImportError:
        print("✗ pytest not installed")
        return False
    
    # Check if required test dependencies are available
    required_packages = [
        "pytest-asyncio",
        "httpx",
        "sqlalchemy",
        "fastapi"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✓ {package} available")
        except ImportError:
            print(f"✗ {package} not available")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\nMissing packages: {', '.join(missing_packages)}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    
    # Check if test directories exist
    test_dirs = ["tests/unit", "tests/integration", "tests/e2e", "tests/fixtures"]
    for test_dir in test_dirs:
        if Path(test_dir).exists():
            print(f"✓ {test_dir} directory exists")
        else:
            print(f"✗ {test_dir} directory missing")
            return False
    
    print("✓ Test environment is ready")
    return True


def main():
    """Main function to handle command line arguments."""
    parser = argparse.ArgumentParser(
        description="Test runner for Trigger Service",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python tests/run_tests.py --all                    # Run all tests
  python tests/run_tests.py --unit --coverage        # Run unit tests with coverage
  python tests/run_tests.py --integration --verbose  # Run integration tests with verbose output
  python tests/run_tests.py --e2e                    # Run end-to-end tests
  python tests/run_tests.py --performance            # Run performance tests
  python tests/run_tests.py --test tests/unit/test_trigger_manager.py  # Run specific test
  python tests/run_tests.py --check                  # Check test environment
        """
    )
    
    parser.add_argument("--all", action="store_true", help="Run all test suites")
    parser.add_argument("--unit", action="store_true", help="Run unit tests")
    parser.add_argument("--integration", action="store_true", help="Run integration tests")
    parser.add_argument("--e2e", action="store_true", help="Run end-to-end tests")
    parser.add_argument("--performance", action="store_true", help="Run performance tests")
    parser.add_argument("--test", type=str, help="Run specific test file or function")
    parser.add_argument("--check", action="store_true", help="Check test environment")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--coverage", action="store_true", help="Generate coverage report (unit tests only)")
    
    args = parser.parse_args()
    
    # If no arguments provided, show help
    if len(sys.argv) == 1:
        parser.print_help()
        return
    
    # Check environment if requested
    if args.check:
        if check_test_environment():
            print("\n✓ Test environment is ready!")
            sys.exit(0)
        else:
            print("\n✗ Test environment has issues!")
            sys.exit(1)
    
    # Change to project root directory
    project_root = Path(__file__).parent.parent
    import os
    os.chdir(project_root)
    
    success = True
    
    if args.all:
        success = run_all_tests(args.verbose, args.coverage)
    elif args.unit:
        success = run_unit_tests(args.verbose, args.coverage)
    elif args.integration:
        success = run_integration_tests(args.verbose)
    elif args.e2e:
        success = run_e2e_tests(args.verbose)
    elif args.performance:
        success = run_performance_tests(args.verbose)
    elif args.test:
        success = run_specific_test(args.test, args.verbose)
    else:
        print("No test suite specified. Use --help for options.")
        parser.print_help()
        sys.exit(1)
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
