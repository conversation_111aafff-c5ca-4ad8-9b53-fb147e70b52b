# Google Calendar Event Field Mapping Guide

This guide explains how to use the new Google Calendar event field mapping feature to control which event fields are passed to workflow execution payloads.

## Overview

The Google Calendar event field mapping feature allows you to:

- Select specific fields from Google Calendar events
- Map Google Calendar field names to custom workflow field names
- Control the data types of mapped fields
- Reduce payload size by including only necessary fields

## Configuration

### Basic Trigger Configuration (Backward Compatible)

```json
{
  "workflow_id": "workflow-456",
  "trigger_type": "google_calendar",
  "trigger_name": "Meeting Reminder Workflow",
  "trigger_config": {
    "calendar_id": "primary",
    "use_polling": false,
    "poll_interval_seconds": 60,
    "webhook_ttl": 604800
  },
  "event_types": ["created", "updated"]
}
```

### Advanced Configuration with Field Mapping

```json
{
  "workflow_id": "workflow-456",
  "trigger_type": "google_calendar",
  "trigger_name": "Meeting Reminder Workflow",
  "trigger_config": {
    "calendar_id": "primary",
    "use_polling": false,
    "poll_interval_seconds": 60,
    "webhook_ttl": 604800,
    "selected_event_fields": [
      {
        "calendar_field": "summary",
        "workflow_field": "event_title",
        "field_type": "string"
      },
      {
        "calendar_field": "start.dateTime",
        "workflow_field": "start_time",
        "field_type": "string"
      },
      {
        "calendar_field": "end.dateTime",
        "workflow_field": "end_time",
        "field_type": "string"
      },
      {
        "calendar_field": "attendees",
        "workflow_field": "attendee_list",
        "field_type": "array"
      },
      {
        "calendar_field": "location",
        "workflow_field": "meeting_location",
        "field_type": "string"
      },
      {
        "calendar_field": "hangoutLink",
        "workflow_field": "meeting_link",
        "field_type": "string"
      }
    ]
  },
  "event_types": ["created", "updated"]
}
```

## Field Mapping Schema

### EventFieldMapping

| Field            | Type   | Required | Description                                                                     |
| ---------------- | ------ | -------- | ------------------------------------------------------------------------------- |
| `calendar_field` | string | Yes      | Google Calendar event field name (supports dot notation)                        |
| `workflow_field` | string | Yes      | Custom field name for the workflow                                              |
| `field_type`     | string | No       | Expected field type: "string", "number", "boolean", "array" (default: "string") |

### Supported Field Types

- **string**: Text values (default)
- **number**: Numeric values (converted from strings if possible)
- **boolean**: Boolean values (supports "true"/"false", "1"/"0", "yes"/"no")
- **array**: Array/list values (kept as-is)

## Available Google Calendar Fields

Based on the Google Calendar Event schema, you can map the following fields:

### Basic Event Information

- `id` - Event ID
- `summary` - Event title
- `description` - Event description
- `location` - Event location
- `status` - Event status (confirmed, tentative, cancelled)

### Date and Time

- `start.dateTime` - Start date and time
- `start.date` - Start date (for all-day events)
- `start.timeZone` - Start timezone
- `end.dateTime` - End date and time
- `end.date` - End date (for all-day events)
- `end.timeZone` - End timezone

### Attendees and Organizer

- `attendees` - Array of attendees
- `organizer.email` - Organizer email
- `organizer.displayName` - Organizer name
- `creator.email` - Creator email

### Meeting Links and Conference Data

- `hangoutLink` - Google Meet link
- `conferenceData.entryPoints` - Conference entry points
- `conferenceData.conferenceId` - Conference ID

### Metadata

- `created` - Creation timestamp
- `updated` - Last update timestamp
- `sequence` - Event sequence number
- `etag` - Event etag

## Example Workflow Payloads

### Without Field Mapping (Default Behavior)

```json
{
  "user_dependent_fields": [
    "event_type",
    "event_id",
    "timestamp",
    "source",
    "kind",
    "etag",
    "id",
    "status",
    "summary",
    "start",
    "end",
    "attendees",
    "hangoutLink"
  ],
  "user_payload_template": {
    "event_type": {
      "transition_id": "transition-event_type-default",
      "value": "created"
    },
    "summary": {
      "transition_id": "transition-summary-default",
      "value": "Team Sync-up"
    }
    // ... all other fields
  }
}
```

### With Field Mapping

```json
{
  "user_dependent_fields": [
    "event_title",
    "start_time",
    "end_time",
    "attendee_list",
    "meeting_location",
    "meeting_link"
  ],
  "user_payload_template": {
    "event_title": {
      "transition_id": "transition-event_title-1",
      "value": "Team Sync-up"
    },
    "start_time": {
      "transition_id": "transition-start_time-1",
      "value": "2025-06-19T10:00:00+05:30"
    },
    "end_time": {
      "transition_id": "transition-end_time-1",
      "value": "2025-06-19T10:30:00+05:30"
    },
    "attendee_list": {
      "transition_id": "transition-attendee_list-1",
      "value": [
        {
          "email": "<EMAIL>",
          "responseStatus": "accepted"
        },
        {
          "email": "<EMAIL>",
          "responseStatus": "needsAction"
        }
      ]
    },
    "meeting_location": {
      "transition_id": "transition-meeting_location-1",
      "value": "Conference Room A"
    },
    "meeting_link": {
      "transition_id": "transition-meeting_link-1",
      "value": "https://meet.google.com/xyz-abc-def"
    }
  }
}
```

## Best Practices

### 1. Use Meaningful Workflow Field Names

```json
{
  "calendar_field": "summary",
  "workflow_field": "event_title", // Better than "summary"
  "field_type": "string"
}
```

### 2. Specify Appropriate Field Types

```json
{
  "calendar_field": "attendees",
  "workflow_field": "attendee_list",
  "field_type": "array" // Important for arrays
}
```

### 3. Use Dot Notation for Nested Fields

```json
{
  "calendar_field": "start.dateTime", // Access nested properties
  "workflow_field": "start_time",
  "field_type": "string"
}
```

### 4. Map Only Required Fields

Only include fields that your workflow actually needs to reduce payload size and improve performance.

## Common Use Cases

### 1. Meeting Notification Workflow

```json
"selected_event_fields": [
  {
    "calendar_field": "summary",
    "workflow_field": "meeting_title",
    "field_type": "string"
  },
  {
    "calendar_field": "start.dateTime",
    "workflow_field": "meeting_start",
    "field_type": "string"
  },
  {
    "calendar_field": "attendees",
    "workflow_field": "participants",
    "field_type": "array"
  },
  {
    "calendar_field": "hangoutLink",
    "workflow_field": "video_link",
    "field_type": "string"
  }
]
```

### 2. Calendar Sync Workflow

```json
"selected_event_fields": [
  {
    "calendar_field": "id",
    "workflow_field": "calendar_event_id",
    "field_type": "string"
  },
  {
    "calendar_field": "summary",
    "workflow_field": "title",
    "field_type": "string"
  },
  {
    "calendar_field": "start.dateTime",
    "workflow_field": "start_datetime",
    "field_type": "string"
  },
  {
    "calendar_field": "end.dateTime",
    "workflow_field": "end_datetime",
    "field_type": "string"
  },
  {
    "calendar_field": "location",
    "workflow_field": "venue",
    "field_type": "string"
  }
]
```

### 3. Attendee Management Workflow

```json
"selected_event_fields": [
  {
    "calendar_field": "summary",
    "workflow_field": "event_name",
    "field_type": "string"
  },
  {
    "calendar_field": "attendees",
    "workflow_field": "attendee_list",
    "field_type": "array"
  },
  {
    "calendar_field": "organizer.email",
    "workflow_field": "organizer_email",
    "field_type": "string"
  }
]
```

## Migration Guide

### Existing Triggers

Existing triggers without `selected_event_fields` will continue to work with the default behavior (all fields extracted).

### Adding Field Mapping to Existing Triggers

1. Update the trigger configuration to include `selected_event_fields`
2. Test with a subset of fields first
3. Gradually add more fields as needed
4. Update your workflow to handle the new field names

## Testing

Use the provided test script to validate your field mappings:

```bash
python test_event_field_mapping.py
```

This script demonstrates:

- Schema validation
- Field extraction
- Payload generation
- Type conversion

## Troubleshooting

### Field Not Found

If a mapped field is not found in the event data, it will be omitted from the workflow payload.

### Type Conversion Errors

If type conversion fails, the original value is kept. Check the logs for conversion warnings.

### Nested Field Access

Use dot notation for nested fields: `start.dateTime`, `organizer.email`, etc.

### Array Fields

Always specify `"field_type": "array"` for array fields like `attendees`.
