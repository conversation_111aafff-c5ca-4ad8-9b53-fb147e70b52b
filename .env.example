# Application Configuration
DEBUG=true
HOST=0.0.0.0
PORT=8000
LOG_LEVEL=INFO
LOG_FORMAT=json

# Database Configuration
DATABASE_URL=postgresql://trigger_user:trigger_password@localhost:5432/trigger_db

# Auth Service Configuration
AUTH_SERVICE_URL=https://auth-service.example.com
AUTH_SERVICE_API_KEY=your-auth-service-api-key

# Workflow Service Configuration
WORKFLOW_SERVICE_URL=https://ruh-test-api.rapidinnovation.dev
WORKFLOW_SERVICE_API_KEY=your-workflow-service-api-key

# Google Calendar Configuration
GOOGLE_CALENDAR_WEBHOOK_URL=https://trigger-service.example.com/api/v1/webhooks/google-calendar

# Redis Configuration (for Task Queue and Distributed Locking)
REDIS_URL=redis://localhost:6379/0

# Task Queue Configuration
TASK_QUEUE_REDIS_URL=redis://localhost:6379/0
TASK_QUEUE_DEFAULT_TIMEOUT=30
TASK_QUEUE_MAX_RETRIES=3
TASK_QUEUE_RETRY_DELAY=5

# Distributed Locking Configuration
DISTRIBUTED_LOCK_REDIS_URL=redis://localhost:6379/0
DISTRIBUTED_LOCK_DEFAULT_TTL=300
DISTRIBUTED_LOCK_RETRY_DELAY=0.1
DISTRIBUTED_LOCK_MAX_RETRIES=10

# Legacy Celery Configuration (deprecated, use Task Queue instead)
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Security
SECRET_KEY=your-secret-key-here
API_KEY=your-api-key-for-internal-services

# External Service Timeouts (seconds)
HTTP_TIMEOUT=30
AUTH_SERVICE_TIMEOUT=10
WORKFLOW_SERVICE_TIMEOUT=60

# Retry Configuration
MAX_RETRY_ATTEMPTS=5
RETRY_BACKOFF_FACTOR=2
RETRY_MAX_DELAY=300

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=20

# Scheduler Configuration
SCHEDULER_BATCH_SIZE=50
SCHEDULER_CONCURRENCY=10
SCHEDULER_CYCLE_INTERVAL=30

# Production Scalability Configuration
ENABLE_CONCURRENT_PROCESSING=true
MAX_CONCURRENT_SCHEDULERS=100
SEMAPHORE_LIMIT=10
ENABLE_TASK_QUEUE=true
ENABLE_DISTRIBUTED_LOCKING=true

# Database Optimization
ENABLE_DATABASE_INDEXES=true
DATABASE_QUERY_TIMEOUT=30
DATABASE_CONNECTION_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=10

# Task Worker Configuration
TASK_WORKER_CONCURRENCY=5
TASK_WORKER_QUEUES=workflow_execution,scheduler_tasks
TASK_WORKER_TIMEOUT=300

# Monitoring and Metrics
ENABLE_METRICS=true
METRICS_PORT=9090
ENABLE_SCHEDULER_METRICS=true
ENABLE_TASK_QUEUE_METRICS=true
METRICS_COLLECTION_INTERVAL=60

# Health Check Configuration
HEALTH_CHECK_REDIS=true
HEALTH_CHECK_DATABASE=true
HEALTH_CHECK_TIMEOUT=10

# Production Mode Settings
PRODUCTION_MODE=false
ENABLE_DEBUG_LOGGING=true
LOG_SQL_QUERIES=false
