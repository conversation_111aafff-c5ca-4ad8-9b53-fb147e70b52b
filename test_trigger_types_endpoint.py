#!/usr/bin/env python3
"""
Test script for the new trigger types endpoint.

This script tests the /api/v1/triggers/types endpoint to ensure it returns
the correct information about available trigger types.
"""

import asyncio
import json
from typing import Dict, Any

import httpx


async def test_trigger_types_endpoint():
    """Test the trigger types endpoint."""

    # Base URL for the API
    base_url = "http://localhost:8000"

    # Test both endpoints
    endpoints = [
        f"{base_url}/public/trigger-types",
        f"{base_url}/api/v1/triggers/types",
    ]

    print("🧪 Testing Trigger Types Endpoints")
    print("=" * 50)

    for endpoint in endpoints:
        print(f"\n📡 Testing endpoint: {endpoint}")
        try:
            async with httpx.AsyncClient() as client:
                print(f"📡 Making GET request to: {endpoint}")

                # Make the request (no authentication required for this endpoint)
                response = await client.get(endpoint)

                print(f"📊 Response Status: {response.status_code}")

                if response.status_code == 200:
                    print(f"✅ Request successful for {endpoint}!")

                    # Parse the response
                    data = response.json()

                    print(f"\n📋 Response Data from {endpoint}:")
                    print(json.dumps(data, indent=2))

                    # Validate the response structure (only for the first successful endpoint)
                    if endpoint == endpoints[0] or all(
                        ep != endpoints[0] for ep in endpoints
                    ):
                        await validate_response_structure(data)

                    return  # Exit after first successful test
                else:
                    print(
                        f"❌ Request failed for {endpoint} with status {response.status_code}"
                    )
                    print(f"Response: {response.text}")

        except Exception as e:
            print(f"❌ Test failed for {endpoint} with error: {str(e)}")

    # If we get here, all endpoints failed
    raise Exception("All endpoints failed")


async def validate_response_structure(data):
    """Validate the response structure."""
    print("\n🔍 Validating Response Structure:")

    # Check top-level structure
    assert "trigger_types" in data, "Missing 'trigger_types' field"
    assert "total_types" in data, "Missing 'total_types' field"
    print("✅ Top-level structure is correct")

    # Check trigger types array
    trigger_types = data["trigger_types"]
    assert isinstance(trigger_types, list), "trigger_types should be a list"
    assert len(trigger_types) > 0, "trigger_types should not be empty"
    print(f"✅ Found {len(trigger_types)} trigger type(s)")

    # Check Google Calendar trigger type
    google_calendar_trigger = None
    for trigger_type in trigger_types:
        if trigger_type["name"] == "google_calendar":
            google_calendar_trigger = trigger_type
            break

    assert google_calendar_trigger is not None, "Google Calendar trigger type not found"
    print("✅ Google Calendar trigger type found")

    # Validate Google Calendar trigger structure
    required_fields = [
        "name",
        "display_name",
        "description",
        "supported_events",
        "configuration_schema",
        "sample_event_data",
    ]
    for field in required_fields:
        assert field in google_calendar_trigger, f"Missing field: {field}"
    print("✅ Google Calendar trigger has all required fields")

    # Check supported events
    supported_events = google_calendar_trigger["supported_events"]
    expected_events = ["created", "updated", "deleted"]
    for event in expected_events:
        assert event in supported_events, f"Missing supported event: {event}"
    print("✅ All expected events are supported")

    # Check configuration schema
    config_schema = google_calendar_trigger["configuration_schema"]
    assert "type" in config_schema, "Configuration schema missing 'type'"
    assert "properties" in config_schema, "Configuration schema missing 'properties'"
    assert (
        "calendar_id" in config_schema["properties"]
    ), "Configuration schema missing 'calendar_id'"
    print("✅ Configuration schema is valid")

    # Check sample event data
    sample_event = google_calendar_trigger["sample_event_data"]
    assert isinstance(sample_event, dict), "Sample event data should be a dictionary"
    assert "kind" in sample_event, "Sample event missing 'kind' field"
    assert "id" in sample_event, "Sample event missing 'id' field"
    assert "summary" in sample_event, "Sample event missing 'summary' field"
    print("✅ Sample event data is valid")

    # Check total_types matches actual count
    assert data["total_types"] == len(
        trigger_types
    ), "total_types doesn't match actual count"
    print("✅ total_types field is accurate")

    print("\n🎉 All tests passed! The trigger types endpoint is working correctly.")

    # Display key information
    print("\n📊 Summary:")
    print(f"   • Total trigger types: {data['total_types']}")
    print(
        f"   • Google Calendar events: {', '.join(google_calendar_trigger['supported_events'])}"
    )
    print(f"   • Sample event ID: {sample_event.get('id', 'N/A')}")
    print(f"   • Sample event summary: {sample_event.get('summary', 'N/A')}")


async def test_original_endpoint():
    """Test the original trigger types endpoint."""

    # Base URL for the API
    base_url = "http://localhost:8000"
    endpoint = f"{base_url}/api/v1/triggers/types"

    print("🧪 Testing Original Trigger Types Endpoint")
    print("=" * 50)

    try:
        async with httpx.AsyncClient() as client:
            print(f"📡 Making GET request to: {endpoint}")

            # Make the request (no authentication required for this endpoint)
            response = await client.get(endpoint)

            print(f"📊 Response Status: {response.status_code}")

            if response.status_code == 200:
                print("✅ Request successful!")

                # Parse the response
                data = response.json()

                print("\n📋 Response Data:")
                print(json.dumps(data, indent=2))

                # Validate the response structure
                print("\n🔍 Validating Response Structure:")

                # Check top-level structure
                assert "trigger_types" in data, "Missing 'trigger_types' field"
                assert "total_types" in data, "Missing 'total_types' field"
                print("✅ Top-level structure is correct")

                # Check trigger types array
                trigger_types = data["trigger_types"]
                assert isinstance(trigger_types, list), "trigger_types should be a list"
                assert len(trigger_types) > 0, "trigger_types should not be empty"
                print(f"✅ Found {len(trigger_types)} trigger type(s)")

                # Check Google Calendar trigger type
                google_calendar_trigger = None
                for trigger_type in trigger_types:
                    if trigger_type["name"] == "google_calendar":
                        google_calendar_trigger = trigger_type
                        break

                assert (
                    google_calendar_trigger is not None
                ), "Google Calendar trigger type not found"
                print("✅ Google Calendar trigger type found")

                # Validate Google Calendar trigger structure
                required_fields = [
                    "name",
                    "display_name",
                    "description",
                    "supported_events",
                    "configuration_schema",
                    "sample_event_data",
                ]
                for field in required_fields:
                    assert field in google_calendar_trigger, f"Missing field: {field}"
                print("✅ Google Calendar trigger has all required fields")

                # Check supported events
                supported_events = google_calendar_trigger["supported_events"]
                expected_events = ["created", "updated", "deleted"]
                for event in expected_events:
                    assert (
                        event in supported_events
                    ), f"Missing supported event: {event}"
                print("✅ All expected events are supported")

                # Check configuration schema
                config_schema = google_calendar_trigger["configuration_schema"]
                assert "type" in config_schema, "Configuration schema missing 'type'"
                assert (
                    "properties" in config_schema
                ), "Configuration schema missing 'properties'"
                assert (
                    "calendar_id" in config_schema["properties"]
                ), "Configuration schema missing 'calendar_id'"
                print("✅ Configuration schema is valid")

                # Check sample event data
                sample_event = google_calendar_trigger["sample_event_data"]
                assert isinstance(
                    sample_event, dict
                ), "Sample event data should be a dictionary"
                assert "kind" in sample_event, "Sample event missing 'kind' field"
                assert "id" in sample_event, "Sample event missing 'id' field"
                assert "summary" in sample_event, "Sample event missing 'summary' field"
                print("✅ Sample event data is valid")

                # Check total_types matches actual count
                assert data["total_types"] == len(
                    trigger_types
                ), "total_types doesn't match actual count"
                print("✅ total_types field is accurate")

                print(
                    "\n🎉 All tests passed! The trigger types endpoint is working correctly."
                )

                # Display key information
                print("\n📊 Summary:")
                print(f"   • Total trigger types: {data['total_types']}")
                print(
                    f"   • Google Calendar events: {', '.join(google_calendar_trigger['supported_events'])}"
                )
                print(f"   • Sample event ID: {sample_event.get('id', 'N/A')}")
                print(
                    f"   • Sample event summary: {sample_event.get('summary', 'N/A')}"
                )

            else:
                print(f"❌ Request failed with status {response.status_code}")
                print(f"Response: {response.text}")

    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        raise


async def test_endpoint_accessibility():
    """Test that the endpoint is accessible without authentication."""

    base_url = "http://localhost:8000"
    endpoint = f"{base_url}/api/v1/triggers/types"

    print("\n🔓 Testing Public Access (No Authentication)")
    print("=" * 50)

    try:
        async with httpx.AsyncClient() as client:
            # Test without any headers
            response = await client.get(endpoint)

            if response.status_code == 200:
                print("✅ Endpoint is publicly accessible (no authentication required)")
            else:
                print(f"❌ Unexpected status code: {response.status_code}")
                print(f"Response: {response.text}")

    except Exception as e:
        print(f"❌ Accessibility test failed: {str(e)}")


if __name__ == "__main__":
    print("🚀 Starting Trigger Types Endpoint Tests")
    print("Make sure the FastAPI server is running on http://localhost:8000")
    print()

    # Run the tests
    asyncio.run(test_trigger_types_endpoint())
    asyncio.run(test_endpoint_accessibility())

    print("\n✨ Test suite completed!")
