# Trigger Types API Documentation

## Overview

The Trigger Types API provides information about all available trigger types in the system. This public endpoint allows developers to discover supported trigger types, their configuration requirements, and sample event data for integration purposes.

## Endpoint

### GET `/api/v1/triggers/types`

Returns information about all available trigger types with their details and sample event data.

**Authentication**: Not required (public endpoint)

**HTTP Method**: `GET`

**URL**: `http://localhost:8000/api/v1/triggers/types`

## Response Format

### Success Response (200 OK)

```json
{
  "trigger_types": [
    {
      "name": "google_calendar",
      "display_name": "Google Calendar",
      "description": "Trigger workflows based on Google Calendar events (created, updated, deleted)",
      "supported_events": ["created", "updated", "deleted"],
      "configuration_schema": {
        "type": "object",
        "properties": {
          "calendar_id": {
            "type": "string",
            "description": "Google Calendar ID (use 'primary' for main calendar)",
            "example": "primary"
          },
          "selected_event_fields": {
            "type": "array",
            "description": "Optional: Select specific event fields to include in workflow payload",
            "items": {
              "type": "object",
              "properties": {
                "source_field": {
                  "type": "string",
                  "description": "Source field from Google Calendar event (supports dot notation)"
                },
                "target_field": {
                  "type": "string",
                  "description": "Target field name in workflow payload"
                },
                "field_type": {
                  "type": "string",
                  "enum": ["string", "number", "boolean", "array"],
                  "description": "Expected field type for conversion"
                }
              },
              "required": ["source_field", "target_field", "field_type"]
            }
          }
        },
        "required": ["calendar_id"]
      },
      "sample_event_data": {
        "kind": "calendar#event",
        "etag": "\"3181161784712000\"",
        "id": "4eahs9ghkhrvkld72hogu9ph3e_20200519T140000Z",
        "status": "confirmed",
        "htmlLink": "https://www.google.com/calendar/event?eid=...",
        "created": "2020-05-19T17:00:58.000Z",
        "updated": "2020-05-19T17:00:58.356Z",
        "summary": "Sample Meeting",
        "description": "This is a sample meeting description",
        "location": "Conference Room A",
        "creator": {
          "email": "<EMAIL>",
          "displayName": "Admin User"
        },
        "organizer": {
          "email": "<EMAIL>",
          "displayName": "Admin User"
        },
        "start": {
          "dateTime": "2020-05-19T14:00:00-07:00",
          "timeZone": "America/Los_Angeles"
        },
        "end": {
          "dateTime": "2020-05-19T15:00:00-07:00",
          "timeZone": "America/Los_Angeles"
        },
        "iCalUID": "<EMAIL>",
        "sequence": 0,
        "attendees": [
          {
            "email": "<EMAIL>",
            "displayName": "Attendee One",
            "responseStatus": "needsAction"
          },
          {
            "email": "<EMAIL>",
            "displayName": "Attendee Two",
            "responseStatus": "accepted"
          }
        ],
        "hangoutLink": "https://meet.google.com/abc-defg-hij",
        "conferenceData": {
          "entryPoints": [
            {
              "entryPointType": "video",
              "uri": "https://meet.google.com/abc-defg-hij",
              "label": "meet.google.com/abc-defg-hij"
            }
          ],
          "conferenceSolution": {
            "key": {
              "type": "hangoutsMeet"
            },
            "name": "Google Meet",
            "iconUri": "https://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v6/web-512dp/logo_meet_2020q4_color_2x_web_512dp.png"
          },
          "conferenceId": "abc-defg-hij"
        },
        "reminders": {
          "useDefault": true
        }
      }
    }
  ],
  "total_types": 1
}
```

### Error Response (500 Internal Server Error)

```json
{
  "detail": "Internal server error while retrieving trigger types"
}
```

## Response Schema

### TriggerTypesResponse

| Field           | Type                   | Description                     |
| --------------- | ---------------------- | ------------------------------- |
| `trigger_types` | Array[TriggerTypeInfo] | List of available trigger types |
| `total_types`   | Integer                | Total number of trigger types   |

### TriggerTypeInfo

| Field                  | Type          | Description                             |
| ---------------------- | ------------- | --------------------------------------- |
| `name`                 | String        | Internal name of the trigger type       |
| `display_name`         | String        | Human-readable name for display         |
| `description`          | String        | Description of what the trigger does    |
| `supported_events`     | Array[String] | List of supported event types           |
| `configuration_schema` | Object        | JSON schema for trigger configuration   |
| `sample_event_data`    | Object        | Sample event data for this trigger type |

## Usage Examples

### Basic Request

```bash
curl -X GET "http://localhost:8000/api/v1/triggers/types"
```

### Using with JavaScript/Fetch

```javascript
async function getTriggerTypes() {
  try {
    const response = await fetch("http://localhost:8000/api/v1/triggers/types");
    const data = await response.json();

    console.log("Available trigger types:", data.trigger_types);
    return data;
  } catch (error) {
    console.error("Error fetching trigger types:", error);
  }
}
```

### Using with Python/httpx

```python
import httpx
import asyncio

async def get_trigger_types():
    async with httpx.AsyncClient() as client:
        response = await client.get('http://localhost:8000/api/v1/triggers/types')
        return response.json()

# Usage
data = asyncio.run(get_trigger_types())
print(f"Found {data['total_types']} trigger types")
```

## Integration Guide

### 1. Discovering Available Triggers

Use this endpoint to dynamically discover what trigger types are available in your system:

```python
async def discover_triggers():
    response = await get_trigger_types()

    for trigger_type in response['trigger_types']:
        print(f"Trigger: {trigger_type['display_name']}")
        print(f"Events: {', '.join(trigger_type['supported_events'])}")
        print(f"Description: {trigger_type['description']}")
        print("---")
```

### 2. Understanding Configuration Requirements

Each trigger type includes a JSON schema that describes the required configuration:

```python
def get_configuration_requirements(trigger_name):
    response = get_trigger_types()

    for trigger_type in response['trigger_types']:
        if trigger_type['name'] == trigger_name:
            schema = trigger_type['configuration_schema']
            required_fields = schema.get('required', [])
            properties = schema.get('properties', {})

            print(f"Required fields for {trigger_name}:")
            for field in required_fields:
                field_info = properties.get(field, {})
                print(f"  - {field}: {field_info.get('description', 'No description')}")

            return schema

    return None
```

### 3. Using Sample Event Data

The sample event data helps you understand the structure of events that will be sent to your workflows:

```python
def analyze_event_structure(trigger_name):
    response = get_trigger_types()

    for trigger_type in response['trigger_types']:
        if trigger_type['name'] == trigger_name:
            sample_event = trigger_type['sample_event_data']

            print(f"Sample event fields for {trigger_name}:")
            for key, value in sample_event.items():
                print(f"  - {key}: {type(value).__name__}")
                if isinstance(value, dict):
                    for nested_key in value.keys():
                        print(f"    - {key}.{nested_key}")

            return sample_event

    return None
```

## Field Mapping Integration

For Google Calendar triggers, you can use the sample event data to understand available fields for field mapping:

```python
def get_available_fields(sample_event, prefix=""):
    """Recursively extract all available field paths from sample event."""
    fields = []

    for key, value in sample_event.items():
        field_path = f"{prefix}.{key}" if prefix else key
        fields.append(field_path)

        if isinstance(value, dict):
            nested_fields = get_available_fields(value, field_path)
            fields.extend(nested_fields)
        elif isinstance(value, list) and value and isinstance(value[0], dict):
            # For arrays of objects, show the structure of the first item
            nested_fields = get_available_fields(value[0], f"{field_path}[0]")
            fields.extend(nested_fields)

    return fields

# Usage
response = get_trigger_types()
google_calendar = next(t for t in response['trigger_types'] if t['name'] == 'google_calendar')
available_fields = get_available_fields(google_calendar['sample_event_data'])

print("Available fields for mapping:")
for field in sorted(available_fields):
    print(f"  - {field}")
```

## Future Extensibility

This endpoint is designed to be extensible. As new trigger types are added to the system, they will automatically appear in the response. Each new trigger type should include:

1. **Unique name**: Internal identifier for the trigger type
2. **Display name**: Human-readable name for UI display
3. **Description**: Clear explanation of the trigger's purpose
4. **Supported events**: List of event types the trigger can handle
5. **Configuration schema**: JSON schema defining required configuration
6. **Sample event data**: Representative event data structure

## Testing

Use the provided test script to verify the endpoint functionality:

```bash
python test_trigger_types_endpoint.py
```

This will validate:

- Endpoint accessibility (no authentication required)
- Response structure and data types
- Required fields presence
- Sample event data validity
- Configuration schema completeness

## Notes

- This endpoint is public and does not require authentication
- The response includes complete sample event data to aid in integration
- Configuration schemas follow JSON Schema specification
- Field mapping is supported for Google Calendar triggers using dot notation
- The endpoint is designed to be forward-compatible with new trigger types
