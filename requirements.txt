# Core dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database
sqlalchemy[asyncio]==2.0.23
asyncpg==0.29.0
alembic==1.13.1

# Redis for task queue and distributed locking
redis[hiredis]==5.0.1
aioredis==2.0.1

# HTTP client
httpx==0.25.2
aiohttp==3.9.1

# Authentication
google-auth==2.25.2
google-auth-oauthlib==1.2.0
google-auth-httplib2==0.2.0

# Google APIs
google-api-python-client==2.110.0
google-calendar==0.8.0

# Scheduling and time handling
croniter==2.0.1
python-dateutil==2.8.2
pytz==2023.3

# Logging and monitoring
structlog==23.2.0
prometheus-client==0.19.0

# Configuration
python-dotenv==1.0.0
pyyaml==6.0.1

# Utilities
click==8.1.7
rich==13.7.0

# Development dependencies
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Production server
gunicorn==21.2.0

# Security
cryptography==41.0.8
