[Unit]
Description=Trigger Scheduler Service
Documentation=https://github.com/your-org/trigger-service
After=network.target redis.service postgresql.service
Wants=redis.service postgresql.service
Requires=network.target

[Service]
Type=simple
User=scheduler
Group=scheduler
WorkingDirectory=/opt/trigger-service
Environment=PYTHONPATH=/opt/trigger-service
Environment=REDIS_URL=redis://localhost:6379
Environment=DATABASE_URL=postgresql://scheduler:password@localhost/trigger_db
Environment=LOG_LEVEL=INFO

# Main service command
ExecStart=/opt/trigger-service/venv/bin/python scripts/run_scheduler.py \
    --redis-url=${REDIS_URL} \
    --max-schedulers=20 \
    --batch-size=100 \
    --worker-concurrency=10 \
    --cycle-interval=30 \
    --log-level=${LOG_LEVEL} \
    --metrics-port=8080 \
    --health-port=8081 \
    --pid-file=/var/run/scheduler-service/scheduler.pid

# Process management
ExecReload=/bin/kill -HUP $MAINPID
ExecStop=/bin/kill -TERM $MAINPID
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30
Restart=always
RestartSec=10

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/scheduler-service /var/run/scheduler-service /tmp

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096
MemoryMax=2G
CPUQuota=200%

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=scheduler-service

# PID file
PIDFile=/var/run/scheduler-service/scheduler.pid
RuntimeDirectory=scheduler-service
RuntimeDirectoryMode=0755

[Install]
WantedBy=multi-user.target